(()=>{var e={};e.id=404,e.ids=[404],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3676:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(482),a=s(9108),i=s(2563),n=s.n(i),o=s(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1808)),"/Users/<USER>/Downloads/astrology/src/app/blog/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/blog/page.tsx"],p="/blog/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6172:(e,t,s)=>{Promise.resolve().then(s.bind(s,805))},805:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(5344);s(3729);var a=s(6506),i=s(7292),n=s(1902);let o=()=>(0,r.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,r.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[r.jsx("div",{className:"absolute top-10 left-10",children:r.jsx(n.ht,{size:100,className:"text-saffron-400 om-pulse"})}),r.jsx("div",{className:"absolute bottom-10 right-10",children:r.jsx(n.pO,{size:120,className:"text-lotus-pink-400"})})]}),r.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,r.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[r.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Spiritual Insights"}),r.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Explore articles on astrology, spirituality, and ancient wisdom"})]})})]}),r.jsx("section",{className:"py-16 bg-white",children:r.jsx("div",{className:"spiritual-container",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{title:"Understanding Your Birth Chart: A Beginner's Guide",excerpt:"Learn how to read and interpret your birth chart with this comprehensive guide to Vedic astrology basics.",date:"2024-01-15",category:"Astrology Basics",readTime:"5 min read"},{title:"The Power of Mantras in Daily Life",excerpt:"Discover how ancient mantras can transform your spiritual practice and bring peace to your daily routine.",date:"2024-01-12",category:"Spirituality",readTime:"7 min read"},{title:"Gemstones and Their Healing Properties",excerpt:"Explore the mystical world of gemstones and learn how they can enhance your spiritual energy.",date:"2024-01-10",category:"Gemstones",readTime:"6 min read"}].map((e,t)=>(0,r.jsxs)(i.E.article,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"spiritual-card-enhanced",children:[r.jsx("div",{className:"mb-4",children:r.jsx("span",{className:"text-xs bg-saffron-100 text-saffron-700 px-2 py-1 rounded",children:e.category})}),r.jsx("h2",{className:"text-xl font-serif font-bold mb-3",children:e.title}),r.jsx("p",{className:"text-gray-600 mb-4",children:e.excerpt}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-sm text-gray-500 mb-4",children:[r.jsx("span",{children:e.date}),r.jsx("span",{children:e.readTime})]}),r.jsx(a.default,{href:`/blog/${e.title.toLowerCase().replace(/\s+/g,"-")}`,className:"spiritual-button text-sm py-2 px-4",children:"Read More"})]},e.title))})})})]})},1808:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/blog/page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[651,713],()=>s(3676));module.exports=r})();