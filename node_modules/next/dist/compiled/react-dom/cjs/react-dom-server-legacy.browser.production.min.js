/*
 React
 react-dom-server-legacy.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var ba=require("next/dist/compiled/react"),ca=require("react-dom");function q(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function fa(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var v=Object.assign,y=Object.prototype.hasOwnProperty,ka=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),la={},ma={};
function za(a){if(y.call(ma,a))return!0;if(y.call(la,a))return!1;if(ka.test(a))return ma[a]=!0;la[a]=!0;return!1}
var Aa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ba=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ca=/["'&<>]/;
function z(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ca.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Da=/([A-Z])/g,Ea=/^ms-/,Ka=Array.isArray,La=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ma={pending:!1,data:null,method:null,action:null},Na=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,kb={prefetchDNS:Oa,preconnect:Pa,preload:Qa,preloadModule:Ra,preinitStyle:hb,preinitScript:ib,preinitModuleScript:jb},C=[],lb=/(<\/|<)(s)(cript)/gi;function mb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function nb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function H(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function ob(a,b,c){switch(b){case "noscript":return H(2,null,a.tagScope|1);case "select":return H(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return H(3,null,a.tagScope);case "picture":return H(2,null,a.tagScope|2);case "math":return H(4,null,a.tagScope);case "foreignObject":return H(2,null,a.tagScope);case "table":return H(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return H(6,null,a.tagScope);case "colgroup":return H(8,null,a.tagScope);case "tr":return H(7,null,a.tagScope)}return 5<=
a.insertionMode?H(2,null,a.tagScope):0===a.insertionMode?"html"===b?H(1,null,a.tagScope):H(2,null,a.tagScope):1===a.insertionMode?H(2,null,a.tagScope):a}var pb=new Map;
function qb(a,b){if("object"!==typeof b)throw Error(q(62));var c=!0,d;for(d in b)if(y.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=z(d);e=z((""+e).trim())}else f=pb.get(d),void 0===f&&(f=z(d.replace(Da,"-$1").toLowerCase().replace(Ea,"-ms-")),pb.set(d,f)),e="number"===typeof e?0===e||Aa.has(d)?""+e:e+"px":z((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}
function rb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function K(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',z(c),'"')}function sb(a){var b=a.nextFormID++;return a.idPrefix+b}var Cb=z("javascript:throw new Error('A React form was unexpectedly submitted.')");function Db(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error(q(480));K(this,"name",b);K(this,"value",a);this.push("/>")}
function Eb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=sb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Cb,'"'),g=f=e=d=h=null,Fb(b,c)));null!=h&&L(a,"name",h);null!=d&&L(a,"formAction",d);null!=e&&L(a,"formEncType",e);null!=f&&L(a,"formMethod",f);null!=g&&L(a,"formTarget",g);return k}
function L(a,b,c){switch(b){case "className":K(a,"class",c);break;case "tabIndex":K(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":K(a,b,c);break;case "style":qb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',z(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":rb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',z(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',z(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',z(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',z(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',z(c),'"');break;case "xlinkActuate":K(a,"xlink:actuate",
c);break;case "xlinkArcrole":K(a,"xlink:arcrole",c);break;case "xlinkRole":K(a,"xlink:role",c);break;case "xlinkShow":K(a,"xlink:show",c);break;case "xlinkTitle":K(a,"xlink:title",c);break;case "xlinkType":K(a,"xlink:type",c);break;case "xmlBase":K(a,"xml:base",c);break;case "xmlLang":K(a,"xml:lang",c);break;case "xmlSpace":K(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ba.get(b)||b,za(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',z(c),'"')}}}function O(a,b,c){if(null!=b){if(null!=c)throw Error(q(60));if("object"!==typeof b||!("__html"in b))throw Error(q(61));b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}function Gb(a){var b="";ba.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Fb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Hb(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return P(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return P(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:z(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:v({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Ib(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return P(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return P(d.preconnectChunks,b);case "preload":return P(d.preloadChunks,
b);default:return P(d.hoistableChunks,b)}}function P(a,b){a.push(Q("link"));for(var c in b)if(y.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error(q(399,"link"));default:L(a,c,d)}}a.push("/>");return null}function Jb(a,b,c){a.push(Q(c));for(var d in b)if(y.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(q(399,c));default:L(a,d,e)}}a.push("/>");return null}
function Kb(a,b){a.push(Q("title"));var c=null,d=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:L(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(z(""+b));O(a,d,c);a.push(Lb("title"));return null}
function Mb(a,b){a.push(Q("script"));var c=null,d=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:L(a,e,f)}}a.push(">");O(a,d,c);"string"===typeof c&&a.push(z(c));a.push(Lb("script"));return null}
function Nb(a,b,c){a.push(Q(c));var d=c=null,e;for(e in b)if(y.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:L(a,e,f)}}a.push(">");O(a,d,c);return"string"===typeof c?(a.push(z(c)),null):c}var Ob=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Pb=new Map;function Q(a){var b=Pb.get(a);if(void 0===b){if(!Ob.test(a))throw Error(q(65,a));b="<"+a;Pb.set(a,b)}return b}
function Qb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(Q("select"));var h=null,k=null,l;for(l in c)if(y.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:L(a,l,n)}}a.push(">");O(a,k,h);return h;case "option":var m=f.selectedValue;a.push(Q("option"));var p=null,r=null,D=null,A=null,B;for(B in c)if(y.call(c,
B)){var u=c[B];if(null!=u)switch(B){case "children":p=u;break;case "selected":D=u;break;case "dangerouslySetInnerHTML":A=u;break;case "value":r=u;default:L(a,B,u)}}if(null!=m){var t=null!==r?""+r:Gb(p);if(Ka(m))for(var E=0;E<m.length;E++){if(""+m[E]===t){a.push(' selected=""');break}}else""+m===t&&a.push(' selected=""')}else D&&a.push(' selected=""');a.push(">");O(a,A,p);return p;case "textarea":a.push(Q("textarea"));var x=null,w=null,F=null,R;for(R in c)if(y.call(c,R)){var G=c[R];if(null!=G)switch(R){case "children":F=
G;break;case "value":x=G;break;case "defaultValue":w=G;break;case "dangerouslySetInnerHTML":throw Error(q(91));default:L(a,R,G)}}null===x&&null!==w&&(x=w);a.push(">");if(null!=F){if(null!=x)throw Error(q(92));if(Ka(F)){if(1<F.length)throw Error(q(93));x=""+F[0]}x=""+F}"string"===typeof x&&"\n"===x[0]&&a.push("\n");null!==x&&a.push(z(""+x));return null;case "input":a.push(Q("input"));var W=null,na=null,ha=null,M=null,I=null,Z=null,Sa=null,Ta=null,Ua=null,oa;for(oa in c)if(y.call(c,oa)){var T=c[oa];
if(null!=T)switch(oa){case "children":case "dangerouslySetInnerHTML":throw Error(q(399,"input"));case "name":W=T;break;case "formAction":na=T;break;case "formEncType":ha=T;break;case "formMethod":M=T;break;case "formTarget":I=T;break;case "defaultChecked":Ua=T;break;case "defaultValue":Sa=T;break;case "checked":Ta=T;break;case "value":Z=T;break;default:L(a,oa,T)}}var tb=Eb(a,d,e,na,ha,M,I,W);null!==Ta?rb(a,"checked",Ta):null!==Ua&&rb(a,"checked",Ua);null!==Z?L(a,"value",Z):null!==Sa&&L(a,"value",
Sa);a.push("/>");null!==tb&&tb.forEach(Db,a);return null;case "button":a.push(Q("button"));var pa=null,qa=null,da=null,ra=null,sa=null,Va=null,ta=null,Wa;for(Wa in c)if(y.call(c,Wa)){var ea=c[Wa];if(null!=ea)switch(Wa){case "children":pa=ea;break;case "dangerouslySetInnerHTML":qa=ea;break;case "name":da=ea;break;case "formAction":ra=ea;break;case "formEncType":sa=ea;break;case "formMethod":Va=ea;break;case "formTarget":ta=ea;break;default:L(a,Wa,ea)}}var Rc=Eb(a,d,e,ra,sa,Va,ta,da);a.push(">");null!==
Rc&&Rc.forEach(Db,a);O(a,qa,pa);if("string"===typeof pa){a.push(z(pa));var Sc=null}else Sc=pa;return Sc;case "form":a.push(Q("form"));var Xa=null,Tc=null,ia=null,Ya=null,Za=null,$a=null,ab;for(ab in c)if(y.call(c,ab)){var ja=c[ab];if(null!=ja)switch(ab){case "children":Xa=ja;break;case "dangerouslySetInnerHTML":Tc=ja;break;case "action":ia=ja;break;case "encType":Ya=ja;break;case "method":Za=ja;break;case "target":$a=ja;break;default:L(a,ab,ja)}}var Zb=null,$b=null;if("function"===typeof ia)if("function"===
typeof ia.$$FORM_ACTION){var ze=sb(d),Fa=ia.$$FORM_ACTION(ze);ia=Fa.action||"";Ya=Fa.encType;Za=Fa.method;$a=Fa.target;Zb=Fa.data;$b=Fa.name}else a.push(" ","action",'="',Cb,'"'),$a=Za=Ya=ia=null,Fb(d,e);null!=ia&&L(a,"action",ia);null!=Ya&&L(a,"encType",Ya);null!=Za&&L(a,"method",Za);null!=$a&&L(a,"target",$a);a.push(">");null!==$b&&(a.push('<input type="hidden"'),K(a,"name",$b),a.push("/>"),null!==Zb&&Zb.forEach(Db,a));O(a,Tc,Xa);if("string"===typeof Xa){a.push(z(Xa));var Uc=null}else Uc=Xa;return Uc;
case "menuitem":a.push(Q("menuitem"));for(var ub in c)if(y.call(c,ub)){var Vc=c[ub];if(null!=Vc)switch(ub){case "children":case "dangerouslySetInnerHTML":throw Error(q(400));default:L(a,ub,Vc)}}a.push(">");return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Wc=Kb(a,c);else Kb(e.hoistableChunks,c),Wc=null;return Wc;case "link":return Hb(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var ac=c.async;if("string"!==typeof c.src||!c.src||!ac||"function"===typeof ac||
"symbol"===typeof ac||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Xc=Mb(a,c);else{var vb=c.src;if("module"===c.type){var wb=d.moduleScriptResources;var Yc=e.preloads.moduleScripts}else wb=d.scriptResources,Yc=e.preloads.scripts;var xb=wb.hasOwnProperty(vb)?wb[vb]:void 0;if(null!==xb){wb[vb]=null;var bc=c;if(xb){2===xb.length&&(bc=v({},c),Ib(bc,xb));var Zc=Yc.get(vb);Zc&&(Zc.length=0)}var $c=[];e.scripts.add($c);Mb($c,bc)}g&&a.push("\x3c!-- --\x3e");Xc=null}return Xc;
case "style":var yb=c.precedence,ua=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof yb||"string"!==typeof ua||""===ua){a.push(Q("style"));var Ga=null,ad=null,bb;for(bb in c)if(y.call(c,bb)){var zb=c[bb];if(null!=zb)switch(bb){case "children":Ga=zb;break;case "dangerouslySetInnerHTML":ad=zb;break;default:L(a,bb,zb)}}a.push(">");var cb=Array.isArray(Ga)?2>Ga.length?Ga[0]:null:Ga;"function"!==typeof cb&&"symbol"!==typeof cb&&null!==cb&&void 0!==cb&&a.push(z(""+cb));O(a,
ad,Ga);a.push(Lb("style"));var bd=null}else{var va=e.styles.get(yb);if(null!==(d.styleResources.hasOwnProperty(ua)?d.styleResources[ua]:void 0)){d.styleResources[ua]=null;va?va.hrefs.push(z(ua)):(va={precedence:z(yb),rules:[],hrefs:[z(ua)],sheets:new Map},e.styles.set(yb,va));var cd=va.rules,Ha=null,dd=null,Ab;for(Ab in c)if(y.call(c,Ab)){var cc=c[Ab];if(null!=cc)switch(Ab){case "children":Ha=cc;break;case "dangerouslySetInnerHTML":dd=cc}}var db=Array.isArray(Ha)?2>Ha.length?Ha[0]:null:Ha;"function"!==
typeof db&&"symbol"!==typeof db&&null!==db&&void 0!==db&&cd.push(z(""+db));O(cd,dd,Ha)}va&&e.boundaryResources&&e.boundaryResources.styles.add(va);g&&a.push("\x3c!-- --\x3e");bd=void 0}return bd;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var ed=Jb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),ed="string"===typeof c.charSet?Jb(e.charsetChunks,c,"meta"):"viewport"===c.name?Jb(e.preconnectChunks,c,"meta"):Jb(e.hoistableChunks,c,"meta");return ed;case "listing":case "pre":a.push(Q(b));
var eb=null,fb=null,gb;for(gb in c)if(y.call(c,gb)){var Bb=c[gb];if(null!=Bb)switch(gb){case "children":eb=Bb;break;case "dangerouslySetInnerHTML":fb=Bb;break;default:L(a,gb,Bb)}}a.push(">");if(null!=fb){if(null!=eb)throw Error(q(60));if("object"!==typeof fb||!("__html"in fb))throw Error(q(61));var wa=fb.__html;null!==wa&&void 0!==wa&&("string"===typeof wa&&0<wa.length&&"\n"===wa[0]?a.push("\n",wa):a.push(""+wa))}"string"===typeof eb&&"\n"===eb[0]&&a.push("\n");return eb;case "img":var N=c.src,J=
c.srcSet;if(!("lazy"===c.loading||!N&&!J||"string"!==typeof N&&null!=N||"string"!==typeof J&&null!=J)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof J||":"!==J[4]||"d"!==J[0]&&"D"!==J[0]||"a"!==J[1]&&"A"!==J[1]||"t"!==J[2]&&"T"!==J[2]||"a"!==J[3]&&"A"!==J[3])){var fd="string"===typeof c.sizes?c.sizes:void 0,Ia=J?J+"\n"+(fd||""):N,dc=e.preloads.images,
xa=dc.get(Ia);if(xa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)dc.delete(Ia),e.highImagePreloads.add(xa)}else if(!d.imageResources.hasOwnProperty(Ia)){d.imageResources[Ia]=C;var ec=c.crossOrigin;var gd="string"===typeof ec?"use-credentials"===ec?ec:"":void 0;var aa=e.headers,fc;aa&&0<aa.remainingCapacity&&("high"===c.fetchPriority||500>aa.highImagePreloads.length)&&(fc=Rb(N,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:gd,integrity:c.integrity,nonce:c.nonce,type:c.type,
fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(aa.remainingCapacity-=fc.length))?(e.resets.image[Ia]=C,aa.highImagePreloads&&(aa.highImagePreloads+=", "),aa.highImagePreloads+=fc):(xa=[],P(xa,{rel:"preload",as:"image",href:J?void 0:N,imageSrcSet:J,imageSizes:fd,crossOrigin:gd,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(xa):(e.bulkPreloads.add(xa),
dc.set(Ia,xa)))}}return Jb(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Jb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var hd=Nb(e.headChunks,c,"head")}else hd=Nb(a,c,"head");return hd;case "html":if(0===
f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var id=Nb(e.htmlChunks,c,"html")}else id=Nb(a,c,"html");return id;default:if(-1!==b.indexOf("-")){a.push(Q(b));var gc=null,jd=null,Ja;for(Ja in c)if(y.call(c,Ja)){var ya=c[Ja];if(null!=ya){var Ae=Ja;switch(Ja){case "children":gc=ya;break;case "dangerouslySetInnerHTML":jd=ya;break;case "style":qb(a,ya);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:za(Ja)&&"function"!==typeof ya&&"symbol"!==typeof ya&&
a.push(" ",Ae,'="',z(ya),'"')}}}a.push(">");O(a,jd,gc);return gc}}return Nb(a,c,b)}var Sb=new Map;function Lb(a){var b=Sb.get(a);void 0===b&&(b="</"+a+">",Sb.set(a,b));return b}function Tb(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}function Ub(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error(q(395));a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Vb(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error(q(397));}}
function Wb(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error(q(397));}}var Xb=/[<\u2028\u2029]/g;
function Yb(a){return JSON.stringify(a).replace(Xb,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var hc=/[&><\u2028\u2029]/g;
function ic(a){return JSON.stringify(a).replace(hc,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var jc=!1,kc=!0;
function lc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);kc=this.push("</style>");jc=!0;b.length=0;c.length=0}}function mc(a){return 2!==a.state?jc=!0:!1}function nc(a,b,c){jc=!1;kc=!0;b.styles.forEach(lc,a);b.stylesheets.forEach(mc);jc&&(c.stylesToHoist=!0);return kc}
function S(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var oc=[];function pc(a){P(oc,a.props);for(var b=0;b<oc.length;b++)this.push(oc[b]);oc.length=0;a.state=2}
function qc(a){var b=0<a.sheets.size;a.sheets.forEach(pc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function rc(a){if(0===a.state){a.state=1;var b=a.props;P(oc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<oc.length;a++)this.push(oc[a]);oc.length=0}}function sc(a){a.sheets.forEach(rc,this);a.sheets.clear()}
function tc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=ic(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=ic(""+d.props.href);a.push(g);e=""+e;a.push(",");e=ic(e);a.push(e);for(var h in f)if(y.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(q(399,"link"));default:a:{e=
a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=ic(k);e.push(k);e.push(",");g=ic(g);e.push(g)}}a.push("]");
c=",[";d.state=3}});a.push("]")}
function uc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=z(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=z(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=z(JSON.stringify(e));a.push(e);for(var h in f)if(y.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error(q(399,"link"));
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=z(JSON.stringify(k));e.push(k);
e.push(",");g=z(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function Oa(a){var b=U?U:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(vc,wc)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],P(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}xc(b)}}}
function Pa(a,b){var c=U?U:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(vc,wc)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(yc,zc);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],P(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}xc(c)}}}
function Qa(a,b,c){var d=U?U:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var l=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(l))return;e.imageResources[l]=C;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Rb(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[l]=C,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],P(e,v({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(l,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];P(g,v({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?C:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
P(g,v({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?C:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=C;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(l=Rb(a,b,c),2<=(e.remainingCapacity-=l.length)))f.resets.font[a]=C,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=l;else switch(e=[],
a=v({rel:"preload",href:a,as:b},c),P(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}xc(d)}}}
function Ra(a,b){var c=U?U:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?C:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=C}P(f,v({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);xc(c)}}}
function hb(a,b,c){var d=U?U:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:z(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:v({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Ib(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),xc(d))}}}
function ib(a,b){var c=U?U:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=v({src:a,async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),xc(c))}}}
function jb(a,b){var c=U?U:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=v({src:a,type:"module",async:!0},b),f&&(2===f.length&&Ib(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Mb(a,b),xc(c))}}}function Ib(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Rb(a,b,c){a=(""+a).replace(vc,wc);b=(""+b).replace(yc,zc);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)y.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(yc,zc)+'"'));return b}var vc=/[<>\r\n]/g;
function wc(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var yc=/["';,\r\n]/g;
function zc(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Ac(a){this.styles.add(a)}function Bc(a){this.stylesheets.add(a)}
function Cc(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(lb,mb),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,l=new Set,n=new Set,m=new Map,p=new Set,r=new Set,D=new Set,A={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var B=0;B<f.length;B++){var u=f[B],t,E=void 0,x=void 0,w={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof u?w.href=t=u:(w.href=t=u.src,w.integrity=x="string"===typeof u.integrity?u.integrity:void 0,w.crossOrigin=E="string"===typeof u||null==u.crossOrigin?void 0:"use-credentials"===u.crossOrigin?"use-credentials":"");u=a;var F=t;u.scriptResources[F]=null;u.moduleScriptResources[F]=null;u=[];P(u,w);p.add(u);d.push('<script src="',z(t));"string"===typeof x&&d.push('" integrity="',z(x));"string"===typeof E&&d.push('" crossorigin="',z(E));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)w=
g[f],E=t=void 0,x={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof w?x.href=B=w:(x.href=B=w.src,x.integrity=E="string"===typeof w.integrity?w.integrity:void 0,x.crossOrigin=t="string"===typeof w||null==w.crossOrigin?void 0:"use-credentials"===w.crossOrigin?"use-credentials":""),w=a,u=B,w.scriptResources[u]=null,w.moduleScriptResources[u]=null,w=[],P(w,x),p.add(w),d.push('<script type="module" src="',z(B)),"string"===typeof E&&d.push('" integrity="',z(E)),"string"===typeof t&&
d.push('" crossorigin="',z(t)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:l,highImagePreloads:n,styles:m,
bootstrapScripts:p,scripts:r,bulkPreloads:D,preloads:A,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function Dc(a,b,c,d){if(c.generateStaticMarkup)return a.push(z(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(z(b)),a=!0);return a}
var Ec=Symbol.for("react.element"),Fc=Symbol.for("react.portal"),Gc=Symbol.for("react.fragment"),Hc=Symbol.for("react.strict_mode"),Ic=Symbol.for("react.profiler"),Jc=Symbol.for("react.provider"),Kc=Symbol.for("react.context"),Lc=Symbol.for("react.server_context"),Mc=Symbol.for("react.forward_ref"),Nc=Symbol.for("react.suspense"),Oc=Symbol.for("react.suspense_list"),Pc=Symbol.for("react.memo"),Qc=Symbol.for("react.lazy"),kd=Symbol.for("react.scope"),ld=Symbol.for("react.debug_trace_mode"),md=Symbol.for("react.offscreen"),
nd=Symbol.for("react.legacy_hidden"),od=Symbol.for("react.cache"),pd=Symbol.for("react.default_value"),qd=Symbol.iterator,rd=Symbol.for("react.client.reference");
function sd(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===rd?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Gc:return"Fragment";case Fc:return"Portal";case Ic:return"Profiler";case Hc:return"StrictMode";case Nc:return"Suspense";case Oc:return"SuspenseList";case od:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Kc:return(a.displayName||"Context")+".Consumer";case Jc:return(a._context.displayName||"Context")+".Provider";case Mc:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Pc:return b=a.displayName||null,null!==b?b:sd(a.type)||"Memo";case Qc:b=a._payload;a=a._init;try{return sd(a(b))}catch(c){}}return null}var td;function ud(a){if(void 0===td)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);td=b&&b[1]||""}return"\n"+td+a}var vd=!1;
function wd(a,b){if(!a||vd)return"";vd=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var m=function(){throw Error();};Object.defineProperty(m.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(m,[])}catch(r){var p=r}Reflect.construct(a,[],m)}else{try{m.call()}catch(r){p=r}a.call(m.prototype)}}else{try{throw Error();}catch(r){p=r}(m=a())&&"function"===typeof m.catch&&
m.catch(function(){})}}catch(r){if(r&&p&&"string"===typeof r.stack)return[r.stack,p.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var k=g.split("\n"),l=h.split("\n");for(e=d=0;d<k.length&&!k[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<l.length&&!l[e].includes("DetermineComponentFrameRoot");)e++;if(d===k.length||e===l.length)for(d=k.length-1,e=l.length-1;1<=d&&0<=e&&k[d]!==l[e];)e--;for(;1<=d&&0<=e;d--,e--)if(k[d]!==l[e]){if(1!==d||1!==e){do if(d--,e--,0>e||k[d]!==l[e]){var n="\n"+k[d].replace(" at new "," at ");a.displayName&&n.includes("<anonymous>")&&(n=n.replace("<anonymous>",a.displayName));return n}while(1<=d&&0<=e)}break}}}finally{vd=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?ud(c):""}
var xd={};function yd(a,b){a=a.contextTypes;if(!a)return xd;var c={},d;for(d in a)c[d]=b[d];return c}var zd=null;function Ad(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error(q(401));}else{if(null===c)throw Error(q(401));Ad(a,c)}b.context._currentValue2=b.value}}function Bd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&Bd(a)}function Cd(a){var b=a.parent;null!==b&&Cd(b);a.context._currentValue2=a.value}
function Dd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error(q(402));a.depth===b.depth?Ad(a,b):Dd(a,b)}function Ed(a,b){var c=b.parent;if(null===c)throw Error(q(402));a.depth===c.depth?Ad(a,c):Ed(a,c);b.context._currentValue2=b.value}function Fd(a){var b=zd;b!==a&&(null===b?Cd(a):null===a?Bd(b):b.depth===a.depth?Ad(b,a):b.depth>a.depth?Dd(b,a):Ed(b,a),zd=a)}
var Gd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Hd(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Gd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:v({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Gd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=v({},f,h)):v(f,h))}a.state=f}else f.queue=null}
var Id={id:1,overflow:""};function Jd(a,b,c){var d=a.id;a=a.overflow;var e=32-Kd(d)-1;d&=~(1<<e);c+=1;var f=32-Kd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Kd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Kd=Math.clz32?Math.clz32:Ld,Md=Math.log,Nd=Math.LN2;function Ld(a){a>>>=0;return 0===a?32:31-(Md(a)/Nd|0)|0}var Od=Error(q(460));function Pd(){}
function Qd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Pd,Pd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Rd=b;throw Od;}}var Rd=null;
function Sd(){if(null===Rd)throw Error(q(459));var a=Rd;Rd=null;return a}function Td(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Ud="function"===typeof Object.is?Object.is:Td,Vd=null,Wd=null,Xd=null,Yd=null,Zd=null,V=null,$d=!1,ae=!1,be=0,ce=0,de=-1,ee=0,fe=null,ge=null,he=0;function ie(){if(null===Vd)throw Error(q(321));return Vd}function je(){if(0<he)throw Error(q(312));return{memoizedState:null,queue:null,next:null}}
function ke(){null===V?null===Zd?($d=!1,Zd=V=je()):($d=!0,V=Zd):null===V.next?($d=!1,V=V.next=je()):($d=!0,V=V.next);return V}function le(a,b,c,d){for(;ae;)ae=!1,ce=be=0,de=-1,ee=0,he+=1,V=null,c=a(b,d);me();return c}function ne(){var a=fe;fe=null;return a}function me(){Yd=Xd=Wd=Vd=null;ae=!1;Zd=null;he=0;V=ge=null}function oe(a,b){return"function"===typeof b?b(a):b}
function pe(a,b,c){Vd=ie();V=ke();if($d){var d=V.queue;b=d.dispatch;if(null!==ge&&(c=ge.get(d),void 0!==c)){ge.delete(d);d=V.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);V.memoizedState=d;return[d,b]}return[V.memoizedState,b]}a=a===oe?"function"===typeof b?b():b:void 0!==c?c(b):b;V.memoizedState=a;a=V.queue={last:null,dispatch:null};a=a.dispatch=qe.bind(null,Vd,a);return[V.memoizedState,a]}
function re(a,b){Vd=ie();V=ke();b=void 0===b?null:b;if(null!==V){var c=V.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Ud(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();V.memoizedState=[a,b];return a}function qe(a,b,c){if(25<=he)throw Error(q(301));if(a===Vd)if(ae=!0,a={action:c,next:null},null===ge&&(ge=new Map),c=ge.get(b),void 0===c)ge.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}
function se(){throw Error(q(394));}function te(){throw Error(q(479));}function ue(a){var b=ee;ee+=1;null===fe&&(fe=[]);return Qd(fe,a,b)}function ve(){throw Error(q(393));}function we(){}
var ye={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ue(a);if(a.$$typeof===Kc||a.$$typeof===Lc)return a._currentValue2}throw Error(q(438,String(a)));},useContext:function(a){ie();return a._currentValue2},useMemo:re,useReducer:pe,useRef:function(a){Vd=ie();V=ke();var b=V.memoizedState;return null===b?(a={current:a},V.memoizedState=a):b},useState:function(a){return pe(oe,a)},useInsertionEffect:we,useLayoutEffect:we,
useCallback:function(a,b){return re(function(){return a},b)},useImperativeHandle:we,useEffect:we,useDebugValue:we,useDeferredValue:function(a){ie();return a},useTransition:function(){ie();return[!1,se]},useId:function(){var a=Wd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Kd(a)-1)).toString(32)+b;var c=xe;if(null===c)throw Error(q(404));b=be++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error(q(407));return c()},useCacheRefresh:function(){return ve},
useHostTransitionStatus:function(){ie();return Ma},useOptimistic:function(a){ie();return[a,te]},useFormState:function(a,b,c){ie();var d=ce++,e=Xd;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Yd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+fa(JSON.stringify([g,null,d]),0),k===f&&(de=d,b=e[0]))}var l=a.bind(null,b);a=function(m){l(m)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(m){m=
l.$$FORM_ACTION(m);void 0!==c&&(c+="",m.action=c);var p=m.data;p&&(null===f&&(f=void 0!==c?"p"+c:"k"+fa(JSON.stringify([g,null,d]),0)),p.append("$ACTION_KEY",f));return m});return[b,a]}var n=a.bind(null,b);return[b,function(m){n(m)}]}},xe=null,Be={getCacheSignal:function(){throw Error(q(248));},getCacheForType:function(){throw Error(q(248));}},Ce=La.ReactCurrentDispatcher,De=La.ReactCurrentCache;function Ee(a){console.error(a);return null}function Fe(){}
function Ge(a,b,c,d,e,f,g,h,k,l,n,m){Na.current=kb;var p=[],r=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:r,pingedTasks:p,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Ee:f,onPostpone:void 0===n?Fe:n,onAllReady:void 0===g?
Fe:g,onShellReady:void 0===h?Fe:h,onShellError:void 0===k?Fe:k,onFatalError:void 0===l?Fe:l,formState:void 0===m?null:m};c=He(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Ie(b,null,a,-1,null,c,r,null,d,xd,null,Id,null);p.push(a);return b}var U=null;function Je(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Ke(a))}
function Le(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Ie(a,b,c,d,e,f,g,h,k,l,n,m,p){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var r={replay:null,node:c,childIndex:d,ping:function(){return Je(a,r)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:m,componentStack:p,thenableState:b};g.add(r);return r}
function Me(a,b,c,d,e,f,g,h,k,l,n,m,p){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var r={replay:c,node:d,childIndex:e,ping:function(){return Je(a,r)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:m,componentStack:p,thenableState:b};g.add(r);return r}
function He(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function Ne(a,b){return{tag:0,parent:a.componentStack,type:b}}
function Oe(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=ud(b.type,null,null);break;case 1:a+=wd(b.type,!1);break;case 2:a+=wd(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function X(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function Pe(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Qe(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error(q(108,sd(e)||"Unknown",h));e=v({},c,d)}b.legacyContext=e;Y(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Y(a,b,null,f,-1),b.keyPath=e}
function Re(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Jd(c,1,0),Se(a,b,d,-1),b.treeContext=c):h?Se(a,b,d,-1):Y(a,b,null,d,-1);b.keyPath=f}function Te(a,b){if(a&&a.defaultProps){b=v({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Ue(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:e};g=yd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:g);Hd(h,e,f,g);Qe(a,b,c,h,e);b.componentStack=d}else{g=yd(e,b.legacyContext);h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e};Vd={};Wd=b;Xd=a;Yd=c;ce=be=0;de=-1;ee=0;fe=d;d=e(f,g);d=le(e,f,d,g);var k=0!==
be,l=ce,n=de;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Hd(d,e,f,g),Qe(a,b,c,d,e)):Re(a,b,c,d,k,l,n);b.componentStack=h}else if("string"===typeof e){d=b.componentStack;b.componentStack=Ne(b,e);g=b.blockedSegment;if(null===g)g=f.children,h=b.formatContext,k=b.keyPath,b.formatContext=ob(h,e,f),b.keyPath=c,Se(a,b,g,-1),b.formatContext=h,b.keyPath=k;else{k=Qb(g.chunks,e,f,a.resumableState,a.renderState,b.formatContext,g.lastPushedText);g.lastPushedText=!1;h=b.formatContext;
l=b.keyPath;b.formatContext=ob(h,e,f);b.keyPath=c;Se(a,b,k,-1);b.formatContext=h;b.keyPath=l;a:{c=g.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(Lb(e))}g.lastPushedText=
!1}b.componentStack=d}else{switch(e){case nd:case ld:case Hc:case Ic:case Gc:e=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=e;return;case md:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Y(a,b,null,f.children,-1),b.keyPath=e);return;case Oc:e=b.componentStack;b.componentStack=Ne(b,"SuspenseList");d=b.keyPath;b.keyPath=c;Y(a,b,null,f.children,-1);b.keyPath=d;b.componentStack=e;return;case kd:throw Error(q(343));case Nc:a:if(null!==b.replay){e=b.keyPath;b.keyPath=c;c=f.children;try{Se(a,
b,c,-1)}finally{b.keyPath=e}}else{var m=b.componentStack;e=b.componentStack=Ne(b,"Suspense");var p=b.keyPath;d=b.blockedBoundary;var r=b.blockedSegment;g=f.fallback;var D=f.children;f=new Set;l=Le(a,f);null!==a.trackedPostpones&&(l.trackedContentKeyPath=c);n=He(a,r.chunks.length,l,b.formatContext,!1,!1);r.children.push(n);r.lastPushedText=!1;var A=He(a,0,null,b.formatContext,!1,!1);A.parentFlushed=!0;b.blockedBoundary=l;b.blockedSegment=A;a.renderState.boundaryResources=l.resources;b.keyPath=c;try{if(Se(a,
b,D,-1),a.renderState.generateStaticMarkup||A.lastPushedText&&A.textEmbedded&&A.chunks.push("\x3c!-- --\x3e"),A.status=1,Ve(l,A),0===l.pendingTasks&&0===l.status){l.status=1;b.componentStack=m;break a}}catch(B){A.status=4,l.status=4,h=Oe(a,b.componentStack),k=X(a,B,h),l.errorDigest=k,We(a,l)}finally{a.renderState.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=r,b.keyPath=p,b.componentStack=m}h=[c[0],"Suspense Fallback",c[2]];k=a.trackedPostpones;null!==k&&(m=[h[1],h[2],
[],null],k.workingMap.set(h,m),5===l.status?k.workingMap.get(c)[4]=m:l.trackedFallbackNode=m);b=Ie(a,null,g,-1,d,n,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext,e);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Mc:h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e.render};e=e.render;Vd={};Wd=b;Xd=a;Yd=c;ce=be=0;de=-1;ee=0;fe=d;d=e(f,g);f=le(e,f,d,g);Re(a,b,c,f,0!==be,ce,de);b.componentStack=h;return;case Pc:e=e.type;f=Te(e,
f);Ue(a,b,c,d,e,f,g);return;case Jc:g=f.children;d=b.keyPath;e=e._context;f=f.value;h=e._currentValue2;e._currentValue2=f;k=zd;zd=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:h,value:f};b.context=f;b.keyPath=c;Y(a,b,null,g,-1);a=zd;if(null===a)throw Error(q(403));c=a.parentValue;a.context._currentValue2=c===pd?a.context._defaultValue:c;a=zd=a.parent;b.context=a;b.keyPath=d;return;case Kc:f=f.children;f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;Y(a,b,null,f,-1);b.keyPath=e;return;
case Qc:g=b.componentStack;b.componentStack=Ne(b,"Lazy");h=e._init;e=h(e._payload);f=Te(e,f);Ue(a,b,c,d,e,f,void 0);b.componentStack=g;return}throw Error(q(130,null==e?e:typeof e,""));}}function Xe(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=He(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Se(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(Ve(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Y(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Xe(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Ec:var f=d.type,g=d.key,h=d.props,k=d.ref,l=sd(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var m=b.replay;e=m.nodes;for(d=0;d<e.length;d++){var p=e[d];if(n===p[1]){if(4===p.length){if(null!==l&&l!==p[0])throw Error(q(490,p[0],l));var r=p[2];l=p[3];p=b.node;b.replay={nodes:r,slots:l,pendingTasks:1};
try{Ue(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(q(488));b.replay.pendingTasks--}catch(w){if("object"===typeof w&&null!==w&&(w===Od||"function"===typeof w.then))throw b.node===p&&(b.replay=m),w;b.replay.pendingTasks--;h=Oe(a,b.componentStack);g=a;a=b.blockedBoundary;c=w;h=X(g,c,h);Ye(g,a,r,l,c,h)}b.replay=m}else{if(f!==Nc)throw Error(q(490,"Suspense",sd(f)||"Unknown"));b:{m=void 0;c=p[5];f=p[2];k=p[3];l=null===p[4]?[]:p[4][2];p=null===p[4]?null:p[4][3];n=b.componentStack;
var D=b.componentStack=Ne(b,"Suspense"),A=b.keyPath,B=b.replay,u=b.blockedBoundary,t=h.children;h=h.fallback;var E=new Set,x=Le(a,E);x.parentFlushed=!0;x.rootSegmentID=c;b.blockedBoundary=x;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=x.resources;try{Se(a,b,t,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(q(488));b.replay.pendingTasks--;if(0===x.pendingTasks&&0===x.status){x.status=1;a.completedBoundaries.push(x);break b}}catch(w){x.status=4,r=Oe(a,
b.componentStack),m=X(a,w,r),x.errorDigest=m,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(x)}finally{a.renderState.boundaryResources=u?u.resources:null,b.blockedBoundary=u,b.replay=B,b.keyPath=A,b.componentStack=n}b=Me(a,null,{nodes:l,slots:p,pendingTasks:0},h,-1,u,E,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,D);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Ue(a,b,g,c,f,h,k);return;case Fc:throw Error(q(257));case Qc:h=b.componentStack;
b.componentStack=Ne(b,"Lazy");g=d._init;d=g(d._payload);b.componentStack=h;Y(a,b,null,d,e);return}if(Ka(d)){Ze(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=qd&&d[qd]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);Ze(a,b,g,e)}return}if("function"===typeof d.then)return Y(a,b,null,ue(d),e);if(d.$$typeof===Kc||d.$$typeof===Lc)return Y(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);
throw Error(q(31,"[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e));}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Dc(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Dc(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function Ze(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{Ze(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error(q(488));b.replay.pendingTasks--}catch(m){if("object"===typeof m&&null!==m&&(m===Od||"function"===typeof m.then))throw m;b.replay.pendingTasks--;c=Oe(a,b.componentStack);var l=b.blockedBoundary,
n=m;c=X(a,n,c);Ye(a,l,d,k,n,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=Jd(f,g,d),l=h[d],"number"===typeof l?(Xe(a,b,l,k,d),delete h[d]):Se(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Jd(f,g,h),Se(a,b,d,h);b.treeContext=f;b.keyPath=e}
function We(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function Se(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.componentStack,n=b.blockedSegment;if(null===n)try{return Y(a,b,null,c,d)}catch(r){if(me(),c=r===Od?Sd():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=ne();a=Me(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=l;Fd(g);return}}else{var m=n.children.length,p=n.chunks.length;try{return Y(a,b,null,c,d)}catch(r){if(me(),n.children.length=m,n.chunks.length=p,c=r===Od?Sd():r,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=ne();n=b.blockedSegment;m=He(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(m);n.lastPushedText=!1;a=Ie(a,d,b.node,b.childIndex,b.blockedBoundary,m,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,
b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=l;Fd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Fd(g);throw c;}function $e(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,af(this,b,a))}
function Ye(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Ye(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=Le(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error(q(487));4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var m in d)delete d[m]}}
function bf(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){X(b,c,d);Pe(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=X(b,c,d),Ye(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&cf(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=Oe(b,a.componentStack),a=X(b,c,a),d.errorDigest=a,We(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return bf(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&df(b)}
function ef(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),l=k.next();0<e.remainingCapacity&&!l.done;l=k.next()){var n=l.value,m=n.props,p=m.href,r=n.props,D=Rb(r.href,"style",{crossOrigin:r.crossOrigin,integrity:r.integrity,
nonce:r.nonce,type:r.type,fetchPriority:r.fetchPriority,referrerPolicy:r.referrerPolicy,media:r.media});if(2<=(e.remainingCapacity-=D.length))c.resets.style[p]=C,f&&(f+=", "),f+=D,c.resets.style[p]="string"===typeof m.crossOrigin||"string"===typeof m.integrity?[m.crossOrigin,m.integrity]:C;else break b}}f?d({Link:f}):d({})}}}catch(A){X(a,A,{})}}function cf(a){null===a.trackedPostpones&&ef(a,!0);a.onShellError=Fe;a=a.onShellReady;a()}
function df(a){ef(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function Ve(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&Ve(a,c)}else a.completedSegments.push(b)}
function af(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error(q(389));a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&cf(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&Ve(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach($e,a),b.fallbackAbortableTasks.clear())):null!==c&&c.parentFlushed&&1===c.status&&
(Ve(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&df(a)}
function Ke(a){if(2!==a.status){var b=zd,c=Ce.current;Ce.current=ye;var d=De.current;De.current=Be;var e=U;U=a;var f=xe;xe=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var m=k.blockedSegment;if(null===m){var p=l;if(0!==k.replay.pendingTasks){Fd(k.context);try{var r=k.thenableState;k.thenableState=null;Y(p,k,r,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error(q(488));
k.replay.pendingTasks--;k.abortSet.delete(k);af(p,k.blockedBoundary,null)}catch(I){me();var D=I===Od?Sd():I;if("object"===typeof D&&null!==D&&"function"===typeof D.then){var A=k.ping;D.then(A,A);k.thenableState=ne()}else{k.replay.pendingTasks--;k.abortSet.delete(k);var B=Oe(p,k.componentStack);l=void 0;var u=p,t=k.blockedBoundary,E=D,x=k.replay.nodes,w=k.replay.slots;l=X(u,E,B);Ye(u,t,x,w,E,l);p.pendingRootTasks--;0===p.pendingRootTasks&&cf(p);p.allPendingTasks--;0===p.allPendingTasks&&df(p)}}finally{p.renderState.boundaryResources=
null}}}else if(p=void 0,u=m,0===u.status){Fd(k.context);var F=u.children.length,R=u.chunks.length;try{var G=k.thenableState;k.thenableState=null;Y(l,k,G,k.node,k.childIndex);l.renderState.generateStaticMarkup||u.lastPushedText&&u.textEmbedded&&u.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);u.status=1;af(l,k.blockedBoundary,u)}catch(I){me();u.children.length=F;u.chunks.length=R;var W=I===Od?Sd():I;if("object"===typeof W&&null!==W&&"function"===typeof W.then){var na=k.ping;W.then(na,na);k.thenableState=
ne()}else{var ha=Oe(l,k.componentStack);k.abortSet.delete(k);u.status=4;var M=k.blockedBoundary;p=X(l,W,ha);null===M?Pe(l,W):(M.pendingTasks--,4!==M.status&&(M.status=4,M.errorDigest=p,We(l,M),M.parentFlushed&&l.clientRenderedBoundaries.push(M)));l.allPendingTasks--;0===l.allPendingTasks&&df(l)}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&ff(a,a.destination)}catch(I){X(a,I,{}),Pe(a,I)}finally{xe=f,Ce.current=c,De.current=d,c===ye&&Fd(b),U=e}}}
function gf(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=hf(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error(q(390));
}}
function hf(a,b,c){var d=c.boundary;if(null===d)return gf(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=z(d),b.push(d),b.push('"')),b.push("></template>")),gf(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Ub(b,a.renderState,
d.rootSegmentID),gf(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Ub(b,a.renderState,d.rootSegmentID),gf(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(Ac,e),c.stylesheets.forEach(Bc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error(q(391));hf(a,b,c[0]);a=a.renderState.generateStaticMarkup?
!0:b.push("\x3c!--/$--\x3e");return a}function jf(a,b,c){Vb(b,a.renderState,c.parentFormatContext,c.id);hf(a,b,c);return Wb(b,c.parentFormatContext)}
function kf(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)lf(a,b,c,d[e]);d.length=0;nc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),tc(b,c)):(b.push('" data-sty="'),uc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Tb(b,a)&&d}
function lf(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error(q(392));return jf(a,b,d)}if(e===c.rootSegmentID)return jf(a,b,d);jf(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function ff(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var m=Q("head");b.push(m);
b.push(">")}}else if(n)for(f=0;f<n.length;f++)b.push(n[f]);var p=e.charsetChunks;for(f=0;f<p.length;f++)b.push(p[f]);p.length=0;e.preconnects.forEach(S,b);e.preconnects.clear();var r=e.preconnectChunks;for(f=0;f<r.length;f++)b.push(r[f]);r.length=0;e.fontPreloads.forEach(S,b);e.fontPreloads.clear();e.highImagePreloads.forEach(S,b);e.highImagePreloads.clear();e.styles.forEach(qc,b);var D=e.importMapChunks;for(f=0;f<D.length;f++)b.push(D[f]);D.length=0;e.bootstrapScripts.forEach(S,b);e.scripts.forEach(S,
b);e.scripts.clear();e.bulkPreloads.forEach(S,b);e.bulkPreloads.clear();var A=e.preloadChunks;for(f=0;f<A.length;f++)b.push(A[f]);A.length=0;var B=e.hoistableChunks;for(f=0;f<B.length;f++)b.push(B[f]);B.length=0;if(l&&null===n){var u=Lb("head");b.push(u)}hf(a,b,d);a.completedRootSegment=null;Tb(b,a.renderState)}else return;var t=a.renderState;d=0;t.preconnects.forEach(S,b);t.preconnects.clear();var E=t.preconnectChunks;for(d=0;d<E.length;d++)b.push(E[d]);E.length=0;t.fontPreloads.forEach(S,b);t.fontPreloads.clear();
t.highImagePreloads.forEach(S,b);t.highImagePreloads.clear();t.styles.forEach(sc,b);t.scripts.forEach(S,b);t.scripts.clear();t.bulkPreloads.forEach(S,b);t.bulkPreloads.clear();var x=t.preloadChunks;for(d=0;d<x.length;d++)b.push(x[d]);x.length=0;var w=t.hoistableChunks;for(d=0;d<w.length;d++)b.push(w[d]);w.length=0;var F=a.clientRenderedBoundaries;for(c=0;c<F.length;c++){var R=F[c];t=b;var G=a.resumableState,W=a.renderState,na=R.rootSegmentID,ha=R.errorDigest,M=R.errorMessage,I=R.errorComponentStack,
Z=0===G.streamingFormat;Z?(t.push(W.startInlineScript),0===(G.instructions&4)?(G.instructions|=4,t.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):t.push('$RX("')):t.push('<template data-rxi="" data-bid="');t.push(W.boundaryPrefix);var Sa=na.toString(16);t.push(Sa);Z&&t.push('"');if(ha||M||I)if(Z){t.push(",");var Ta=Yb(ha||"");t.push(Ta)}else{t.push('" data-dgst="');
var Ua=z(ha||"");t.push(Ua)}if(M||I)if(Z){t.push(",");var oa=Yb(M||"");t.push(oa)}else{t.push('" data-msg="');var T=z(M||"");t.push(T)}if(I)if(Z){t.push(",");var tb=Yb(I);t.push(tb)}else{t.push('" data-stck="');var pa=z(I);t.push(pa)}if(Z?!t.push(")\x3c/script>"):!t.push('"></template>')){a.destination=null;c++;F.splice(0,c);return}}F.splice(0,c);var qa=a.completedBoundaries;for(c=0;c<qa.length;c++)if(!kf(a,b,qa[c])){a.destination=null;c++;qa.splice(0,c);return}qa.splice(0,c);var da=a.partialBoundaries;
for(c=0;c<da.length;c++){var ra=da[c];a:{F=a;R=b;F.renderState.boundaryResources=ra.resources;var sa=ra.completedSegments;for(G=0;G<sa.length;G++)if(!lf(F,R,ra,sa[G])){G++;sa.splice(0,G);var Va=!1;break a}sa.splice(0,G);Va=nc(R,ra.resources,F.renderState)}if(!Va){a.destination=null;c++;da.splice(0,c);return}}da.splice(0,c);var ta=a.completedBoundaries;for(c=0;c<ta.length;c++)if(!kf(a,b,ta[c])){a.destination=null;c++;ta.splice(0,c);return}ta.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(da=Lb("body"),b.push(da)),c.hasHtml&&(c=Lb("html"),b.push(c)),b.push(null),a.destination=null)}}function xc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?ff(a,b):a.flushScheduled=!1}}
function mf(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{ff(a,b)}catch(c){X(a,c,{}),Pe(a,c)}}}function nf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error(q(432)):b;c.forEach(function(e){return bf(e,a,d)});c.clear()}null!==a.destination&&ff(a,a.destination)}catch(e){X(a,e,{}),Pe(a,e)}}function of(){}
function pf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=nb(b?b.identifierPrefix:void 0,void 0);a=Ge(a,b,Cc(b,c),H(0,null,0),Infinity,of,void 0,function(){h=!0},void 0,void 0,void 0);a.flushScheduled=null!==a.destination;Ke(a);null===a.trackedPostpones&&ef(a,0===a.pendingRootTasks);nf(a,d);mf(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error(q(426));return g}exports.renderToNodeStream=function(){throw Error(q(207));};
exports.renderToStaticMarkup=function(a,b){return pf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};exports.renderToStaticNodeStream=function(){throw Error(q(208));};exports.renderToString=function(a,b){return pf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-60a927d04-20240113";

//# sourceMappingURL=react-dom-server-legacy.browser.production.min.js.map
