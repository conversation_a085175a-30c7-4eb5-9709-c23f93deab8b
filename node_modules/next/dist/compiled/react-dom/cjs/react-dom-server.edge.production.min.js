/*
 React
 react-dom-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react"),ca=require("react-dom");
function da(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}var l=null,n=0;
function r(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=new Uint8Array(512),n=0),a.enqueue(b);else{var c=l.length-n;c<b.byteLength&&(0===c?a.enqueue(l):(l.set(b.subarray(0,c),n),a.enqueue(l),b=b.subarray(c)),l=new Uint8Array(512),n=0);l.set(b,n);n+=b.byteLength}}function w(a,b){r(a,b);return!0}function la(a){l&&0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=null,n=0)}var ma=new TextEncoder;function z(a){return ma.encode(a)}
function B(a){return ma.encode(a)}function pa(a,b){"function"===typeof a.error?a.error(b):a.close()}
var C=Object.assign,E=Object.prototype.hasOwnProperty,qa=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),wa={},xa={};
function ya(a){if(E.call(xa,a))return!0;if(E.call(wa,a))return!1;if(qa.test(a))return xa[a]=!0;wa[a]=!0;return!1}
var Ea=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Fa=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ga=/["'&<>]/;
function H(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ga.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ha=/([A-Z])/g,Ia=/^ms-/,Ja=Array.isArray,Ka=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,La={pending:!1,data:null,method:null,action:null},Sa=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,$a={prefetchDNS:Ta,preconnect:Ua,preload:Va,preloadModule:Wa,preinitStyle:Xa,preinitScript:Ya,preinitModuleScript:Za},K=[],ab=B('"></template>'),bb=B("<script>"),pb=B("\x3c/script>"),qb=B('<script src="'),rb=B('<script type="module" src="'),sb=B('" nonce="'),tb=B('" integrity="'),
ub=B('" crossorigin="'),vb=B('" async="">\x3c/script>'),wb=/(<\/|<)(s)(cript)/gi;function xb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var yb=B('<script type="importmap">'),zb=B("\x3c/script>");
function Ab(a,b,c,d,e,f){var g=void 0===b?bb:B('<script nonce="'+H(b)+'">'),h=a.idPrefix,k=[],m=null,p=a.bootstrapScriptContent,q=a.bootstrapScripts,t=a.bootstrapModules;void 0!==p&&k.push(g,z((""+p).replace(wb,xb)),pb);void 0!==c&&("string"===typeof c?(m={src:c,chunks:[]},Jb(m.chunks,{src:c,async:!0,integrity:void 0,nonce:b})):(m={src:c.src,chunks:[]},Jb(m.chunks,{src:c.src,async:!0,integrity:c.integrity,nonce:b})));c=[];void 0!==d&&(c.push(yb),c.push(z((""+JSON.stringify(d)).replace(wb,xb))),c.push(zb));
d=e?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof f?f:2E3}:null;e={placeholderPrefix:B(h+"P:"),segmentPrefix:B(h+"S:"),boundaryPrefix:B(h+"B:"),startInlineScript:g,htmlChunks:null,headChunks:null,externalRuntimeScript:m,bootstrapChunks:k,onHeaders:e,headers:d,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:c,preloadChunks:[],hoistableChunks:[],preconnects:new Set,
fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:b,boundaryResources:null,stylesToHoist:!1};if(void 0!==q)for(g=0;g<q.length;g++)c=q[g],d=m=void 0,f={rel:"preload",as:"script",fetchPriority:"low",nonce:b},"string"===typeof c?f.href=h=c:(f.href=h=c.src,f.integrity=d="string"===typeof c.integrity?c.integrity:void 0,f.crossOrigin=m="string"===
typeof c||null==c.crossOrigin?void 0:"use-credentials"===c.crossOrigin?"use-credentials":""),c=a,p=h,c.scriptResources[p]=null,c.moduleScriptResources[p]=null,c=[],N(c,f),e.bootstrapScripts.add(c),k.push(qb,z(H(h))),b&&k.push(sb,z(H(b))),"string"===typeof d&&k.push(tb,z(H(d))),"string"===typeof m&&k.push(ub,z(H(m))),k.push(vb);if(void 0!==t)for(q=0;q<t.length;q++)f=t[q],m=h=void 0,d={rel:"modulepreload",fetchPriority:"low",nonce:b},"string"===typeof f?d.href=g=f:(d.href=g=f.src,d.integrity=m="string"===
typeof f.integrity?f.integrity:void 0,d.crossOrigin=h="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=a,c=g,f.scriptResources[c]=null,f.moduleScriptResources[c]=null,f=[],N(f,d),e.bootstrapScripts.add(f),k.push(rb,z(H(g))),b&&k.push(sb,z(H(b))),"string"===typeof m&&k.push(tb,z(H(m))),"string"===typeof h&&k.push(ub,z(H(h))),k.push(vb);return e}
function Kb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function O(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Lb(a){return O("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Mb(a,b,c){switch(b){case "noscript":return O(2,null,a.tagScope|1);case "select":return O(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return O(3,null,a.tagScope);case "picture":return O(2,null,a.tagScope|2);case "math":return O(4,null,a.tagScope);case "foreignObject":return O(2,null,a.tagScope);case "table":return O(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return O(6,null,a.tagScope);case "colgroup":return O(8,null,a.tagScope);case "tr":return O(7,null,a.tagScope)}return 5<=
a.insertionMode?O(2,null,a.tagScope):0===a.insertionMode?"html"===b?O(1,null,a.tagScope):O(2,null,a.tagScope):1===a.insertionMode?O(2,null,a.tagScope):a}var Nb=B("\x3c!-- --\x3e");function Ob(a,b,c,d){if(""===b)return d;d&&a.push(Nb);a.push(z(H(b)));return!0}var Pb=new Map,Qb=B(' style="'),Rb=B(":"),Sb=B(";");
function Tb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(E.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=z(H(d));e=z(H((""+e).trim()))}else f=Pb.get(d),void 0===f&&(f=B(H(d.replace(Ha,"-$1").toLowerCase().replace(Ia,"-ms-"))),Pb.set(d,f)),e="number"===typeof e?0===e||Ea.has(d)?z(""+
e):z(e+"px"):z(H((""+e).trim()));c?(c=!1,a.push(Qb,f,Rb,e)):a.push(Sb,f,Rb,e)}}c||a.push(P)}var Q=B(" "),Ub=B('="'),P=B('"'),Vb=B('=""');function Wb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Vb)}function R(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(Q,z(b),Ub,z(H(c)),P)}function Xb(a){var b=a.nextFormID++;return a.idPrefix+b}var Yb=B(H("javascript:throw new Error('A React form was unexpectedly submitted.')")),Zb=B('<input type="hidden"');
function $b(a,b){this.push(Zb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");R(this,"name",b);R(this,"value",a);this.push(ac)}
function bc(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Xb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(Q,z("formAction"),Ub,Yb,P),g=f=e=d=h=null,cc(b,c)));null!=h&&S(a,"name",h);null!=d&&S(a,"formAction",d);null!=e&&S(a,"formEncType",e);null!=f&&S(a,"formMethod",f);null!=g&&S(a,"formTarget",g);return k}
function S(a,b,c){switch(b){case "className":R(a,"class",c);break;case "tabIndex":R(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":R(a,b,c);break;case "style":Tb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,z(b),Ub,z(H(c)),P);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Wb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,z("xlink:href"),Ub,z(H(c)),P);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Ub,z(H(c)),P);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Vb);break;case "capture":case "download":!0===c?a.push(Q,z(b),Vb):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,z(b),Ub,z(H(c)),P);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(Q,z(b),Ub,z(H(c)),P);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(Q,z(b),Ub,z(H(c)),P);break;case "xlinkActuate":R(a,"xlink:actuate",
c);break;case "xlinkArcrole":R(a,"xlink:arcrole",c);break;case "xlinkRole":R(a,"xlink:role",c);break;case "xlinkShow":R(a,"xlink:show",c);break;case "xlinkTitle":R(a,"xlink:title",c);break;case "xlinkType":R(a,"xlink:type",c);break;case "xmlBase":R(a,"xml:base",c);break;case "xmlLang":R(a,"xml:lang",c);break;case "xmlSpace":R(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Fa.get(b)||b,ya(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(Q,z(b),Ub,z(H(c)),P)}}}var T=B(">"),ac=B("/>");
function dc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(z(""+b))}}function ec(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var fc=B(' selected=""'),gc=B('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function cc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,gc,pb))}var hc=B("\x3c!--F!--\x3e"),ic=B("\x3c!--F--\x3e");
function jc(a,b,c,d,e,f,g){var h=b.rel,k=b.href,m=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return N(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof m||null!=b.disabled||b.onLoad||b.onError)return N(a,b);f=d.styles.get(m);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:z(H(m)),rules:[],hrefs:[],sheets:new Map},d.styles.set(m,f)),b={state:0,props:C({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&kc(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push(Nb);return null}if(b.onLoad||b.onError)return N(a,b);e&&a.push(Nb);switch(b.rel){case "preconnect":case "dns-prefetch":return N(d.preconnectChunks,b);case "preload":return N(d.preloadChunks,b);default:return N(d.hoistableChunks,
b)}}function N(a,b){a.push(U("link"));for(var c in b)if(E.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:S(a,c,d)}}a.push(ac);return null}
function lc(a,b,c){a.push(U(c));for(var d in b)if(E.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:S(a,d,e)}}a.push(ac);return null}
function mc(a,b){a.push(U("title"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(T);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(z(H(""+b)));dc(a,d,c);a.push(wc("title"));return null}
function Jb(a,b){a.push(U("script"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(T);dc(a,d,c);"string"===typeof c&&a.push(z(H(c)));a.push(wc("script"));return null}
function xc(a,b,c){a.push(U(c));var d=c=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:S(a,e,f)}}a.push(T);dc(a,d,c);return"string"===typeof c?(a.push(z(H(c))),null):c}var yc=B("\n"),zc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Ac=new Map;function U(a){var b=Ac.get(a);if(void 0===b){if(!zc.test(a))throw Error("Invalid tag: "+a);b=B("<"+a);Ac.set(a,b)}return b}var Bc=B("<!DOCTYPE html>");
function Cc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(U("select"));var h=null,k=null,m;for(m in c)if(E.call(c,m)){var p=c[m];if(null!=p)switch(m){case "children":h=p;break;case "dangerouslySetInnerHTML":k=p;break;case "defaultValue":case "value":break;default:S(a,m,p)}}a.push(T);dc(a,k,h);return h;case "option":var q=f.selectedValue;a.push(U("option"));var t=null,u=null,A=null,D=null,v;for(v in c)if(E.call(c,
v)){var y=c[v];if(null!=y)switch(v){case "children":t=y;break;case "selected":A=y;break;case "dangerouslySetInnerHTML":D=y;break;case "value":u=y;default:S(a,v,y)}}if(null!=q){var ea=null!==u?""+u:ec(t);if(Ja(q))for(var V=0;V<q.length;V++){if(""+q[V]===ea){a.push(fc);break}}else""+q===ea&&a.push(fc)}else A&&a.push(fc);a.push(T);dc(a,D,t);return t;case "textarea":a.push(U("textarea"));var x=null,F=null,G=null,fa;for(fa in c)if(E.call(c,fa)){var na=c[fa];if(null!=na)switch(fa){case "children":G=na;
break;case "value":x=na;break;case "defaultValue":F=na;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:S(a,fa,na)}}null===x&&null!==F&&(x=F);a.push(T);if(null!=G){if(null!=x)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ja(G)){if(1<G.length)throw Error("<textarea> can only have at most one child.");x=""+G[0]}x=""+G}"string"===typeof x&&"\n"===x[0]&&a.push(yc);null!==x&&a.push(z(H(""+x)));
return null;case "input":a.push(U("input"));var W=null,ha=null,ia=null,I=null,L=null,ra=null,sa=null,ta=null,Ma=null,ja;for(ja in c)if(E.call(c,ja)){var ba=c[ja];if(null!=ba)switch(ja){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":W=ba;break;case "formAction":ha=ba;break;case "formEncType":ia=ba;break;case "formMethod":I=ba;break;case "formTarget":L=ba;break;case "defaultChecked":Ma=
ba;break;case "defaultValue":sa=ba;break;case "checked":ta=ba;break;case "value":ra=ba;break;default:S(a,ja,ba)}}var wd=bc(a,d,e,ha,ia,I,L,W);null!==ta?Wb(a,"checked",ta):null!==Ma&&Wb(a,"checked",Ma);null!==ra?S(a,"value",ra):null!==sa&&S(a,"value",sa);a.push(ac);null!==wd&&wd.forEach($b,a);return null;case "button":a.push(U("button"));var cb=null,xd=null,yd=null,zd=null,Ad=null,Bd=null,Cd=null,db;for(db in c)if(E.call(c,db)){var oa=c[db];if(null!=oa)switch(db){case "children":cb=oa;break;case "dangerouslySetInnerHTML":xd=
oa;break;case "name":yd=oa;break;case "formAction":zd=oa;break;case "formEncType":Ad=oa;break;case "formMethod":Bd=oa;break;case "formTarget":Cd=oa;break;default:S(a,db,oa)}}var Dd=bc(a,d,e,zd,Ad,Bd,Cd,yd);a.push(T);null!==Dd&&Dd.forEach($b,a);dc(a,xd,cb);if("string"===typeof cb){a.push(z(H(cb)));var Ed=null}else Ed=cb;return Ed;case "form":a.push(U("form"));var eb=null,Fd=null,ua=null,fb=null,gb=null,hb=null,ib;for(ib in c)if(E.call(c,ib)){var va=c[ib];if(null!=va)switch(ib){case "children":eb=va;
break;case "dangerouslySetInnerHTML":Fd=va;break;case "action":ua=va;break;case "encType":fb=va;break;case "method":gb=va;break;case "target":hb=va;break;default:S(a,ib,va)}}var nc=null,oc=null;if("function"===typeof ua)if("function"===typeof ua.$$FORM_ACTION){var wf=Xb(d),Na=ua.$$FORM_ACTION(wf);ua=Na.action||"";fb=Na.encType;gb=Na.method;hb=Na.target;nc=Na.data;oc=Na.name}else a.push(Q,z("action"),Ub,Yb,P),hb=gb=fb=ua=null,cc(d,e);null!=ua&&S(a,"action",ua);null!=fb&&S(a,"encType",fb);null!=gb&&
S(a,"method",gb);null!=hb&&S(a,"target",hb);a.push(T);null!==oc&&(a.push(Zb),R(a,"name",oc),a.push(ac),null!==nc&&nc.forEach($b,a));dc(a,Fd,eb);if("string"===typeof eb){a.push(z(H(eb)));var Gd=null}else Gd=eb;return Gd;case "menuitem":a.push(U("menuitem"));for(var Bb in c)if(E.call(c,Bb)){var Hd=c[Bb];if(null!=Hd)switch(Bb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:S(a,Bb,Hd)}}a.push(T);return null;case "title":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var Id=mc(a,c);else mc(e.hoistableChunks,c),Id=null;return Id;case "link":return jc(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var pc=c.async;if("string"!==typeof c.src||!c.src||!pc||"function"===typeof pc||"symbol"===typeof pc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Jd=Jb(a,c);else{var Cb=c.src;if("module"===c.type){var Db=d.moduleScriptResources;var Kd=e.preloads.moduleScripts}else Db=d.scriptResources,
Kd=e.preloads.scripts;var Eb=Db.hasOwnProperty(Cb)?Db[Cb]:void 0;if(null!==Eb){Db[Cb]=null;var qc=c;if(Eb){2===Eb.length&&(qc=C({},c),kc(qc,Eb));var Ld=Kd.get(Cb);Ld&&(Ld.length=0)}var Md=[];e.scripts.add(Md);Jb(Md,qc)}g&&a.push(Nb);Jd=null}return Jd;case "style":var Fb=c.precedence,za=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Fb||"string"!==typeof za||""===za){a.push(U("style"));var Oa=null,Nd=null,jb;for(jb in c)if(E.call(c,jb)){var Gb=c[jb];if(null!=Gb)switch(jb){case "children":Oa=
Gb;break;case "dangerouslySetInnerHTML":Nd=Gb;break;default:S(a,jb,Gb)}}a.push(T);var kb=Array.isArray(Oa)?2>Oa.length?Oa[0]:null:Oa;"function"!==typeof kb&&"symbol"!==typeof kb&&null!==kb&&void 0!==kb&&a.push(z(H(""+kb)));dc(a,Nd,Oa);a.push(wc("style"));var Od=null}else{var Aa=e.styles.get(Fb);if(null!==(d.styleResources.hasOwnProperty(za)?d.styleResources[za]:void 0)){d.styleResources[za]=null;Aa?Aa.hrefs.push(z(H(za))):(Aa={precedence:z(H(Fb)),rules:[],hrefs:[z(H(za))],sheets:new Map},e.styles.set(Fb,
Aa));var Pd=Aa.rules,Pa=null,Qd=null,Hb;for(Hb in c)if(E.call(c,Hb)){var rc=c[Hb];if(null!=rc)switch(Hb){case "children":Pa=rc;break;case "dangerouslySetInnerHTML":Qd=rc}}var lb=Array.isArray(Pa)?2>Pa.length?Pa[0]:null:Pa;"function"!==typeof lb&&"symbol"!==typeof lb&&null!==lb&&void 0!==lb&&Pd.push(z(H(""+lb)));dc(Pd,Qd,Pa)}Aa&&e.boundaryResources&&e.boundaryResources.styles.add(Aa);g&&a.push(Nb);Od=void 0}return Od;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Rd=lc(a,c,
"meta");else g&&a.push(Nb),Rd="string"===typeof c.charSet?lc(e.charsetChunks,c,"meta"):"viewport"===c.name?lc(e.preconnectChunks,c,"meta"):lc(e.hoistableChunks,c,"meta");return Rd;case "listing":case "pre":a.push(U(b));var mb=null,nb=null,ob;for(ob in c)if(E.call(c,ob)){var Ib=c[ob];if(null!=Ib)switch(ob){case "children":mb=Ib;break;case "dangerouslySetInnerHTML":nb=Ib;break;default:S(a,ob,Ib)}}a.push(T);if(null!=nb){if(null!=mb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof nb||!("__html"in nb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var Ba=nb.__html;null!==Ba&&void 0!==Ba&&("string"===typeof Ba&&0<Ba.length&&"\n"===Ba[0]?a.push(yc,z(Ba)):a.push(z(""+Ba)))}"string"===typeof mb&&"\n"===mb[0]&&a.push(yc);return mb;case "img":var M=c.src,J=c.srcSet;if(!("lazy"===c.loading||!M&&!J||"string"!==typeof M&&null!=M||"string"!==
typeof J&&null!=J)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof M||":"!==M[4]||"d"!==M[0]&&"D"!==M[0]||"a"!==M[1]&&"A"!==M[1]||"t"!==M[2]&&"T"!==M[2]||"a"!==M[3]&&"A"!==M[3])&&("string"!==typeof J||":"!==J[4]||"d"!==J[0]&&"D"!==J[0]||"a"!==J[1]&&"A"!==J[1]||"t"!==J[2]&&"T"!==J[2]||"a"!==J[3]&&"A"!==J[3])){var Sd="string"===typeof c.sizes?c.sizes:void 0,Qa=J?J+"\n"+(Sd||""):M,sc=e.preloads.images,Ca=sc.get(Qa);if(Ca){if("high"===c.fetchPriority||10>e.highImagePreloads.size)sc.delete(Qa),
e.highImagePreloads.add(Ca)}else if(!d.imageResources.hasOwnProperty(Qa)){d.imageResources[Qa]=K;var tc=c.crossOrigin;var Td="string"===typeof tc?"use-credentials"===tc?tc:"":void 0;var ka=e.headers,uc;ka&&0<ka.remainingCapacity&&("high"===c.fetchPriority||500>ka.highImagePreloads.length)&&(uc=Dc(M,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Td,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(ka.remainingCapacity-=
uc.length))?(e.resets.image[Qa]=K,ka.highImagePreloads&&(ka.highImagePreloads+=", "),ka.highImagePreloads+=uc):(Ca=[],N(Ca,{rel:"preload",as:"image",href:J?void 0:M,imageSrcSet:J,imageSizes:Sd,crossOrigin:Td,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ca):(e.bulkPreloads.add(Ca),sc.set(Qa,Ca)))}}return lc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return lc(a,
c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Ud=xc(e.headChunks,c,"head")}else Ud=xc(a,c,"head");return Ud;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[Bc];var Vd=xc(e.htmlChunks,c,"html")}else Vd=xc(a,c,"html");return Vd;default:if(-1!==b.indexOf("-")){a.push(U(b));
var vc=null,Wd=null,Ra;for(Ra in c)if(E.call(c,Ra)){var Da=c[Ra];if(null!=Da){var xf=Ra;switch(Ra){case "children":vc=Da;break;case "dangerouslySetInnerHTML":Wd=Da;break;case "style":Tb(a,Da);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:ya(Ra)&&"function"!==typeof Da&&"symbol"!==typeof Da&&a.push(Q,z(xf),Ub,z(H(Da)),P)}}}a.push(T);dc(a,Wd,vc);return vc}}return xc(a,c,b)}var Ec=new Map;
function wc(a){var b=Ec.get(a);void 0===b&&(b=B("</"+a+">"),Ec.set(a,b));return b}function Fc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)r(a,b[c]);return c<b.length?(c=b[c],b.length=0,w(a,c)):!0}var Gc=B('<template id="'),Hc=B('"></template>'),Ic=B("\x3c!--$--\x3e"),Jc=B('\x3c!--$?--\x3e<template id="'),Kc=B('"></template>'),Lc=B("\x3c!--$!--\x3e"),Mc=B("\x3c!--/$--\x3e"),Nc=B("<template"),Oc=B('"'),Pc=B(' data-dgst="');B(' data-msg="');B(' data-stck="');var Qc=B("></template>");
function Rc(a,b,c){r(a,Jc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");r(a,b.boundaryPrefix);r(a,z(c.toString(16)));return w(a,Kc)}
var Sc=B('<div hidden id="'),Tc=B('">'),Uc=B("</div>"),Vc=B('<svg aria-hidden="true" style="display:none" id="'),Wc=B('">'),Xc=B("</svg>"),Yc=B('<math aria-hidden="true" style="display:none" id="'),Zc=B('">'),$c=B("</math>"),ad=B('<table hidden id="'),bd=B('">'),cd=B("</table>"),dd=B('<table hidden><tbody id="'),ed=B('">'),fd=B("</tbody></table>"),gd=B('<table hidden><tr id="'),hd=B('">'),id=B("</tr></table>"),jd=B('<table hidden><colgroup id="'),kd=B('">'),ld=B("</colgroup></table>");
function md(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return r(a,Sc),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,Tc);case 3:return r(a,Vc),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,Wc);case 4:return r(a,Yc),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,Zc);case 5:return r(a,ad),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,bd);case 6:return r(a,dd),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,ed);case 7:return r(a,gd),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,hd);
case 8:return r(a,jd),r(a,b.segmentPrefix),r(a,z(d.toString(16))),w(a,kd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function nd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return w(a,Uc);case 3:return w(a,Xc);case 4:return w(a,$c);case 5:return w(a,cd);case 6:return w(a,fd);case 7:return w(a,id);case 8:return w(a,ld);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var od=B('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),pd=B('$RS("'),qd=B('","'),rd=B('")\x3c/script>'),sd=B('<template data-rsi="" data-sid="'),td=B('" data-pid="'),ud=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
vd=B('$RC("'),Xd=B('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Yd=B('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Zd=B('$RR("'),$d=B('","'),ae=B('",'),be=B('"'),ce=B(")\x3c/script>"),de=B('<template data-rci="" data-bid="'),ee=B('<template data-rri="" data-bid="'),fe=B('" data-sid="'),ge=B('" data-sty="'),he=B('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),ie=B('$RX("'),je=B('"'),ke=B(","),le=B(")\x3c/script>"),me=B('<template data-rxi="" data-bid="'),ne=B('" data-dgst="'),
oe=B('" data-msg="'),pe=B('" data-stck="'),qe=/[<\u2028\u2029]/g;function re(a){return JSON.stringify(a).replace(qe,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var se=/[&><\u2028\u2029]/g;
function te(a){return JSON.stringify(a).replace(se,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ue=B('<style media="not all" data-precedence="'),ve=B('" data-href="'),we=B('">'),xe=B("</style>"),ye=!1,ze=!0;function Ae(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){r(this,ue);r(this,a.precedence);for(r(this,ve);d<c.length-1;d++)r(this,c[d]),r(this,Be);r(this,c[d]);r(this,we);for(d=0;d<b.length;d++)r(this,b[d]);ze=w(this,xe);ye=!0;b.length=0;c.length=0}}function Ce(a){return 2!==a.state?ye=!0:!1}
function De(a,b,c){ye=!1;ze=!0;b.styles.forEach(Ae,a);b.stylesheets.forEach(Ce);ye&&(c.stylesToHoist=!0);return ze}function Ee(a){for(var b=0;b<a.length;b++)r(this,a[b]);a.length=0}var Fe=[];function Ge(a){N(Fe,a.props);for(var b=0;b<Fe.length;b++)r(this,Fe[b]);Fe.length=0;a.state=2}var He=B('<style data-precedence="'),Ie=B('" data-href="'),Be=B(" "),Je=B('">'),Ke=B("</style>");
function Le(a){var b=0<a.sheets.size;a.sheets.forEach(Ge,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){r(this,He);r(this,a.precedence);a=0;if(d.length){for(r(this,Ie);a<d.length-1;a++)r(this,d[a]),r(this,Be);r(this,d[a])}r(this,Je);for(a=0;a<c.length;a++)r(this,c[a]);r(this,Ke);c.length=0;d.length=0}}
function Me(a){if(0===a.state){a.state=1;var b=a.props;N(Fe,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Fe.length;a++)r(this,Fe[a]);Fe.length=0}}function Ne(a){a.sheets.forEach(Me,this);a.sheets.clear()}var Oe=B("["),Pe=B(",["),Qe=B(","),Re=B("]");
function Se(a,b){r(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)r(a,c),r(a,z(te(""+d.props.href))),r(a,Re),c=Pe;else{r(a,c);var e=d.props["data-precedence"],f=d.props;r(a,z(te(""+d.props.href)));e=""+e;r(a,Qe);r(a,z(te(e)));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!ya(g))break a;h=""+h}r(e,Qe);r(e,z(te(k)));r(e,Qe);r(e,z(te(h)))}}}r(a,
Re);c=Pe;d.state=3}});r(a,Re)}
function Te(a,b){r(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)r(a,c),r(a,z(H(JSON.stringify(""+d.props.href)))),r(a,Re),c=Pe;else{r(a,c);var e=d.props["data-precedence"],f=d.props;r(a,z(H(JSON.stringify(""+d.props.href))));e=""+e;r(a,Qe);r(a,z(H(JSON.stringify(e))));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var k=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!ya(g))break a;h=""+h}r(e,Qe);r(e,z(H(JSON.stringify(k))));r(e,Qe);r(e,z(H(JSON.stringify(h))))}}}r(a,
Re);c=Pe;d.state=3}});r(a,Re)}function Ta(a){var b=Ue();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Ve,We)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],N(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Xe(b)}}}
function Ua(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Ve,We)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(Ye,Ze);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],N(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Xe(c)}}}
function Va(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var m=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(m))return;e.imageResources[m]=K;e=f.headers;var p;e&&0<e.remainingCapacity&&"high"===k&&(p=Dc(a,b,c),2<=(e.remainingCapacity-=p.length))?(f.resets.image[m]=K,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=p):(e=[],N(e,C({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(m,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];N(g,C({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?K:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
N(g,C({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?K:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=K;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(m=Dc(a,b,c),2<=(e.remainingCapacity-=m.length)))f.resets.font[a]=K,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=m;else switch(e=[],
a=C({rel:"preload",href:a,as:b},c),N(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Xe(d)}}}
function Wa(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?K:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=K}N(f,C({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Xe(c)}}}
function Xa(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:z(H(b)),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:C({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&kc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Xe(d))}}}
function Ya(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=C({src:a,async:!0},b),f&&(2===f.length&&kc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Jb(a,b),Xe(c))}}}
function Za(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=C({src:a,type:"module",async:!0},b),f&&(2===f.length&&kc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Jb(a,b),Xe(c))}}}function kc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Dc(a,b,c){a=(""+a).replace(Ve,We);b=(""+b).replace(Ye,Ze);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)E.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ye,Ze)+'"'));return b}var Ve=/[<>\r\n]/g;
function We(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ye=/["';,\r\n]/g;
function Ze(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function $e(a){this.styles.add(a)}function af(a){this.stylesheets.add(a)}
var bf="function"===typeof AsyncLocalStorage,cf=bf?new AsyncLocalStorage:null,df=Symbol.for("react.element"),ef=Symbol.for("react.portal"),ff=Symbol.for("react.fragment"),gf=Symbol.for("react.strict_mode"),hf=Symbol.for("react.profiler"),jf=Symbol.for("react.provider"),kf=Symbol.for("react.context"),lf=Symbol.for("react.server_context"),mf=Symbol.for("react.forward_ref"),nf=Symbol.for("react.suspense"),of=Symbol.for("react.suspense_list"),pf=Symbol.for("react.memo"),qf=Symbol.for("react.lazy"),rf=
Symbol.for("react.scope"),sf=Symbol.for("react.debug_trace_mode"),tf=Symbol.for("react.offscreen"),uf=Symbol.for("react.legacy_hidden"),vf=Symbol.for("react.cache"),yf=Symbol.for("react.default_value"),zf=Symbol.iterator,Af=Symbol.for("react.client.reference");
function Bf(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===Af?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ff:return"Fragment";case ef:return"Portal";case hf:return"Profiler";case gf:return"StrictMode";case nf:return"Suspense";case of:return"SuspenseList";case vf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case kf:return(a.displayName||"Context")+".Consumer";case jf:return(a._context.displayName||"Context")+".Provider";case mf:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case pf:return b=a.displayName||null,null!==b?b:Bf(a.type)||"Memo";case qf:b=a._payload;a=a._init;try{return Bf(a(b))}catch(c){}}return null}var Cf;function Df(a){if(void 0===Cf)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Cf=b&&b[1]||""}return"\n"+Cf+a}var Ef=!1;
function Ff(a,b){if(!a||Ef)return"";Ef=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var q=function(){throw Error();};Object.defineProperty(q.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(q,[])}catch(u){var t=u}Reflect.construct(a,[],q)}else{try{q.call()}catch(u){t=u}a.call(q.prototype)}}else{try{throw Error();}catch(u){t=u}(q=a())&&"function"===typeof q.catch&&
q.catch(function(){})}}catch(u){if(u&&t&&"string"===typeof u.stack)return[u.stack,t.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var k=g.split("\n"),m=h.split("\n");for(e=d=0;d<k.length&&!k[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<m.length&&!m[e].includes("DetermineComponentFrameRoot");)e++;if(d===k.length||e===m.length)for(d=k.length-1,e=m.length-1;1<=d&&0<=e&&k[d]!==m[e];)e--;for(;1<=d&&0<=e;d--,e--)if(k[d]!==m[e]){if(1!==d||1!==e){do if(d--,e--,0>e||k[d]!==m[e]){var p="\n"+k[d].replace(" at new "," at ");a.displayName&&p.includes("<anonymous>")&&(p=p.replace("<anonymous>",a.displayName));return p}while(1<=d&&0<=e)}break}}}finally{Ef=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?Df(c):""}
var Gf={};function Hf(a,b){a=a.contextTypes;if(!a)return Gf;var c={},d;for(d in a)c[d]=b[d];return c}var If=null;function Jf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Jf(a,c)}b.context._currentValue=b.value}}
function Kf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Kf(a)}function Lf(a){var b=a.parent;null!==b&&Lf(b);a.context._currentValue=a.value}function Mf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Jf(a,b):Mf(a,b)}
function Nf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Jf(a,c):Nf(a,c);b.context._currentValue=b.value}function Of(a){var b=If;b!==a&&(null===b?Lf(a):null===a?Kf(b):b.depth===a.depth?Jf(b,a):b.depth>a.depth?Mf(b,a):Nf(b,a),If=a)}
var Pf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Qf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Pf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:C({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Pf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=C({},f,h)):C(f,h))}a.state=f}else f.queue=null}
var Rf={id:1,overflow:""};function Sf(a,b,c){var d=a.id;a=a.overflow;var e=32-Tf(d)-1;d&=~(1<<e);c+=1;var f=32-Tf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Tf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Tf=Math.clz32?Math.clz32:Uf,Vf=Math.log,Wf=Math.LN2;function Uf(a){a>>>=0;return 0===a?32:31-(Vf(a)/Wf|0)|0}var Xf=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Yf(){}function Zf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Yf,Yf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}$f=b;throw Xf;}}var $f=null;
function ag(){if(null===$f)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=$f;$f=null;return a}function bg(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var cg="function"===typeof Object.is?Object.is:bg,dg=null,eg=null,fg=null,gg=null,hg=null,X=null,ig=!1,jg=!1,kg=0,lg=0,mg=-1,ng=0,og=null,pg=null,qg=0;
function rg(){if(null===dg)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return dg}
function sg(){if(0<qg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function tg(){null===X?null===hg?(ig=!1,hg=X=sg()):(ig=!0,X=hg):null===X.next?(ig=!1,X=X.next=sg()):(ig=!0,X=X.next);return X}function ug(a,b,c,d){for(;jg;)jg=!1,lg=kg=0,mg=-1,ng=0,qg+=1,X=null,c=a(b,d);vg();return c}function wg(){var a=og;og=null;return a}function vg(){gg=fg=eg=dg=null;jg=!1;hg=null;qg=0;X=pg=null}
function xg(a,b){return"function"===typeof b?b(a):b}function yg(a,b,c){dg=rg();X=tg();if(ig){var d=X.queue;b=d.dispatch;if(null!==pg&&(c=pg.get(d),void 0!==c)){pg.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===xg?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=zg.bind(null,dg,a);return[X.memoizedState,a]}
function Ag(a,b){dg=rg();X=tg();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!cg(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}
function zg(a,b,c){if(25<=qg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===dg)if(jg=!0,a={action:c,next:null},null===pg&&(pg=new Map),c=pg.get(b),void 0===c)pg.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Bg(){throw Error("startTransition cannot be called during server rendering.");}function Cg(){throw Error("Cannot update optimistic state while rendering.");}
function Dg(a){var b=ng;ng+=1;null===og&&(og=[]);return Zf(og,a,b)}function Eg(){throw Error("Cache cannot be refreshed during server rendering.");}function Fg(){}
var Hg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Dg(a);if(a.$$typeof===kf||a.$$typeof===lf)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){rg();return a._currentValue},useMemo:Ag,useReducer:yg,useRef:function(a){dg=rg();X=tg();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return yg(xg,a)},
useInsertionEffect:Fg,useLayoutEffect:Fg,useCallback:function(a,b){return Ag(function(){return a},b)},useImperativeHandle:Fg,useEffect:Fg,useDebugValue:Fg,useDeferredValue:function(a){rg();return a},useTransition:function(){rg();return[!1,Bg]},useId:function(){var a=eg.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Tf(a)-1)).toString(32)+b;var c=Gg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=kg++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Eg},useHostTransitionStatus:function(){rg();return La},useOptimistic:function(a){rg();return[a,Cg]},useFormState:function(a,b,c){rg();var d=lg++,e=fg;if("function"===typeof a.$$FORM_ACTION){var f=null,g=gg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+da(JSON.stringify([g,null,d]),0),k===f&&(mg=d,b=e[0]))}var m=a.bind(null,b);a=function(q){m(q)};"function"===typeof m.$$FORM_ACTION&&(a.$$FORM_ACTION=function(q){q=m.$$FORM_ACTION(q);void 0!==c&&(c+="",q.action=c);var t=q.data;t&&(null===f&&(f=void 0!==c?"p"+c:"k"+da(JSON.stringify([g,null,d]),0)),t.append("$ACTION_KEY",f));return q});return[b,a]}var p=a.bind(null,b);return[b,function(q){p(q)}]}},Gg=null,Ig=
{getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},Jg=Ka.ReactCurrentDispatcher,Kg=Ka.ReactCurrentCache;function Lg(a){console.error(a);return null}function Mg(){}
function Ng(a,b,c,d,e,f,g,h,k,m,p,q){Sa.current=$a;var t=[],u=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:u,pingedTasks:t,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Lg:f,onPostpone:void 0===p?Mg:p,onAllReady:void 0===g?
Mg:g,onShellReady:void 0===h?Mg:h,onShellError:void 0===k?Mg:k,onFatalError:void 0===m?Mg:m,formState:void 0===q?null:q};c=Og(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Pg(b,null,a,-1,null,c,u,null,d,Gf,null,Rf,null);t.push(a);return b}var Qg=null;function Ue(){if(Qg)return Qg;if(bf){var a=cf.getStore();if(a)return a}return null}function Rg(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return Sg(a)},0))}
function Tg(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Pg(a,b,c,d,e,f,g,h,k,m,p,q,t){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var u={replay:null,node:c,childIndex:d,ping:function(){return Rg(a,u)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:m,context:p,treeContext:q,componentStack:t,thenableState:b};g.add(u);return u}
function Ug(a,b,c,d,e,f,g,h,k,m,p,q,t){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var u={replay:c,node:d,childIndex:e,ping:function(){return Rg(a,u)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:m,context:p,treeContext:q,componentStack:t,thenableState:b};g.add(u);return u}
function Og(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function Vg(a,b){return{tag:0,parent:a.componentStack,type:b}}
function Wg(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=Df(b.type,null,null);break;case 1:a+=Ff(b.type,!1);break;case 2:a+=Ff(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function Y(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function Xg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,pa(a.destination,b)):(a.status=1,a.fatalError=b)}
function Yg(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((Bf(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=C({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Zg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var m=0;m<f;m++)m===g?k.push(hc):k.push(ic)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Sf(c,1,0),$g(a,b,d,-1),b.treeContext=c):h?$g(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function ah(a,b){if(a&&a.defaultProps){b=C({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function bh(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:e};g=Hf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:g);Qf(h,e,f,g);Yg(a,b,c,h,e);b.componentStack=d}else{g=Hf(e,b.legacyContext);h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e};dg={};eg=b;fg=a;gg=c;lg=kg=0;mg=-1;ng=0;og=d;d=e(f,g);d=ug(e,f,d,g);var k=0!==
kg,m=lg,p=mg;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Qf(d,e,f,g),Yg(a,b,c,d,e)):Zg(a,b,c,d,k,m,p);b.componentStack=h}else if("string"===typeof e){d=b.componentStack;b.componentStack=Vg(b,e);g=b.blockedSegment;if(null===g)g=f.children,h=b.formatContext,k=b.keyPath,b.formatContext=Mb(h,e,f),b.keyPath=c,$g(a,b,g,-1),b.formatContext=h,b.keyPath=k;else{k=Cc(g.chunks,e,f,a.resumableState,a.renderState,b.formatContext,g.lastPushedText);g.lastPushedText=!1;h=b.formatContext;
m=b.keyPath;b.formatContext=Mb(h,e,f);b.keyPath=c;$g(a,b,k,-1);b.formatContext=h;b.keyPath=m;a:{c=g.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(wc(e))}g.lastPushedText=
!1}b.componentStack=d}else{switch(e){case uf:case sf:case gf:case hf:case ff:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case tf:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case of:e=b.componentStack;b.componentStack=Vg(b,"SuspenseList");d=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=d;b.componentStack=e;return;case rf:throw Error("ReactDOMServer does not yet support scope components.");case nf:a:if(null!==b.replay){e=
b.keyPath;b.keyPath=c;c=f.children;try{$g(a,b,c,-1)}finally{b.keyPath=e}}else{var q=b.componentStack;e=b.componentStack=Vg(b,"Suspense");var t=b.keyPath;d=b.blockedBoundary;var u=b.blockedSegment;g=f.fallback;var A=f.children;f=new Set;m=Tg(a,f);null!==a.trackedPostpones&&(m.trackedContentKeyPath=c);p=Og(a,u.chunks.length,m,b.formatContext,!1,!1);u.children.push(p);u.lastPushedText=!1;var D=Og(a,0,null,b.formatContext,!1,!1);D.parentFlushed=!0;b.blockedBoundary=m;b.blockedSegment=D;a.renderState.boundaryResources=
m.resources;b.keyPath=c;try{if($g(a,b,A,-1),D.lastPushedText&&D.textEmbedded&&D.chunks.push(Nb),D.status=1,ch(m,D),0===m.pendingTasks&&0===m.status){m.status=1;b.componentStack=q;break a}}catch(v){D.status=4,m.status=4,h=Wg(a,b.componentStack),k=Y(a,v,h),m.errorDigest=k,dh(a,m)}finally{a.renderState.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=u,b.keyPath=t,b.componentStack=q}h=[c[0],"Suspense Fallback",c[2]];k=a.trackedPostpones;null!==k&&(q=[h[1],h[2],[],null],k.workingMap.set(h,
q),5===m.status?k.workingMap.get(c)[4]=q:m.trackedFallbackNode=q);b=Pg(a,null,g,-1,d,p,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext,e);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case mf:h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e.render};e=e.render;dg={};eg=b;fg=a;gg=c;lg=kg=0;mg=-1;ng=0;og=d;d=e(f,g);f=ug(e,f,d,g);Zg(a,b,c,f,0!==kg,lg,mg);b.componentStack=h;return;case pf:e=e.type;f=ah(e,f);bh(a,b,c,d,e,f,g);return;
case jf:g=f.children;d=b.keyPath;e=e._context;f=f.value;h=e._currentValue;e._currentValue=f;k=If;If=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:h,value:f};b.context=f;b.keyPath=c;Z(a,b,null,g,-1);a=If;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===yf?a.context._defaultValue:c;a=If=a.parent;b.context=a;b.keyPath=d;return;case kf:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,
b,null,f,-1);b.keyPath=e;return;case qf:g=b.componentStack;b.componentStack=Vg(b,"Lazy");h=e._init;e=h(e._payload);f=ah(e,f);bh(a,b,c,d,e,f,void 0);b.componentStack=g;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==e?e:typeof e)+"."));}}
function eh(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Og(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,$g(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(ch(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)eh(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case df:var f=d.type,g=d.key,h=d.props,k=d.ref,m=Bf(f),p=null==g?-1===e?0:e:g;g=[b.keyPath,m,p];if(null!==b.replay)a:{var q=b.replay;e=q.nodes;for(d=0;d<e.length;d++){var t=e[d];if(p===t[1]){if(4===t.length){if(null!==m&&m!==t[0])throw Error("Expected the resume to render <"+t[0]+"> in this slot but instead it rendered <"+
m+">. The tree doesn't match so React will fallback to client rendering.");var u=t[2];m=t[3];t=b.node;b.replay={nodes:u,slots:m,pendingTasks:1};try{bh(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(F){if("object"===typeof F&&null!==F&&(F===Xf||"function"===typeof F.then))throw b.node===t&&(b.replay=q),
F;b.replay.pendingTasks--;h=Wg(a,b.componentStack);g=a;a=b.blockedBoundary;c=F;h=Y(g,c,h);fh(g,a,u,m,c,h)}b.replay=q}else{if(f!==nf)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(Bf(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{q=void 0;c=t[5];f=t[2];k=t[3];m=null===t[4]?[]:t[4][2];t=null===t[4]?null:t[4][3];p=b.componentStack;var A=b.componentStack=Vg(b,"Suspense"),D=b.keyPath,v=b.replay,y=b.blockedBoundary,
ea=h.children;h=h.fallback;var V=new Set,x=Tg(a,V);x.parentFlushed=!0;x.rootSegmentID=c;b.blockedBoundary=x;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=x.resources;try{$g(a,b,ea,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===x.pendingTasks&&0===x.status){x.status=1;a.completedBoundaries.push(x);
break b}}catch(F){x.status=4,u=Wg(a,b.componentStack),q=Y(a,F,u),x.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(x)}finally{a.renderState.boundaryResources=y?y.resources:null,b.blockedBoundary=y,b.replay=v,b.keyPath=D,b.componentStack=p}b=Ug(a,null,{nodes:m,slots:t,pendingTasks:0},h,-1,y,V,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,A);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else bh(a,b,g,c,f,h,k);return;case ef:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case qf:h=b.componentStack;b.componentStack=Vg(b,"Lazy");g=d._init;d=g(d._payload);b.componentStack=h;Z(a,b,null,d,e);return}if(Ja(d)){gh(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=zf&&d[zf]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);gh(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,Dg(d),e);if(d.$$typeof===kf||d.$$typeof===lf)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function gh(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{gh(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(q){if("object"===typeof q&&
null!==q&&(q===Xf||"function"===typeof q.then))throw q;b.replay.pendingTasks--;c=Wg(a,b.componentStack);var m=b.blockedBoundary,p=q;c=Y(a,p,c);fh(a,m,d,k,p,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=Sf(f,g,d),m=h[d],"number"===typeof m?(eh(a,b,m,k,d),delete h[d]):$g(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Sf(f,g,h),
$g(a,b,d,h);b.treeContext=f;b.keyPath=e}function dh(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function $g(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,m=b.componentStack,p=b.blockedSegment;if(null===p)try{return Z(a,b,null,c,d)}catch(u){if(vg(),c=u===Xf?ag():u,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=wg();a=Ug(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Of(g);return}}else{var q=p.children.length,t=p.chunks.length;try{return Z(a,b,null,c,d)}catch(u){if(vg(),p.children.length=q,p.chunks.length=t,c=u===Xf?ag():u,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=wg();p=b.blockedSegment;q=Og(a,p.chunks.length,null,b.formatContext,p.lastPushedText,!0);p.children.push(q);p.lastPushedText=!1;a=Pg(a,d,b.node,b.childIndex,b.blockedBoundary,q,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,
b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Of(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Of(g);throw c;}function hh(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,ih(this,b,a))}
function fh(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)fh(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,m=f,p=Tg(k,new Set);p.parentFlushed=!0;p.rootSegmentID=h;p.status=4;p.errorDigest=m;p.parentFlushed&&k.clientRenderedBoundaries.push(p)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var q in d)delete d[q]}}
function jh(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){Y(b,c,d);Xg(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Y(b,c,d),fh(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&kh(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=Wg(b,a.componentStack),a=Y(b,c,a),d.errorDigest=a,dh(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return jh(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&lh(b)}
function mh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),m=k.next();0<e.remainingCapacity&&!m.done;m=k.next()){var p=m.value,q=p.props,t=q.href,u=p.props,A=Dc(u.href,"style",{crossOrigin:u.crossOrigin,integrity:u.integrity,
nonce:u.nonce,type:u.type,fetchPriority:u.fetchPriority,referrerPolicy:u.referrerPolicy,media:u.media});if(2<=(e.remainingCapacity-=A.length))c.resets.style[t]=K,f&&(f+=", "),f+=A,c.resets.style[t]="string"===typeof q.crossOrigin||"string"===typeof q.integrity?[q.crossOrigin,q.integrity]:K;else break b}}f?d({Link:f}):d({})}}}catch(D){Y(a,D,{})}}function kh(a){null===a.trackedPostpones&&mh(a,!0);a.onShellError=Mg;a=a.onShellReady;a()}
function lh(a){mh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function ch(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&ch(a,c)}else a.completedSegments.push(b)}
function ih(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&kh(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&ch(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(hh,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(ch(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&lh(a)}
function Sg(a){if(2!==a.status){var b=If,c=Jg.current;Jg.current=Hg;var d=Kg.current;Kg.current=Ig;var e=Qg;Qg=a;var f=Gg;Gg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],m=a,p=k.blockedBoundary;m.renderState.boundaryResources=p?p.resources:null;var q=k.blockedSegment;if(null===q){var t=m;if(0!==k.replay.pendingTasks){Of(k.context);try{var u=k.thenableState;k.thenableState=null;Z(t,k,u,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);ih(t,k.blockedBoundary,null)}catch(L){vg();var A=L===Xf?ag():L;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var D=k.ping;A.then(D,D);k.thenableState=wg()}else{k.replay.pendingTasks--;k.abortSet.delete(k);var v=Wg(t,k.componentStack);m=void 0;var y=t,ea=k.blockedBoundary,V=A,x=k.replay.nodes,F=k.replay.slots;m=Y(y,V,v);fh(y,ea,x,F,V,m);t.pendingRootTasks--;0===t.pendingRootTasks&&kh(t);t.allPendingTasks--;0===t.allPendingTasks&&lh(t)}}finally{t.renderState.boundaryResources=
null}}}else if(t=void 0,y=q,0===y.status){Of(k.context);var G=y.children.length,fa=y.chunks.length;try{var na=k.thenableState;k.thenableState=null;Z(m,k,na,k.node,k.childIndex);y.lastPushedText&&y.textEmbedded&&y.chunks.push(Nb);k.abortSet.delete(k);y.status=1;ih(m,k.blockedBoundary,y)}catch(L){vg();y.children.length=G;y.chunks.length=fa;var W=L===Xf?ag():L;if("object"===typeof W&&null!==W&&"function"===typeof W.then){var ha=k.ping;W.then(ha,ha);k.thenableState=wg()}else{var ia=Wg(m,k.componentStack);
k.abortSet.delete(k);y.status=4;var I=k.blockedBoundary;t=Y(m,W,ia);null===I?Xg(m,W):(I.pendingTasks--,4!==I.status&&(I.status=4,I.errorDigest=t,dh(m,I),I.parentFlushed&&m.clientRenderedBoundaries.push(I)));m.allPendingTasks--;0===m.allPendingTasks&&lh(m)}}finally{m.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&nh(a,a.destination)}catch(L){Y(a,L,{}),Xg(a,L)}finally{Gg=f,Jg.current=c,Kg.current=d,c===Hg&&Of(b),Qg=e}}}
function oh(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;r(b,Gc);r(b,a.placeholderPrefix);a=z(d.toString(16));r(b,a);return w(b,Hc);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)r(b,d[f]);e=ph(a,b,e)}for(;f<d.length-1;f++)r(b,d[f]);f<d.length&&(e=w(b,d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function ph(a,b,c){var d=c.boundary;if(null===d)return oh(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,w(b,Lc),r(b,Nc),d&&(r(b,Pc),r(b,z(H(d))),r(b,Oc)),w(b,Qc),oh(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),oh(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),oh(a,
b,c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach($e,e),c.stylesheets.forEach(af,e));w(b,Ic);d=d.completedSegments;if(1!==d.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");ph(a,b,d[0])}return w(b,Mc)}function qh(a,b,c){md(b,a.renderState,c.parentFormatContext,c.id);ph(a,b,c);return nd(b,c.parentFormatContext)}
function rh(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)sh(a,b,c,d[e]);d.length=0;De(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(r(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,r(b,512<Xd.byteLength?Xd.slice():Xd)):0===(d.instructions&8)?(d.instructions|=8,r(b,Yd)):r(b,Zd):0===(d.instructions&2)?(d.instructions|=
2,r(b,ud)):r(b,vd)):f?r(b,ee):r(b,de);d=z(e.toString(16));r(b,a.boundaryPrefix);r(b,d);g?r(b,$d):r(b,fe);r(b,a.segmentPrefix);r(b,d);f?g?(r(b,ae),Se(b,c)):(r(b,ge),Te(b,c)):g&&r(b,be);d=g?w(b,ce):w(b,ab);return Fc(b,a)&&d}
function sh(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return qh(a,b,d)}if(e===c.rootSegmentID)return qh(a,b,d);qh(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(r(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,r(b,od)):r(b,pd)):r(b,sd);r(b,a.segmentPrefix);e=z(e.toString(16));r(b,e);d?r(b,qd):r(b,td);r(b,a.placeholderPrefix);
r(b,e);b=d?w(b,rd):w(b,ab);return b}
function nh(a,b){l=new Uint8Array(512);n=0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var m=e.htmlChunks,p=e.headChunks;f=0;if(m){for(f=0;f<m.length;f++)r(b,m[f]);if(p)for(f=0;f<p.length;f++)r(b,p[f]);else r(b,
U("head")),r(b,T)}else if(p)for(f=0;f<p.length;f++)r(b,p[f]);var q=e.charsetChunks;for(f=0;f<q.length;f++)r(b,q[f]);q.length=0;e.preconnects.forEach(Ee,b);e.preconnects.clear();var t=e.preconnectChunks;for(f=0;f<t.length;f++)r(b,t[f]);t.length=0;e.fontPreloads.forEach(Ee,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Ee,b);e.highImagePreloads.clear();e.styles.forEach(Le,b);var u=e.importMapChunks;for(f=0;f<u.length;f++)r(b,u[f]);u.length=0;e.bootstrapScripts.forEach(Ee,b);e.scripts.forEach(Ee,
b);e.scripts.clear();e.bulkPreloads.forEach(Ee,b);e.bulkPreloads.clear();var A=e.preloadChunks;for(f=0;f<A.length;f++)r(b,A[f]);A.length=0;var D=e.hoistableChunks;for(f=0;f<D.length;f++)r(b,D[f]);D.length=0;m&&null===p&&r(b,wc("head"));ph(a,b,d);a.completedRootSegment=null;Fc(b,a.renderState)}else return;var v=a.renderState;d=0;v.preconnects.forEach(Ee,b);v.preconnects.clear();var y=v.preconnectChunks;for(d=0;d<y.length;d++)r(b,y[d]);y.length=0;v.fontPreloads.forEach(Ee,b);v.fontPreloads.clear();
v.highImagePreloads.forEach(Ee,b);v.highImagePreloads.clear();v.styles.forEach(Ne,b);v.scripts.forEach(Ee,b);v.scripts.clear();v.bulkPreloads.forEach(Ee,b);v.bulkPreloads.clear();var ea=v.preloadChunks;for(d=0;d<ea.length;d++)r(b,ea[d]);ea.length=0;var V=v.hoistableChunks;for(d=0;d<V.length;d++)r(b,V[d]);V.length=0;var x=a.clientRenderedBoundaries;for(c=0;c<x.length;c++){var F=x[c];v=b;var G=a.resumableState,fa=a.renderState,na=F.rootSegmentID,W=F.errorDigest,ha=F.errorMessage,ia=F.errorComponentStack,
I=0===G.streamingFormat;I?(r(v,fa.startInlineScript),0===(G.instructions&4)?(G.instructions|=4,r(v,he)):r(v,ie)):r(v,me);r(v,fa.boundaryPrefix);r(v,z(na.toString(16)));I&&r(v,je);if(W||ha||ia)I?(r(v,ke),r(v,z(re(W||"")))):(r(v,ne),r(v,z(H(W||""))));if(ha||ia)I?(r(v,ke),r(v,z(re(ha||"")))):(r(v,oe),r(v,z(H(ha||""))));ia&&(I?(r(v,ke),r(v,z(re(ia)))):(r(v,pe),r(v,z(H(ia)))));if(I?!w(v,le):!w(v,ab)){a.destination=null;c++;x.splice(0,c);return}}x.splice(0,c);var L=a.completedBoundaries;for(c=0;c<L.length;c++)if(!rh(a,
b,L[c])){a.destination=null;c++;L.splice(0,c);return}L.splice(0,c);la(b);l=new Uint8Array(512);n=0;var ra=a.partialBoundaries;for(c=0;c<ra.length;c++){var sa=ra[c];a:{x=a;F=b;x.renderState.boundaryResources=sa.resources;var ta=sa.completedSegments;for(G=0;G<ta.length;G++)if(!sh(x,F,sa,ta[G])){G++;ta.splice(0,G);var Ma=!1;break a}ta.splice(0,G);Ma=De(F,sa.resources,x.renderState)}if(!Ma){a.destination=null;c++;ra.splice(0,c);return}}ra.splice(0,c);var ja=a.completedBoundaries;for(c=0;c<ja.length;c++)if(!rh(a,
b,ja[c])){a.destination=null;c++;ja.splice(0,c);return}ja.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&r(b,wc("body")),c.hasHtml&&r(b,wc("html")),la(b),b.close(),a.destination=null):la(b)}}
function th(a){a.flushScheduled=null!==a.destination;bf?setTimeout(function(){return cf.run(a,Sg,a)},0):setTimeout(function(){return Sg(a)},0);null===a.trackedPostpones&&(bf?setTimeout(function(){return cf.run(a,uh,a)},0):setTimeout(function(){return uh(a)},0))}function uh(a){mh(a,0===a.pendingRootTasks)}function Xe(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setTimeout(function(){var b=a.destination;b?nh(a,b):a.flushScheduled=!1},0))}
function vh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return jh(e,a,d)});c.clear()}null!==a.destination&&nh(a,a.destination)}catch(e){Y(a,e,{}),Xg(a,e)}}
exports.renderToReadableStream=function(a,b){return new Promise(function(c,d){var e,f,g=new Promise(function(u,A){f=u;e=A}),h=b?b.onHeaders:void 0,k;h&&(k=function(u){h(new Headers(u))});var m=Kb(b?b.identifierPrefix:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0),p=Ng(a,m,Ab(m,b?b.nonce:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.importMap:void 0,k,b?b.maxHeadersLength:void 0),Lb(b?b.namespaceURI:void 0),
b?b.progressiveChunkSize:void 0,b?b.onError:void 0,f,function(){var u=new ReadableStream({type:"bytes",pull:function(A){if(1===p.status)p.status=2,pa(A,p.fatalError);else if(2!==p.status&&null===p.destination){p.destination=A;try{nh(p,A)}catch(D){Y(p,D,{}),Xg(p,D)}}},cancel:function(A){p.destination=null;vh(p,A)}},{highWaterMark:0});u.allReady=g;c(u)},function(u){g.catch(function(){});d(u)},e,b?b.onPostpone:void 0,b?b.formState:void 0);if(b&&b.signal){var q=b.signal;if(q.aborted)vh(p,q.reason);else{var t=
function(){vh(p,q.reason);q.removeEventListener("abort",t)};q.addEventListener("abort",t)}}th(p)})};exports.version="18.3.0-canary-60a927d04-20240113";

//# sourceMappingURL=react-dom-server.edge.production.min.js.map
