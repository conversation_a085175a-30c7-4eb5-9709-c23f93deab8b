(()=>{var e={};e.id=11,e.ids=[11],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2643:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(482),r=t(9108),i=t(2563),n=t.n(i),l=t(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8761)),"/Users/<USER>/Downloads/astrology/src/app/register/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/register/page.tsx"],m="/register/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8177:(e,s,t)=>{Promise.resolve().then(t.bind(t,5294))},5294:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5344),r=t(3729),i=t(6506),n=t(7292),l=t(9758),o=t(1902),c=t(7623),d=t(7993);let m=()=>{let{t:e}=(0,l.$G)(["common"]),[s,t]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",dateOfBirth:"",gender:"",agreeToTerms:!1}),[m,x]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)(!1),f=e=>{let{name:s,value:a,type:r}=e.target;t(t=>({...t,[s]:"checkbox"===r?e.target.checked:a}))},j=async e=>{if(e.preventDefault(),s.password!==s.confirmPassword){alert("Passwords do not match!");return}if(!s.agreeToTerms){alert("Please agree to the terms and conditions");return}g(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Registration successful! Welcome to Kashi Vedic family."),g(!1)};return(0,a.jsxs)("div",{className:"min-h-screen pt-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30",children:[(0,a.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[a.jsx("div",{className:"absolute top-20 left-10 opacity-10",children:a.jsx(o.ht,{size:150,className:"text-saffron-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-20 right-10 opacity-10",children:a.jsx(o.pO,{size:180,className:"text-lotus-pink-400"})})]}),a.jsx("div",{className:"spiritual-container py-20 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(o.ht,{className:"w-8 h-8 text-white"})}),a.jsx("h1",{className:"text-2xl font-serif font-bold gradient-text mb-2",children:"Join Our Spiritual Community"}),a.jsx("p",{className:"text-gray-600",children:"Create your account to begin your divine journey"})]}),(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),a.jsx("input",{type:"text",name:"firstName",value:s.firstName,onChange:f,className:"spiritual-input",placeholder:"Enter first name",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),a.jsx("input",{type:"text",name:"lastName",value:s.lastName,onChange:f,className:"spiritual-input",placeholder:"Enter last name",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),a.jsx("input",{type:"email",name:"email",value:s.email,onChange:f,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),a.jsx("input",{type:"tel",name:"phone",value:s.phone,onChange:f,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),a.jsx("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:f,className:"spiritual-input",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{name:"gender",value:s.gender,onChange:f,className:"spiritual-input",required:!0,children:[a.jsx("option",{value:"",children:"Select Gender"}),a.jsx("option",{value:"male",children:"Male"}),a.jsx("option",{value:"female",children:"Female"}),a.jsx("option",{value:"other",children:"Other"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:m?"text":"password",name:"password",value:s.password,onChange:f,className:"spiritual-input pr-10",placeholder:"Create password",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!m),children:m?a.jsx(c.Z,{className:"h-5 w-5 text-gray-400"}):a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password *"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:u?"text":"password",name:"confirmPassword",value:s.confirmPassword,onChange:f,className:"spiritual-input pr-10",placeholder:"Confirm password",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!u),children:u?a.jsx(c.Z,{className:"h-5 w-5 text-gray-400"}):a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("div",{className:"flex items-center h-5",children:a.jsx("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",checked:s.agreeToTerms,onChange:f,className:"h-4 w-4 text-saffron-600 focus:ring-saffron-500 border-gray-300 rounded",required:!0})}),a.jsx("div",{className:"ml-3 text-sm",children:(0,a.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-gray-700",children:["I agree to the"," ",a.jsx(i.default,{href:"/terms",className:"text-saffron-600 hover:text-saffron-500",children:"Terms and Conditions"})," ","and"," ",a.jsx(i.default,{href:"/privacy",className:"text-saffron-600 hover:text-saffron-500",children:"Privacy Policy"})]})})]}),a.jsx("button",{type:"submit",disabled:h,className:"w-full spiritual-button py-3 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Creating Account...":"Create Account"})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",a.jsx(i.default,{href:"/login",className:"text-saffron-600 hover:text-saffron-500 font-medium",children:"Sign in here"})]})})]}),a.jsx(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mt-8",children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-lg font-serif font-bold mb-4 gradient-text text-center",children:"What You'll Get as a Member"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDD2E"}),"Daily personalized horoscope"]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDCCA"}),"Free kundli generation"]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDC68‍\uD83C\uDFEB"}),"Access to expert astrologers"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83C\uDFAF"}),"Personalized remedies"]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDCF1"}),"Mobile app access"]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDD14"}),"Important transit alerts"]})]})]})]})})]})})]})}},8761:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/register/page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default},7993:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(3729);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},7623:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(3729);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(2643));module.exports=a})();