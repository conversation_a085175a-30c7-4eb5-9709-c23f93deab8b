(()=>{var e={};e.id=301,e.ids=[301],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7328:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=i(482),a=i(9108),r=i(2563),n=i.n(r),o=i(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let d=["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,2304)),"/Users/<USER>/Downloads/astrology/src/app/about/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,9361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Downloads/astrology/src/app/about/page.tsx"],m="/about/page",x={require:i,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2451:(e,t,i)=>{Promise.resolve().then(i.bind(i,1208))},1208:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o});var s=i(5344);i(3729);var a=i(7292),r=i(9758),n=i(1902);let o=()=>{let{t:e}=(0,r.$G)(["common","home"]),t=[{icon:n.ht,title:"Authentic Wisdom",titleHi:"प्रामाणिक ज्ञान",description:"We preserve and share the ancient Vedic knowledge passed down through generations of learned sages.",descriptionHi:"हम पीढ़ियों से चले आ रहे विद्वान ऋषियों के प्राचीन वैदिक ज्ञान को संरक्षित और साझा करते हैं।"},{icon:n.pO,title:"Spiritual Growth",titleHi:"आध्यात्मिक विकास",description:"Our mission is to guide seekers on their path to spiritual enlightenment and self-discovery.",descriptionHi:"हमारा मिशन साधकों को आध्यात्मिक ज्ञान और आत्म-खोज के पथ पर मार्गदर्शन करना है।"},{icon:n.X5,title:"Sacred Traditions",titleHi:"पवित्र परंपराएं",description:"We honor and maintain the sacred traditions of Hindu astrology and spiritual practices.",descriptionHi:"हम हिंदू ज्योतिष और आध्यात्मिक प्रथाओं की पवित्र परंपराओं का सम्मान और रखरखाव करते हैं।"},{icon:n.UW,title:"Holistic Approach",titleHi:"समग्र दृष्टिकोण",description:"We believe in addressing the mind, body, and soul for complete spiritual well-being.",descriptionHi:"हम पूर्ण आध्यात्मिक कल्याण के लिए मन, शरीर और आत्मा को संबोधित करने में विश्वास करते हैं।"}];return(0,s.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,s.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[s.jsx("div",{className:"absolute top-10 left-10",children:s.jsx(n.ht,{size:100,className:"text-saffron-400 om-pulse"})}),s.jsx("div",{className:"absolute bottom-10 right-10",children:s.jsx(n.pO,{size:120,className:"text-lotus-pink-400"})}),s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:s.jsx(n.UW,{size:200,className:"text-divine-purple-300 mandala-rotate"})})]}),s.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[s.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"About Kashi Vedic"}),s.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Preserving ancient wisdom for modern seekers on their spiritual journey"})]})})]}),s.jsx("section",{className:"py-20 bg-white",children:s.jsx("div",{className:"spiritual-container",children:s.jsx("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-6 gradient-text",children:"Our Sacred Journey"}),(0,s.jsxs)("div",{className:"prose prose-lg mx-auto text-gray-700 leading-relaxed",children:[s.jsx("p",{className:"mb-6",children:"Founded in the holy city of Varanasi (Kashi), our spiritual center has been a beacon of ancient Vedic wisdom for over two decades. We began as a small ashram dedicated to preserving the sacred knowledge of our ancestors and making it accessible to modern seekers."}),s.jsx("p",{className:"mb-6",children:"Our journey started when Acharya Ramesh Sharma, after years of studying under renowned gurus in the Himalayas, felt called to share the profound insights of Vedic astrology with the world. What began as personal consultations in a humble temple courtyard has grown into a comprehensive spiritual guidance center."}),s.jsx("p",{children:"Today, we serve thousands of seekers worldwide, bridging the gap between ancient wisdom and contemporary life. Our mission remains unchanged: to illuminate the path of dharma and help souls discover their divine purpose through the sacred sciences of astrology, palmistry, numerology, and spiritual counseling."})]})]})})})}),s.jsx("section",{className:"py-20 bg-gray-50",children:(0,s.jsxs)("div",{className:"spiritual-container",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-6 gradient-text",children:"Our Sacred Values"}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"The principles that guide our spiritual mission and service to humanity"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:t.map((e,t)=>{let i=e.icon;return(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"text-center",children:[s.jsx("div",{className:"w-20 h-20 bg-gradient-saffron rounded-full mx-auto mb-6 flex items-center justify-center",children:s.jsx(i,{className:"w-10 h-10 text-white"})}),s.jsx("h3",{className:"text-xl font-serif font-bold mb-3",children:e.title}),s.jsx("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},e.title)})})]})}),s.jsx("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"spiritual-container",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-6 gradient-text",children:"Our Spiritual Guides"}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Meet the enlightened souls who dedicate their lives to guiding others on the spiritual path"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{name:"Acharya Ramesh Sharma",nameHi:"आचार्य रमेश शर्मा",role:"Founder & Chief Astrologer",roleHi:"संस्थापक और मुख्य ज्योतिषी",experience:"25+ years",description:"A renowned Vedic astrologer with deep knowledge of ancient scriptures and modern applications.",descriptionHi:"प्राचीन शास्त्रों और आधुनिक अनुप्रयोगों के गहन ज्ञान के साथ एक प्रसिद्ध वैदिक ज्योतिषी।",image:"/images/founder.jpg"},{name:"Dr. Priya Devi",nameHi:"डॉ. प्रिया देवी",role:"Senior Astrologer",roleHi:"वरिष्ठ ज्योतिषी",experience:"18+ years",description:"Specialist in palmistry, numerology, and spiritual counseling with a PhD in Sanskrit.",descriptionHi:"संस्कृत में पीएचडी के साथ हस्तरेखा, अंकज्योतिष और आध्यात्मिक परामर्श में विशेषज्ञ।",image:"/images/senior-astrologer.jpg"},{name:"Pandit Vikash Joshi",nameHi:"पंडित विकाश जोशी",role:"Vastu Consultant",roleHi:"वास्तु सलाहकार",experience:"20+ years",description:"Expert in Vastu Shastra and sacred architecture with extensive temple consultation experience.",descriptionHi:"व्यापक मंदिर परामर्श अनुभव के साथ वास्तु शास्त्र और पवित्र वास्तुकला में विशेषज्ञ।",image:"/images/vastu-expert.jpg"}].map((e,t)=>(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"spiritual-card-enhanced text-center",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-saffron rounded-full mx-auto mb-6 flex items-center justify-center",children:s.jsx(n.ht,{className:"w-12 h-12 text-white"})}),s.jsx("h3",{className:"text-xl font-serif font-bold mb-2",children:e.name}),s.jsx("p",{className:"text-saffron-600 font-medium mb-2",children:e.role}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 mb-4",children:[e.experience," experience"]}),s.jsx("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},e.name))})]})}),(0,s.jsxs)("section",{className:"py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-saffron-900 text-white relative overflow-hidden",children:[s.jsx("div",{className:"absolute inset-0 opacity-10",children:s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:s.jsx(n.UW,{size:300,className:"text-saffron-400 mandala-rotate"})})}),s.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-8 gradient-text",children:"Our Sacred Mission"}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx("p",{className:"text-xl text-gray-300 mb-8 leading-relaxed",children:'"To illuminate the path of dharma for every soul seeking truth, providing authentic Vedic guidance that honors ancient wisdom while addressing modern challenges. We are committed to preserving the sacred knowledge of our ancestors and making it accessible to all who seek spiritual growth and divine connection."'}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("span",{className:"text-2xl text-saffron-400",children:"\uD83D\uDD49️ सत्यमेव जयते \uD83D\uDD49️"}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Truth Alone Triumphs"})]})]})]})})]})]})}},2304:(e,t,i)=>{"use strict";i.r(t),i.d(t,{$$typeof:()=>r,__esModule:()=>a,default:()=>n});let s=(0,i(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/about/page.tsx`),{__esModule:a,$$typeof:r}=s,n=s.default}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[651,713],()=>i(7328));module.exports=s})();