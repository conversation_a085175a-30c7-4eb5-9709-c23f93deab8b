(()=>{var e={};e.id=672,e.ids=[672],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5109:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>x,originalPathname:()=>o,pages:()=>m,routeModule:()=>h,tree:()=>d});var t=a(482),i=a(9108),n=a(2563),r=a.n(n),l=a(8300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let d=["",{children:["puja",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7468)),"/Users/<USER>/Downloads/astrology/src/app/puja/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,9361,23)),"next/dist/client/components/not-found-error"]}],m=["/Users/<USER>/Downloads/astrology/src/app/puja/page.tsx"],o="/puja/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/puja/page",pathname:"/puja",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4230:(e,s,a)=>{Promise.resolve().then(a.bind(a,1588))},1588:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var t=a(5344),i=a(3729),n=a(7292),r=a(1902);let l=()=>{let[e,s]=(0,i.useState)(null),[a,l]=(0,i.useState)("details"),[c,d]=(0,i.useState)({name:"",email:"",phone:"",date:"",time:"",address:"",specialRequests:"",paymentMethod:"card"}),[m,o]=(0,i.useState)(!1),x=[{id:"ganesh",name:"Ganesh Puja",nameHi:"गणेश पूजा",description:"Remove obstacles and bring prosperity to your life",descriptionHi:"बाधाओं को दूर करें और अपने जीवन में समृद्धि लाएं",duration:"2-3 hours",price:2500,benefits:["Obstacle removal","Success in new ventures","Wisdom and prosperity"],image:"/images/ganesh-puja.jpg"},{id:"lakshmi",name:"Lakshmi Puja",nameHi:"लक्ष्मी पूजा",description:"Attract wealth, abundance and financial prosperity",descriptionHi:"धन, समृद्धि और वित्तीय खुशहाली को आकर्षित करें",duration:"3-4 hours",price:3500,benefits:["Wealth attraction","Financial stability","Business growth"],image:"/images/lakshmi-puja.jpg"},{id:"shiva",name:"Shiva Puja",nameHi:"शिव पूजा",description:"Seek blessings for spiritual growth and inner peace",descriptionHi:"आध्यात्मिक विकास और आंतरिक शांति के लिए आशीर्वाद मांगें",duration:"4-5 hours",price:4e3,benefits:["Spiritual awakening","Inner peace","Negative energy removal"],image:"/images/shiva-puja.jpg"},{id:"durga",name:"Durga Puja",nameHi:"दुर्गा पूजा",description:"Protection from negative forces and empowerment",descriptionHi:"नकारात्मक शक्तियों से सुरक्षा और सशक्तिकरण",duration:"5-6 hours",price:5e3,benefits:["Protection from evil","Strength and courage","Victory over enemies"],image:"/images/durga-puja.jpg"},{id:"saraswati",name:"Saraswati Puja",nameHi:"सरस्वती पूजा",description:"Enhance knowledge, wisdom and creative abilities",descriptionHi:"ज्ञान, बुद्धि और रचनात्मक क्षमताओं को बढ़ाएं",duration:"2-3 hours",price:2e3,benefits:["Enhanced learning","Creative inspiration","Academic success"],image:"/images/saraswati-puja.jpg"},{id:"hanuman",name:"Hanuman Puja",nameHi:"हनुमान पूजा",description:"Gain strength, courage and protection from difficulties",descriptionHi:"शक्ति, साहस और कठिनाइयों से सुरक्षा प्राप्त करें",duration:"3-4 hours",price:3e3,benefits:["Physical strength","Mental courage","Protection from harm"],image:"/images/hanuman-puja.jpg"}],h=e=>{let{name:s,value:a}=e.target;d(e=>({...e,[s]:a}))},u=async e=>{e.preventDefault(),"details"===a?l("payment"):"payment"===a&&(o(!0),await new Promise(e=>setTimeout(e,3e3)),o(!1),l("confirmation"))};return(0,t.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,t.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[t.jsx("div",{className:"absolute top-10 left-10",children:t.jsx(r.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),t.jsx("div",{className:"absolute bottom-10 right-10",children:t.jsx(r.pO,{size:120,className:"text-lotus-pink-400"})})]}),t.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[t.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Sacred Puja Services"}),t.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Book authentic Hindu rituals and ceremonies performed by experienced priests for divine blessings"})]})})]}),t.jsx("section",{className:"py-16 bg-white",children:(0,t.jsxs)("div",{className:"spiritual-container",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Choose Your Sacred Ritual"}),t.jsx("p",{className:"text-gray-600",children:"Select from our collection of traditional pujas and ceremonies"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:x.map((a,i)=>(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{y:-8,scale:1.02},transition:{duration:.5,delay:.1*i,hover:{duration:.3}},viewport:{once:!0},className:`spiritual-card-enhanced cursor-pointer transition-all duration-300 ${e===a.id?"ring-2 ring-bhagwa-500":""}`,onClick:()=>s(a.id),children:[(0,t.jsxs)("div",{className:"text-center mb-4",children:[t.jsx("div",{className:"w-20 h-20 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:t.jsx(r.X5,{className:"w-10 h-10 text-white"})}),t.jsx("h3",{className:"text-xl font-serif font-bold mb-2",children:a.name}),t.jsx("p",{className:"text-sm text-gray-500 mb-3",children:a.nameHi}),t.jsx("p",{className:"text-gray-600 mb-4",children:a.description})]}),(0,t.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"font-medium",children:"Duration:"}),t.jsx("span",{children:a.duration})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{className:"font-medium",children:"Price:"}),(0,t.jsxs)("span",{className:"text-bhagwa-600 font-bold",children:["₹",a.price]})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[t.jsx("h4",{className:"font-semibold text-sm mb-2",children:"Benefits:"}),t.jsx("ul",{className:"space-y-1",children:a.benefits.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center text-xs text-gray-600",children:[t.jsx(r.UI,{className:"w-3 h-3 text-bhagwa-400 mr-2"}),e]},s))})]}),t.jsx("button",{className:"w-full spiritual-button text-sm py-2",children:"Book This Puja"})]},a.id))})]})}),e&&t.jsx("section",{className:"py-16 bg-gray-50",children:t.jsx("div",{className:"spiritual-container max-w-2xl mx-auto",children:(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"spiritual-card-enhanced",children:[t.jsx("div",{className:"flex items-center justify-center mb-8",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:`flex items-center ${"details"===a?"text-bhagwa-600":"payment"===a||"confirmation"===a?"text-green-600":"text-gray-400"}`,children:[t.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${"details"===a?"bg-bhagwa-600 text-white":"payment"===a||"confirmation"===a?"bg-green-600 text-white":"bg-gray-200"}`,children:"1"}),t.jsx("span",{className:"ml-2 text-sm font-medium",children:"Details"})]}),t.jsx("div",{className:"w-8 h-0.5 bg-gray-300"}),(0,t.jsxs)("div",{className:`flex items-center ${"payment"===a?"text-bhagwa-600":"confirmation"===a?"text-green-600":"text-gray-400"}`,children:[t.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${"payment"===a?"bg-bhagwa-600 text-white":"confirmation"===a?"bg-green-600 text-white":"bg-gray-200"}`,children:"2"}),t.jsx("span",{className:"ml-2 text-sm font-medium",children:"Payment"})]}),t.jsx("div",{className:"w-8 h-0.5 bg-gray-300"}),(0,t.jsxs)("div",{className:`flex items-center ${"confirmation"===a?"text-green-600":"text-gray-400"}`,children:[t.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${"confirmation"===a?"bg-green-600 text-white":"bg-gray-200"}`,children:"3"}),t.jsx("span",{className:"ml-2 text-sm font-medium",children:"Confirmation"})]})]})}),(0,t.jsxs)("h3",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:["details"===a&&`Book Your ${x.find(s=>s.id===e)?.name}`,"payment"===a&&"Payment Details","confirmation"===a&&"Booking Confirmed!"]}),"confirmation"===a?(0,t.jsxs)("div",{className:"text-center space-y-6",children:[t.jsx("div",{className:"w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center",children:t.jsx("svg",{className:"w-10 h-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"text-xl font-bold text-green-600 mb-2",children:"Booking Successful!"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Your puja has been booked successfully. You will receive a confirmation email shortly."}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg text-left max-w-md mx-auto",children:[t.jsx("h5",{className:"font-semibold mb-2",children:"Booking Details:"}),(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Booking ID:"}),(0,t.jsxs)("span",{className:"font-mono",children:["PJ",Date.now().toString().slice(-6)]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Puja:"}),t.jsx("span",{children:x.find(s=>s.id===e)?.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Date:"}),t.jsx("span",{children:c.date})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Time:"}),t.jsx("span",{children:c.time})]}),(0,t.jsxs)("div",{className:"flex justify-between font-bold",children:[t.jsx("span",{children:"Amount Paid:"}),(0,t.jsxs)("span",{children:["₹",x.find(s=>s.id===e)?.price]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-center space-x-4",children:[t.jsx("button",{onClick:()=>{s(null),l("details"),d({name:"",email:"",phone:"",date:"",time:"",address:"",specialRequests:"",paymentMethod:"card"})},className:"spiritual-button-secondary py-2 px-6",children:"Book Another Puja"}),t.jsx("button",{className:"spiritual-button py-2 px-6",children:"Download Receipt"})]})]}):(0,t.jsxs)("form",{onSubmit:u,className:"space-y-6",children:["details"===a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),t.jsx("input",{type:"text",name:"name",value:c.name,onChange:h,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),t.jsx("input",{type:"tel",name:"phone",value:c.phone,onChange:h,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX",required:!0})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),t.jsx("input",{type:"email",name:"email",value:c.email,onChange:h,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Date *"}),t.jsx("input",{type:"date",name:"date",value:c.date,onChange:h,className:"spiritual-input",min:new Date().toISOString().split("T")[0],required:!0})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Time"}),(0,t.jsxs)("select",{name:"time",value:c.time,onChange:h,className:"spiritual-input",children:[t.jsx("option",{value:"",children:"Select time"}),t.jsx("option",{value:"morning",children:"Morning (6:00 AM - 12:00 PM)"}),t.jsx("option",{value:"afternoon",children:"Afternoon (12:00 PM - 6:00 PM)"}),t.jsx("option",{value:"evening",children:"Evening (6:00 PM - 10:00 PM)"})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Address for Puja *"}),t.jsx("textarea",{rows:3,name:"address",value:c.address,onChange:h,className:"spiritual-input",placeholder:"Enter complete address where puja will be performed",required:!0})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Special Requirements"}),t.jsx("textarea",{rows:3,name:"specialRequests",value:c.specialRequests,onChange:h,className:"spiritual-input",placeholder:"Any specific requirements or requests for the puja..."})]})]}),"payment"===a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[t.jsx("h4",{className:"font-semibold mb-3",children:"Booking Summary"}),(0,t.jsxs)("div",{className:"text-sm space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Name:"}),t.jsx("span",{children:c.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Puja:"}),t.jsx("span",{children:x.find(s=>s.id===e)?.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Date:"}),t.jsx("span",{children:c.date})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Time:"}),t.jsx("span",{children:c.time})]}),t.jsx("hr",{className:"my-2"}),(0,t.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[t.jsx("span",{children:"Total Amount:"}),(0,t.jsxs)("span",{className:"text-bhagwa-600",children:["₹",x.find(s=>s.id===e)?.price]})]})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Payment Method *"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",id:"card",name:"paymentMethod",value:"card",checked:"card"===c.paymentMethod,onChange:h,className:"mr-3"}),(0,t.jsxs)("label",{htmlFor:"card",className:"flex items-center",children:[t.jsx("span",{className:"text-2xl mr-2",children:"\uD83D\uDCB3"}),"Credit/Debit Card"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",id:"upi",name:"paymentMethod",value:"upi",checked:"upi"===c.paymentMethod,onChange:h,className:"mr-3"}),(0,t.jsxs)("label",{htmlFor:"upi",className:"flex items-center",children:[t.jsx("span",{className:"text-2xl mr-2",children:"\uD83D\uDCF1"}),"UPI Payment"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",id:"netbanking",name:"paymentMethod",value:"netbanking",checked:"netbanking"===c.paymentMethod,onChange:h,className:"mr-3"}),(0,t.jsxs)("label",{htmlFor:"netbanking",className:"flex items-center",children:[t.jsx("span",{className:"text-2xl mr-2",children:"\uD83C\uDFE6"}),"Net Banking"]})]})]})]}),"card"===c.paymentMethod&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Number *"}),t.jsx("input",{type:"text",className:"spiritual-input",placeholder:"1234 5678 9012 3456",maxLength:19,required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiry Date *"}),t.jsx("input",{type:"text",className:"spiritual-input",placeholder:"MM/YY",maxLength:5,required:!0})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVV *"}),t.jsx("input",{type:"text",className:"spiritual-input",placeholder:"123",maxLength:3,required:!0})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cardholder Name *"}),t.jsx("input",{type:"text",className:"spiritual-input",placeholder:"Name as on card",required:!0})]})]}),"upi"===c.paymentMethod&&(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UPI ID *"}),t.jsx("input",{type:"text",className:"spiritual-input",placeholder:"yourname@upi",required:!0})]})]}),"details"===a&&(0,t.jsxs)("div",{className:"bg-bhagwa-50 p-4 rounded-lg",children:[t.jsx("h4",{className:"font-semibold mb-2",children:"Puja Details:"}),(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Puja:"}),t.jsx("span",{children:x.find(s=>s.id===e)?.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{children:"Duration:"}),t.jsx("span",{children:x.find(s=>s.id===e)?.duration})]}),(0,t.jsxs)("div",{className:"flex justify-between font-bold",children:[t.jsx("span",{children:"Total Amount:"}),(0,t.jsxs)("span",{children:["₹",x.find(s=>s.id===e)?.price]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:["payment"===a&&t.jsx("button",{type:"button",onClick:()=>l("details"),className:"spiritual-button-secondary py-3 px-6",children:"Back to Details"}),t.jsx("button",{type:"submit",disabled:m,className:`spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed ${"payment"===a?"ml-auto":"mx-auto"}`,children:m?(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Processing Payment..."]}):"details"===a?"Proceed to Payment":`Pay ₹${x.find(s=>s.id===e)?.price}`})]})]})]})})}),t.jsx("section",{className:"py-16 bg-white",children:t.jsx("div",{className:"spiritual-container",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[t.jsx("h2",{className:"text-3xl font-serif font-bold mb-8 gradient-text",children:"Why Choose Our Puja Services?"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:t.jsx(r.ht,{className:"w-8 h-8 text-white"})}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Authentic Rituals"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Traditional ceremonies performed according to ancient Vedic scriptures"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:t.jsx(r.X5,{className:"w-8 h-8 text-white"})}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Experienced Priests"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"Certified and experienced priests with deep knowledge of rituals"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:t.jsx(r.pO,{className:"w-8 h-8 text-white"})}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Sacred Materials"}),t.jsx("p",{className:"text-gray-600 text-sm",children:"All puja materials and offerings included in the service"})]})]})]})})})]})}},7468:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>r});let t=(0,a(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/puja/page.tsx`),{__esModule:i,$$typeof:n}=t,r=t.default}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[651,713],()=>a(5109));module.exports=t})();