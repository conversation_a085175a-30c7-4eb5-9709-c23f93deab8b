<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="sacred" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <!-- Sacred geometry pattern -->
      <g transform="translate(50,50)" opacity="0.4">
        <!-- Outer square -->
        <rect x="-40" y="-40" width="80" height="80" fill="none" stroke="currentColor" stroke-width="1"/>
        <!-- Inner circle -->
        <circle cx="0" cy="0" r="35" fill="none" stroke="currentColor" stroke-width="1"/>
        <!-- Triangles -->
        <polygon points="0,-25 22,12 -22,12" fill="none" stroke="currentColor" stroke-width="1"/>
        <polygon points="0,25 -22,-12 22,-12" fill="none" stroke="currentColor" stroke-width="1"/>
        <!-- Center point -->
        <circle cx="0" cy="0" r="3" fill="currentColor"/>
        <!-- Corner dots -->
        <circle cx="-30" cy="-30" r="2" fill="currentColor"/>
        <circle cx="30" cy="-30" r="2" fill="currentColor"/>
        <circle cx="-30" cy="30" r="2" fill="currentColor"/>
        <circle cx="30" cy="30" r="2" fill="currentColor"/>
      </g>
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#sacred)"/>
</svg>
