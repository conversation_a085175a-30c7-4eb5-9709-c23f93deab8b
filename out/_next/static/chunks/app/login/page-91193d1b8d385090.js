(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{8349:function(e,s,t){Promise.resolve().then(t.bind(t,378))},378:function(e,s,t){"use strict";t.r(s);var a=t(3827),r=t(4090),i=t(8792),l=t(8371),n=t(2635),c=t(3665),o=t(5228),d=t(463);s.default=()=>{let{t:e}=(0,n.$G)(["common"]),[s,t]=(0,r.useState)({email:"",password:""}),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),f=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))},p=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Login successful! Welcome to Kashi Vedic."),h(!1)};return(0,a.jsxs)("div",{className:"min-h-screen pt-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30",children:[(0,a.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute top-20 left-10 opacity-10",children:(0,a.jsx)(c.ht,{size:150,className:"text-saffron-400 om-pulse"})}),(0,a.jsx)("div",{className:"absolute bottom-20 right-10 opacity-10",children:(0,a.jsx)(c.pO,{size:180,className:"text-lotus-pink-400"})})]}),(0,a.jsx)("div",{className:"spiritual-container py-20 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(c.ht,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h1",{className:"text-2xl font-serif font-bold gradient-text mb-2",children:"Welcome Back"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Sign in to your spiritual journey"})]}),(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",name:"email",value:s.email,onChange:f,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:m?"text":"password",name:"password",value:s.password,onChange:f,className:"spiritual-input pr-10",placeholder:"Enter your password",required:!0}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!m),children:m?(0,a.jsx)(o.Z,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-saffron-600 focus:ring-saffron-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)(i.default,{href:"/forgot-password",className:"text-saffron-600 hover:text-saffron-500",children:"Forgot password?"})})]}),(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full spiritual-button py-3 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Signing in...":"Sign In"})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCE7"}),"Google"]}),(0,a.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:"\uD83D\uDCF1"}),"Phone"]})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,a.jsx)(i.default,{href:"/register",className:"text-saffron-600 hover:text-saffron-500 font-medium",children:"Create one here"})]})})]}),(0,a.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mt-8 text-center",children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsx)("h3",{className:"text-lg font-serif font-bold mb-3 gradient-text",children:"Why Join Kashi Vedic?"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Personalized horoscope and predictions"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Access to expert astrologers"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Detailed kundli analysis and reports"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Spiritual guidance and remedies"]})]})]})})]})})]})}},463:function(e,s,t){"use strict";var a=t(4090);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},i),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});s.Z=r},5228:function(e,s,t){"use strict";var a=t(4090);let r=a.forwardRef(function(e,s){let{title:t,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},i),t?a.createElement("title",{id:r},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});s.Z=r}},function(e){e.O(0,[371,635,792,665,971,69,744],function(){return e(e.s=8349)}),_N_E=e.O()}]);