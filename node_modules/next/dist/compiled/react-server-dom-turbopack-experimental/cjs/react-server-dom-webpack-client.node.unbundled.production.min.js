/**
 * @license React
 * react-server-dom-webpack-client.node.unbundled.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';var n=require("util"),q=require("react-dom"),r=require("react"),u={stream:!0};function v(a,b){var d=a[b[0]];if(a=d[b[2]])d=a.name;else{a=d["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');d=b[2]}return{specifier:a.specifier,name:d,async:4===b.length}}var w=new Map;
function x(a){var b=w.get(a.specifier);if(b)return"fulfilled"===b.status?null:b;var d=import(a.specifier);a.async&&(d=d.then(function(c){return c.default}));d.then(function(c){var e=d;e.status="fulfilled";e.value=c},function(c){var e=d;e.status="rejected";e.reason=c});w.set(a.specifier,d);return d}
function z(a,b,d){if(null!==a)for(var c=1;c<b.length;c+=2){var e=d,h=A.current;if(h){var f=h.preinitScript,k=a.prefix+b[c];var l=a.crossOrigin;l="string"===typeof l?"use-credentials"===l?l:"":void 0;f.call(h,k,{crossOrigin:l,nonce:e})}}}
var A=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,B=Symbol.for("react.element"),C=Symbol.for("react.provider"),aa=Symbol.for("react.server_context"),ba=Symbol.for("react.lazy"),D=Symbol.for("react.default_value"),ca=Symbol.for("react.postpone"),E=Symbol.iterator;function da(a){if(null===a||"object"!==typeof a)return null;a=E&&a[E]||a["@@iterator"];return"function"===typeof a?a:null}var ea=Array.isArray,G=new WeakMap;
function fa(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ha(a,b,d,c){function e(l,g){if(null===g)return null;if("object"===typeof g){if("function"===typeof g.then){null===k&&(k=new FormData);f++;var t=h++;g.then(function(p){p=JSON.stringify(p,e);var y=k;y.append(b+t,p);f--;0===f&&d(y)},function(p){c(p)});return"$@"+t.toString(16)}if(g instanceof FormData){null===k&&(k=new FormData);var F=k;l=h++;var m=b+l+"_";g.forEach(function(p,y){F.append(m+y,p)});return"$K"+l.toString(16)}return g instanceof Map?(g=JSON.stringify(Array.from(g),e),null===k&&
(k=new FormData),l=h++,k.append(b+l,g),"$Q"+l.toString(16)):g instanceof Set?(g=JSON.stringify(Array.from(g),e),null===k&&(k=new FormData),l=h++,k.append(b+l,g),"$W"+l.toString(16)):!ea(g)&&da(g)?Array.from(g):g}if("string"===typeof g){if("Z"===g[g.length-1]&&this[l]instanceof Date)return"$D"+g;g="$"===g[0]?"$"+g:g;return g}if("boolean"===typeof g)return g;if("number"===typeof g)return fa(g);if("undefined"===typeof g)return"$undefined";if("function"===typeof g){g=G.get(g);if(void 0!==g)return g=JSON.stringify(g,
e),null===k&&(k=new FormData),l=h++,k.set(b+l,g),"$F"+l.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof g){l=g.description;if(Symbol.for(l)!==g)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+(g.description+") cannot be found among global symbols."));return"$S"+l}if("bigint"===typeof g)return"$n"+
g.toString(10);throw Error("Type "+typeof g+" is not supported as an argument to a Server Function.");}var h=1,f=0,k=null;a=JSON.stringify(a,e);null===k?d(a):(k.set(b+"0",a),0===f&&d(k))}var H=new WeakMap;function ia(a){var b,d,c=new Promise(function(e,h){b=e;d=h});ha(a,"",function(e){if("string"===typeof e){var h=new FormData;h.append("0",e);e=h}c.status="fulfilled";c.value=e;b(e)},function(e){c.status="rejected";c.reason=e;d(e)});return c}
function ja(a){var b=G.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var d=null;if(null!==b.bound){d=H.get(b);d||(d=ia(b),H.set(b,d));if("rejected"===d.status)throw d.reason;if("fulfilled"!==d.status)throw d;b=d.value;var c=new FormData;b.forEach(function(e,h){c.append("$ACTION_"+a+":"+h,e)});d=c;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:d}}
function ka(a,b){var d=G.get(this);if(!d)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(d.id!==a)return!1;var c=d.bound;if(null===c)return 0===b;switch(c.status){case "fulfilled":return c.value.length===b;case "pending":throw c;case "rejected":throw c.reason;default:throw"string"!==typeof c.status&&(c.status="pending",c.then(function(e){c.status="fulfilled";c.value=e},function(e){c.status="rejected";c.reason=e})),c;}}
function I(a,b){Object.defineProperties(a,{$$FORM_ACTION:{value:ja},$$IS_SIGNATURE_EQUAL:{value:ka},bind:{value:la}});G.set(a,b)}var ma=Function.prototype.bind,na=Array.prototype.slice;function la(){var a=ma.apply(this,arguments),b=G.get(this);if(b){var d=na.call(arguments,1),c=null;c=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(d)}):Promise.resolve(d);I(a,{id:b.id,bound:c})}return a}
function oa(a,b){function d(){var c=Array.prototype.slice.call(arguments);return b(a,c)}I(d,{id:a,bound:null});return d}var J=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ContextRegistry;function K(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}K.prototype=Object.create(Promise.prototype);
K.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function pa(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":throw a;default:throw a.reason;}}function N(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}function O(a,b,d){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":a.value=b;a.reason=d;break;case "rejected":d&&N(d,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&N(d,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.value,c=a.reason;a.status="resolved_module";a.value=b;null!==d&&(M(a),O(a,d,c))}}var R=null,S=null;
function L(a){var b=R,d=S;R=a;S=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==S&&0<S.deps?(S.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{R=b,S=d}}function M(a){try{var b=a.value,d=w.get(b.specifier);if("fulfilled"===d.status)var c=d.value;else throw d.reason;var e="*"===b.name?c:""===b.name?c.default:c[b.name];a.status="fulfilled";a.value=e}catch(h){a.status="rejected",a.reason=h}}
function T(a,b){a._chunks.forEach(function(d){"pending"===d.status&&P(d,b)})}function U(a,b){var d=a._chunks,c=d.get(b);c||(c=new K("pending",null,null,a),d.set(b,c));return c}function qa(a,b,d){if(S){var c=S;c.deps++}else c=S={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&N(e,c.value))}}function ra(a){return function(b){return P(a,b)}}
function sa(a,b){function d(){var e=Array.prototype.slice.call(arguments),h=b.bound;return h?"fulfilled"===h.status?c(b.id,h.value.concat(e)):Promise.resolve(h).then(function(f){return c(b.id,f.concat(e))}):c(b.id,e)}var c=a._callServer;I(d,b);return d}function V(a,b){a=U(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ta(a,b,d,c){if("$"===c[0]){if("$"===c)return B;switch(c[1]){case "$":return c.slice(1);case "L":return b=parseInt(c.slice(2),16),a=U(a,b),{$$typeof:ba,_payload:a,_init:pa};case "@":return b=parseInt(c.slice(2),16),U(a,b);case "S":return Symbol.for(c.slice(2));case "P":return a=c.slice(2),J[a]||(b={$$typeof:aa,_currentValue:D,_currentValue2:D,_defaultValue:D,_threadCount:0,Provider:null,Consumer:null,_globalName:a},b.Provider={$$typeof:C,_context:b},J[a]=b),J[a].Provider;case "F":return b=
parseInt(c.slice(2),16),b=V(a,b),sa(a,b);case "Q":return b=parseInt(c.slice(2),16),a=V(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=V(a,b),new Set(a);case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=U(a,c);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;
case "pending":case "blocked":return c=R,a.then(qa(c,b,d),ra(c)),null;default:throw a.reason;}}}return c}function ua(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function va(a,b,d,c){var e=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==d?d:ua,_nonce:c,_chunks:e,_stringDecoder:new n.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=wa(a);return a}
function W(a,b,d){a._chunks.set(b,new K("fulfilled",d,null,a))}function xa(a,b,d){var c=a._chunks,e=c.get(b);d=JSON.parse(d,a._fromJSON);var h=v(a._bundlerConfig,d);z(a._moduleLoading,d[1],a._nonce);if(d=x(h)){if(e){var f=e;f.status="blocked"}else f=new K("blocked",null,null,a),c.set(b,f);d.then(function(){return Q(f,h)},function(k){return P(f,k)})}else e?Q(e,h):c.set(b,new K("resolved_module",h,null,a))}
function X(a,b){for(var d=a.length,c=b.length,e=0;e<d;e++)c+=a[e].byteLength;c=new Uint8Array(c);for(var h=e=0;h<d;h++){var f=a[h];c.set(f,e);e+=f.byteLength}c.set(b,e);return c}function Y(a,b,d,c,e,h){d=0===d.length&&0===c.byteOffset%h?c:X(d,c);e=new e(d.buffer,d.byteOffset,d.byteLength/h);W(a,b,e)}
function ya(a,b,d,c,e){switch(d){case 65:W(a,b,X(c,e).buffer);return;case 67:Y(a,b,c,e,Int8Array,1);return;case 99:W(a,b,0===c.length?e:X(c,e));return;case 85:Y(a,b,c,e,Uint8ClampedArray,1);return;case 83:Y(a,b,c,e,Int16Array,2);return;case 115:Y(a,b,c,e,Uint16Array,2);return;case 76:Y(a,b,c,e,Int32Array,4);return;case 108:Y(a,b,c,e,Uint32Array,4);return;case 70:Y(a,b,c,e,Float32Array,4);return;case 68:Y(a,b,c,e,Float64Array,8);return;case 78:Y(a,b,c,e,BigInt64Array,8);return;case 109:Y(a,b,c,e,BigUint64Array,
8);return;case 86:Y(a,b,c,e,DataView,1);return}for(var h=a._stringDecoder,f="",k=0;k<c.length;k++)f+=h.decode(c[k],u);f+=h.decode(e);switch(d){case 73:xa(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=A.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];d=a[1];3===a.length?f.preload(b,d,a[2]):f.preload(b,d);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:d=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=d;d=a._chunks;(c=d.get(b))?P(c,f):d.set(b,new K("rejected",null,f,a));break;case 84:a._chunks.set(b,new K("fulfilled",f,null,a));break;case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");f.$$typeof=ca;f.stack="Error: "+f.message;d=a._chunks;(c=d.get(b))?P(c,f):d.set(b,new K("rejected",null,f,a));break;default:c=a._chunks,(d=c.get(b))?"pending"===d.status&&(a=d.value,b=d.reason,d.status=
"resolved_model",d.value=f,null!==a&&(L(d),O(d,a,b))):c.set(b,new K("resolved_model",f,null,a))}}function wa(a){return function(b,d){return"string"===typeof d?ta(a,this,b,d):"object"===typeof d&&null!==d?(b=d[0]===B?{$$typeof:B,type:d[1],key:d[2],ref:null,props:d[3],_owner:null}:d,b):d}}function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b,d){var c=va(b.moduleMap,b.moduleLoading,Z,d&&"string"===typeof d.nonce?d.nonce:void 0);a.on("data",function(e){for(var h=0,f=c._rowState,k=c._rowID,l=c._rowTag,g=c._rowLength,t=c._buffer,F=e.length;h<F;){var m=-1;switch(f){case 0:m=e[h++];58===m?f=1:k=k<<4|(96<m?m-87:m-48);continue;case 1:f=e[h];84===f||65===f||67===f||99===f||85===f||83===f||115===f||76===f||108===f||70===f||68===f||78===f||109===f||86===f?(l=f,f=2,h++):64<f&&91>f?(l=f,f=3,h++):(l=0,f=3);
continue;case 2:m=e[h++];44===m?f=4:g=g<<4|(96<m?m-87:m-48);continue;case 3:m=e.indexOf(10,h);break;case 4:m=h+g,m>e.length&&(m=-1)}var p=e.byteOffset+h;if(-1<m)g=new Uint8Array(e.buffer,p,m-h),ya(c,k,l,t,g),h=m,3===f&&h++,g=k=l=f=0,t.length=0;else{e=new Uint8Array(e.buffer,p,e.byteLength-h);t.push(e);g-=e.byteLength;break}}c._rowState=f;c._rowID=k;c._rowTag=l;c._rowLength=g});a.on("error",function(e){T(c,e)});a.on("end",function(){T(c,Error("Connection closed."))});return U(c,0)};
exports.createServerReference=function(a){return oa(a,Z)};
