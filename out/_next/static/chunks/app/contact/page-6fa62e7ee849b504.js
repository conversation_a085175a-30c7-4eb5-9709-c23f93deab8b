(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[327],{6982:function(e,s,t){Promise.resolve().then(t.bind(t,3751))},3751:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return h}});var a=t(3827),i=t(4090),n=t(8371),r=t(2635),l=t(3665),c=t(3251);let o=i.forwardRef(function(e,s){let{title:t,titleId:a,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},n),t?i.createElement("title",{id:a},t):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),d=i.forwardRef(function(e,s){let{title:t,titleId:a,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},n),t?i.createElement("title",{id:a},t):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}),m=i.forwardRef(function(e,s){let{title:t,titleId:a,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},n),t?i.createElement("title",{id:a},t):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var u=t(9626),h=()=>{let{t:e}=(0,r.$G)(["common","home"]),[s,t]=(0,i.useState)({name:"",email:"",phone:"",subject:"",message:""}),[h,x]=(0,i.useState)(!1),f=[{icon:c.Z,title:"Phone",titleHi:"फोन",details:["+91 98765 43210","+91 87654 32109"],description:"Call us for immediate assistance"},{icon:o,title:"Email",titleHi:"ईमेल",details:["<EMAIL>","<EMAIL>"],description:"Send us your queries anytime"},{icon:d,title:"Address",titleHi:"पता",details:["Kashi Vedic Ashram","Dashashwamedh Ghat, Varanasi","Uttar Pradesh 221001, India"],description:"Visit our sacred center"},{icon:m,title:"Hours",titleHi:"समय",details:["Mon-Sat: 6:00 AM - 9:00 PM","Sunday: 6:00 AM - 6:00 PM"],description:"Our consultation hours"}],p=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))},j=async e=>{e.preventDefault(),x(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Thank you for your message! We will get back to you soon."),t({name:"",email:"",phone:"",subject:"",message:""}),x(!1)};return(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10",children:(0,a.jsx)(l.ht,{size:100,className:"text-saffron-400 om-pulse"})}),(0,a.jsx)("div",{className:"absolute bottom-10 right-10",children:(0,a.jsx)(l.pO,{size:120,className:"text-lotus-pink-400"})})]}),(0,a.jsx)("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Contact Us"}),(0,a.jsx)("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Reach out to us for spiritual guidance, consultations, or any questions about our services"})]})})]}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsx)("div",{className:"spiritual-container",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:f.map((e,s)=>{let t=e.icon;return(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,a.jsx)(t,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.title}),(0,a.jsx)("div",{className:"space-y-1 mb-3",children:e.details.map((e,s)=>(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e},s))}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]},e.title)})})})}),(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"spiritual-container",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsx)(n.E.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsx)("h2",{className:"text-2xl font-serif font-bold mb-6 gradient-text",children:"Send Us a Message"}),(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",name:"name",value:s.name,onChange:p,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",name:"phone",value:s.phone,onChange:p,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",name:"email",value:s.email,onChange:p,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,a.jsxs)("select",{name:"subject",value:s.subject,onChange:p,className:"spiritual-input",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a subject"}),(0,a.jsx)("option",{value:"consultation",children:"Consultation Inquiry"}),(0,a.jsx)("option",{value:"puja",children:"Puja Booking"}),(0,a.jsx)("option",{value:"gemstone",children:"Gemstone Consultation"}),(0,a.jsx)("option",{value:"vastu",children:"Vastu Consultation"}),(0,a.jsx)("option",{value:"general",children:"General Inquiry"}),(0,a.jsx)("option",{value:"feedback",children:"Feedback"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,a.jsx)("textarea",{name:"message",value:s.message,onChange:p,rows:6,className:"spiritual-input",placeholder:"Please describe your inquiry in detail...",required:!0})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{type:"submit",disabled:h,className:"spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:h?"Sending...":"Send Message"})})]})]})}),(0,a.jsxs)(n.E.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsx)("h3",{className:"text-xl font-serif font-bold mb-4 gradient-text",children:"Visit Our Sacred Center"}),(0,a.jsx)("div",{className:"bg-gray-200 h-64 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(l.X5,{className:"w-16 h-16 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Interactive Map"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Dashashwamedh Ghat, Varanasi"})]})}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Located in the heart of the holy city of Varanasi, our ashram is easily accessible from all major landmarks. We are just a few minutes walk from the famous Dashashwamedh Ghat."})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsx)("h3",{className:"text-xl font-serif font-bold mb-4 gradient-text",children:"Need Immediate Help?"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(u.Z,{className:"w-5 h-5 text-saffron-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Live Chat"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Available 24/7 for urgent queries"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.Z,{className:"w-5 h-5 text-saffron-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Emergency Consultation"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Call +91 98765 43210"})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsx)("h4",{className:"font-semibold mb-3",children:"Follow Us"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:(0,a.jsx)("span",{className:"text-saffron-600",children:"\uD83D\uDCD8"})}),(0,a.jsx)("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:(0,a.jsx)("span",{className:"text-saffron-600",children:"\uD83D\uDCF7"})}),(0,a.jsx)("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:(0,a.jsx)("span",{className:"text-saffron-600",children:"\uD83D\uDC26"})}),(0,a.jsx)("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:(0,a.jsx)("span",{className:"text-saffron-600",children:"\uD83D\uDCFA"})})]})]})]})]})]})})})]})}},9626:function(e,s,t){"use strict";var a=t(4090);let i=a.forwardRef(function(e,s){let{title:t,titleId:i,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":i},n),t?a.createElement("title",{id:i},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});s.Z=i},3251:function(e,s,t){"use strict";var a=t(4090);let i=a.forwardRef(function(e,s){let{title:t,titleId:i,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":i},n),t?a.createElement("title",{id:i},t):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))});s.Z=i}},function(e){e.O(0,[371,635,665,971,69,744],function(){return e(e.s=6982)}),_N_E=e.O()}]);