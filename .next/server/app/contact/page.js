(()=>{var e={};e.id=327,e.ids=[327],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8432:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=t(482),i=t(9108),r=t(2563),n=t.n(r),l=t(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1215)),"/Users/<USER>/Downloads/astrology/src/app/contact/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/contact/page.tsx"],m="/contact/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3588:(e,s,t)=>{Promise.resolve().then(t.bind(t,942))},942:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5344),i=t(3729),r=t(7292),n=t(9758),l=t(1902),o=t(8360);let c=i.forwardRef(function({title:e,titleId:s,...t},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?i.createElement("title",{id:s},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}),d=i.forwardRef(function({title:e,titleId:s,...t},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?i.createElement("title",{id:s},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}),m=i.forwardRef(function({title:e,titleId:s,...t},a){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?i.createElement("title",{id:s},e):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});var u=t(9147);let x=()=>{let{t:e}=(0,n.$G)(["common","home"]),[s,t]=(0,i.useState)({name:"",email:"",phone:"",subject:"",message:""}),[x,h]=(0,i.useState)(!1),p=[{icon:o.Z,title:"Phone",titleHi:"फोन",details:["+91 98765 43210","+91 87654 32109"],description:"Call us for immediate assistance"},{icon:c,title:"Email",titleHi:"ईमेल",details:["<EMAIL>","<EMAIL>"],description:"Send us your queries anytime"},{icon:d,title:"Address",titleHi:"पता",details:["Kashi Vedic Ashram","Dashashwamedh Ghat, Varanasi","Uttar Pradesh 221001, India"],description:"Visit our sacred center"},{icon:m,title:"Hours",titleHi:"समय",details:["Mon-Sat: 6:00 AM - 9:00 PM","Sunday: 6:00 AM - 6:00 PM"],description:"Our consultation hours"}],g=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))},f=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Thank you for your message! We will get back to you soon."),t({name:"",email:"",phone:"",subject:"",message:""}),h(!1)};return(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[a.jsx("div",{className:"absolute top-10 left-10",children:a.jsx(l.ht,{size:100,className:"text-saffron-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-10 right-10",children:a.jsx(l.pO,{size:120,className:"text-lotus-pink-400"})})]}),a.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[a.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Contact Us"}),a.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Reach out to us for spiritual guidance, consultations, or any questions about our services"})]})})]}),a.jsx("section",{className:"py-16 bg-white",children:a.jsx("div",{className:"spiritual-container",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:p.map((e,s)=>{let t=e.icon;return(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(t,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-lg font-semibold mb-3",children:e.title}),a.jsx("div",{className:"space-y-1 mb-3",children:e.details.map((e,s)=>a.jsx("p",{className:"text-gray-600 text-sm",children:e},s))}),a.jsx("p",{className:"text-xs text-gray-500",children:e.description})]},e.title)})})})}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsx("div",{className:"spiritual-container",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[a.jsx(r.E.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h2",{className:"text-2xl font-serif font-bold mb-6 gradient-text",children:"Send Us a Message"}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),a.jsx("input",{type:"text",name:"name",value:s.name,onChange:g,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),a.jsx("input",{type:"tel",name:"phone",value:s.phone,onChange:g,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),a.jsx("input",{type:"email",name:"email",value:s.email,onChange:g,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,a.jsxs)("select",{name:"subject",value:s.subject,onChange:g,className:"spiritual-input",required:!0,children:[a.jsx("option",{value:"",children:"Select a subject"}),a.jsx("option",{value:"consultation",children:"Consultation Inquiry"}),a.jsx("option",{value:"puja",children:"Puja Booking"}),a.jsx("option",{value:"gemstone",children:"Gemstone Consultation"}),a.jsx("option",{value:"vastu",children:"Vastu Consultation"}),a.jsx("option",{value:"general",children:"General Inquiry"}),a.jsx("option",{value:"feedback",children:"Feedback"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),a.jsx("textarea",{name:"message",value:s.message,onChange:g,rows:6,className:"spiritual-input",placeholder:"Please describe your inquiry in detail...",required:!0})]}),a.jsx("div",{className:"text-center",children:a.jsx("button",{type:"submit",disabled:x,className:"spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Sending...":"Send Message"})})]})]})}),(0,a.jsxs)(r.E.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4 gradient-text",children:"Visit Our Sacred Center"}),a.jsx("div",{className:"bg-gray-200 h-64 rounded-lg flex items-center justify-center mb-4",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(l.X5,{className:"w-16 h-16 text-gray-400 mx-auto mb-2"}),a.jsx("p",{className:"text-gray-500",children:"Interactive Map"}),a.jsx("p",{className:"text-sm text-gray-400",children:"Dashashwamedh Ghat, Varanasi"})]})}),a.jsx("p",{className:"text-gray-600 text-sm",children:"Located in the heart of the holy city of Varanasi, our ashram is easily accessible from all major landmarks. We are just a few minutes walk from the famous Dashashwamedh Ghat."})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4 gradient-text",children:"Need Immediate Help?"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(u.Z,{className:"w-5 h-5 text-saffron-500"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Live Chat"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Available 24/7 for urgent queries"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(o.Z,{className:"w-5 h-5 text-saffron-500"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:"Emergency Consultation"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Call +91 98765 43210"})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[a.jsx("h4",{className:"font-semibold mb-3",children:"Follow Us"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:a.jsx("span",{className:"text-saffron-600",children:"\uD83D\uDCD8"})}),a.jsx("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:a.jsx("span",{className:"text-saffron-600",children:"\uD83D\uDCF7"})}),a.jsx("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:a.jsx("span",{className:"text-saffron-600",children:"\uD83D\uDC26"})}),a.jsx("div",{className:"w-10 h-10 bg-saffron-100 rounded-full flex items-center justify-center cursor-pointer hover:bg-saffron-200 transition-colors",children:a.jsx("span",{className:"text-saffron-600",children:"\uD83D\uDCFA"})})]})]})]})]})]})})})]})}},1215:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>n});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/contact/page.tsx`),{__esModule:i,$$typeof:r}=a,n=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(8432));module.exports=a})();