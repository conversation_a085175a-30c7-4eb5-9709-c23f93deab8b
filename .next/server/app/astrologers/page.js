(()=>{var e={};e.id=765,e.ids=[765],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},905:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(482),r=t(9108),i=t(2563),n=t.n(i),l=t(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["astrologers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,660)),"/Users/<USER>/Downloads/astrology/src/app/astrologers/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/astrologers/page.tsx"],x="/astrologers/page",p={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/astrologers/page",pathname:"/astrologers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1536:(e,s,t)=>{Promise.resolve().then(t.bind(t,7897))},7897:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var a=t(5344),r=t(3729),i=t(7292),n=t(1902);let l=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))}),o=()=>(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[a.jsx("div",{className:"absolute top-10 left-10",children:a.jsx(n.ht,{size:100,className:"text-saffron-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-10 right-10",children:a.jsx(n.pO,{size:120,className:"text-lotus-pink-400"})})]}),a.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[a.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Our Expert Astrologers"}),a.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Connect with experienced and certified astrologers for personalized guidance"})]})})]}),a.jsx("section",{className:"py-16 bg-white",children:a.jsx("div",{className:"spiritual-container",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{name:"Pandit Raj Kumar Sharma",specialization:"Vedic Astrology & Kundli Analysis",experience:"15+ years",rating:4.9,reviews:1250,languages:["Hindi","English","Sanskrit"],available:!0},{name:"Acharya Priya Devi",specialization:"Palmistry & Tarot Reading",experience:"12+ years",rating:4.8,reviews:980,languages:["Hindi","English"],available:!0},{name:"Guru Vikash Joshi",specialization:"Numerology & Vastu Shastra",experience:"20+ years",rating:4.9,reviews:1500,languages:["Hindi","English","Gujarati"],available:!1}].map((e,s)=>(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:`spiritual-card-enhanced ${e.available?"":"opacity-60"}`,children:[(0,a.jsxs)("div",{className:"relative text-center",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(n.UI,{className:"w-10 h-10 text-white"})}),e.available?a.jsx("div",{className:"absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded-full",children:"Online"}):a.jsx("div",{className:"absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:"Busy"})]}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h3",{className:"text-lg font-semibold mb-1",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600 mb-2",children:e.specialization}),(0,a.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[a.jsx(l,{className:"w-4 h-4 text-yellow-400 fill-current"}),a.jsx("span",{className:"text-sm font-medium ml-1",children:e.rating}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 ml-1",children:["(",e.reviews," reviews)"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-3",children:[e.experience," experience"]}),a.jsx("div",{className:"flex flex-wrap gap-1 justify-center mb-4",children:e.languages.map(e=>a.jsx("span",{className:"text-xs bg-saffron-100 text-saffron-700 px-2 py-1 rounded",children:e},e))}),e.available&&a.jsx("button",{className:"w-full spiritual-button text-sm py-2",children:"Consult Now"})]})]},e.name))})})})]})},660:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/astrologers/page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(905));module.exports=a})();