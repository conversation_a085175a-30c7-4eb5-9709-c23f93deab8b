"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[665],{3665:function(r,e,t){t.d(e,{Fk:function(){return h},N8:function(){return x},Pz:function(){return y},UI:function(){return l},UW:function(){return n},X5:function(){return a},f9:function(){return p},ht:function(){return o},pO:function(){return s}});var c=t(3827);t(4090);var i=t(8371);let o=r=>{let{className:e="",size:t=24}=r;return(0,c.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("path",{d:"M35.5 25.2c-4.8 0-8.7 3.9-8.7 8.7s3.9 8.7 8.7 8.7c4.8 0 8.7-3.9 8.7-8.7s-3.9-8.7-8.7-8.7zm0 14.4c-3.1 0-5.7-2.5-5.7-5.7s2.5-5.7 5.7-5.7 5.7 2.5 5.7 5.7-2.6 5.7-5.7 5.7z"}),(0,c.jsx)("path",{d:"M75.8 45.2c-2.1-7.1-8.4-12.2-15.9-12.2-6.2 0-11.6 3.4-14.4 8.4-1.4-2.1-3.8-3.5-6.5-3.5-4.3 0-7.8 3.5-7.8 7.8 0 1.8.6 3.4 1.6 4.7-3.2 2.8-5.2 6.9-5.2 11.5 0 8.4 6.8 15.2 15.2 15.2 3.9 0 7.4-1.5 10.1-3.9 2.7 2.4 6.2 3.9 10.1 3.9 8.4 0 15.2-6.8 15.2-15.2 0-3.1-.9-6-2.4-8.7z"}),(0,c.jsx)("circle",{cx:"20",cy:"75",r:"8",opacity:"0.7"}),(0,c.jsx)("path",{d:"M85 70c0 8.3-6.7 15-15 15s-15-6.7-15-15 6.7-15 15-15 15 6.7 15 15zm-3 0c0-6.6-5.4-12-12-12s-12 5.4-12 12 5.4 12 12 12 12-5.4 12-12z"})]})},s=r=>{let{className:e="",size:t=24}=r;return(0,c.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("path",{d:"M50 85c-2.8 0-5-2.2-5-5V65c0-2.8 2.2-5 5-5s5 2.2 5 5v15c0 2.8-2.2 5-5 5z"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(-30 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(-60 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(-90 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(-120 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(-150 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(180 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(150 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(120 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(90 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(60 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",transform:"rotate(30 50 45)",opacity:"0.8"}),(0,c.jsx)("ellipse",{cx:"50",cy:"45",rx:"8",ry:"15",opacity:"0.8"}),(0,c.jsx)("circle",{cx:"50",cy:"45",r:"6",fill:"currentColor"})]})},l=r=>{let{className:e="",size:t=24}=r;return(0,c.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("ellipse",{cx:"50",cy:"70",rx:"25",ry:"15"}),(0,c.jsx)("ellipse",{cx:"50",cy:"65",rx:"20",ry:"10",fill:"none",stroke:"currentColor",strokeWidth:"2"}),(0,c.jsx)("path",{d:"M50 45c-2 0-4 2-4 4v16h8V49c0-2-2-4-4-4z"}),(0,c.jsx)("ellipse",{cx:"50",cy:"40",rx:"6",ry:"8",opacity:"0.7"}),(0,c.jsx)("path",{d:"M44 40c0-3.3 2.7-6 6-6s6 2.7 6 6-2.7 6-6 6-6-2.7-6-6z",fill:"none",stroke:"currentColor",strokeWidth:"1"}),(0,c.jsx)("circle",{cx:"50",cy:"35",r:"2",opacity:"0.9"})]})},n=r=>{let{className:e="",size:t=24}=r;return(0,c.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.3"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"30",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.5"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.7"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"10",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.9"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"5"}),Array.from({length:8},(r,e)=>{let t=45*e*(Math.PI/180),i=50+15*Math.cos(t),o=50+15*Math.sin(t),s=50+35*Math.cos(t),l=50+35*Math.sin(t);return(0,c.jsxs)("g",{children:[(0,c.jsx)("line",{x1:i,y1:o,x2:s,y2:l,stroke:"currentColor",strokeWidth:"1",opacity:"0.6"}),(0,c.jsx)("circle",{cx:s,cy:l,r:"3",opacity:"0.8"})]},e)})]})},a=r=>{let{className:e="",size:t=24}=r;return(0,c.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("rect",{x:"20",y:"60",width:"60",height:"30"}),(0,c.jsx)("rect",{x:"15",y:"55",width:"70",height:"8"}),(0,c.jsx)("polygon",{points:"50,20 25,55 75,55"}),(0,c.jsx)("rect",{x:"45",y:"65",width:"10",height:"20"}),(0,c.jsx)("rect",{x:"30",y:"65",width:"8",height:"15"}),(0,c.jsx)("rect",{x:"62",y:"65",width:"8",height:"15"}),(0,c.jsx)("circle",{cx:"50",cy:"40",r:"3"}),(0,c.jsx)("rect",{x:"47",y:"15",width:"6",height:"10"}),(0,c.jsx)("circle",{cx:"50",cy:"12",r:"3"})]})},x=r=>{let{className:e="",size:t=24,animated:o=!1}=r,s=o?i.E.svg:"svg";return(0,c.jsxs)(s,{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",...o&&{animate:{rotate:[0,90,180,270,360]},transition:{duration:15,repeat:1/0,ease:"linear"}},children:[(0,c.jsx)("rect",{x:"10",y:"10",width:"80",height:"80",fill:"none",stroke:"currentColor",strokeWidth:"2"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"35",fill:"none",stroke:"currentColor",strokeWidth:"1"}),(0,c.jsx)("polygon",{points:"50,25 65,60 35,60",fill:"none",stroke:"currentColor",strokeWidth:"1"}),(0,c.jsx)("polygon",{points:"50,75 35,40 65,40",fill:"none",stroke:"currentColor",strokeWidth:"1"}),(0,c.jsx)("circle",{cx:"50",cy:"50",r:"8"}),(0,c.jsx)("circle",{cx:"25",cy:"25",r:"3"}),(0,c.jsx)("circle",{cx:"75",cy:"25",r:"3"}),(0,c.jsx)("circle",{cx:"25",cy:"75",r:"3"}),(0,c.jsx)("circle",{cx:"75",cy:"75",r:"3"})]})},y=r=>{let{className:e="",size:t=24,animated:o=!1}=r,s=o?i.E.svg:"svg";return(0,c.jsxs)(s,{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",...o&&{animate:{scale:[1,1.05,1],filter:["brightness(1)","brightness(1.2)","brightness(1)"]},transition:{duration:5,repeat:1/0,ease:"easeInOut"}},children:[(0,c.jsx)("ellipse",{cx:"50",cy:"80",rx:"20",ry:"8",fill:"currentColor"}),(0,c.jsx)("rect",{x:"35",y:"50",width:"30",height:"30",rx:"5",fill:"currentColor"}),(0,c.jsx)("rect",{x:"45",y:"40",width:"10",height:"15",fill:"currentColor"}),(0,c.jsx)("circle",{cx:"50",cy:"35",r:"8",fill:"currentColor",opacity:"0.9"}),[void 0,void 0,void 0,void 0,void 0].map((r,e)=>(0,c.jsx)(i.E.ellipse,{cx:40+5*e,cy:"25",rx:"2",ry:"8",fill:"currentColor",opacity:"0.7",...o&&{animate:{rotate:[0,5,-5,0],opacity:[.7,1,.7]},transition:{duration:3,repeat:1/0,delay:.2*e,ease:"easeInOut"}}},e)),(0,c.jsx)("path",{d:"M35 45 Q50 35 65 45",fill:"none",stroke:"currentColor",strokeWidth:"2",opacity:"0.6"}),o&&(0,c.jsx)(i.E.circle,{cx:"50",cy:"50",r:"25",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.3",animate:{scale:[1,1.1,1],opacity:[.3,.6,.3]},transition:{duration:4,repeat:1/0,ease:"easeInOut"}})]})},h=r=>{let{className:e="",size:t=24,animated:o=!1}=r,s=o?i.E.svg:"svg";return(0,c.jsxs)(s,{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",...o&&{animate:{scale:[1,1.03,1.06,1.03,1],rotate:[0,1,0,-1,0]},transition:{duration:6,repeat:1/0,ease:"easeInOut"}},children:[(0,c.jsx)("path",{d:"M30 70c0-20 10-35 20-35s20 15 20 35c0 10-5 15-10 15H40c-5 0-10-5-10-15z"}),(0,c.jsx)("path",{d:"M50 40c-8 0-15 7-15 15s7 15 15 15",fill:"none",stroke:"currentColor",strokeWidth:"2",opacity:"0.6"}),(0,c.jsx)("path",{d:"M50 45c-5 0-10 5-10 10s5 10 10 10",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.4"}),(0,c.jsx)("ellipse",{cx:"50",cy:"30",rx:"8",ry:"5",fill:"currentColor",opacity:"0.8"}),o&&(0,c.jsx)(c.Fragment,{children:[void 0,void 0,void 0].map((r,e)=>(0,c.jsx)(i.E.ellipse,{cx:"50",cy:"25",rx:15+8*e,ry:8+4*e,fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.3",animate:{scale:[1,1.2,1],opacity:[.3,.6,0]},transition:{duration:2,repeat:1/0,delay:.3*e,ease:"easeOut"}},e))})]})},p=r=>{let{className:e="",size:t=24,animated:o=!1}=r,s=o?i.E.svg:"svg";return(0,c.jsxs)(s,{width:t,height:t,viewBox:"0 0 100 100",className:e,fill:"currentColor",children:[(0,c.jsx)("circle",{cx:"50",cy:"60",r:"25",fill:"currentColor",opacity:"0.8"}),(0,c.jsx)("circle",{cx:"50",cy:"60",r:"20",fill:"none",stroke:"currentColor",strokeWidth:"2"}),[void 0,void 0,void 0,void 0,void 0].map((r,e)=>{let t=72*e*(Math.PI/180),s=50+12*Math.cos(t),l=60+12*Math.sin(t);return(0,c.jsxs)("g",{children:[(0,c.jsx)("ellipse",{cx:s,cy:l,rx:"3",ry:"2",fill:"currentColor"}),(0,c.jsx)(i.E.ellipse,{cx:s,cy:l-3,rx:"2",ry:"4",fill:"currentColor",opacity:"0.9",...o&&{animate:{scale:[1,1.2,.8,1],opacity:[.9,1,.7,.9]},transition:{duration:2,repeat:1/0,delay:.2*e,ease:"easeInOut"}}})]},e)}),(0,c.jsx)("rect",{x:"48",y:"85",width:"4",height:"10",fill:"currentColor"}),o&&(0,c.jsx)(i.E.circle,{cx:"50",cy:"60",r:"30",fill:"none",stroke:"currentColor",strokeWidth:"1",opacity:"0.2",animate:{scale:[1,1.1,1],opacity:[.2,.5,.2]},transition:{duration:3,repeat:1/0,ease:"easeInOut"}})]})}}}]);