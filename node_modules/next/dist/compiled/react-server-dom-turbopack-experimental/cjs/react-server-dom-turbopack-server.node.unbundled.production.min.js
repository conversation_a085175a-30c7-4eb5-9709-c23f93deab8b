/*
 React
 react-server-dom-turbopack-server.node.unbundled.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("util");require("crypto");var ba=require("async_hooks"),ca=require("react"),da=require("react-dom"),l=null,m=0,p=!0;function q(a,b){a=a.write(b);p=p&&a}
function ea(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,fa.encode(b));else{var d=l;0<m&&(d=l.subarray(m));d=fa.encodeInto(b,d);var c=d.read;m+=d.written;c<b.length&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=fa.encodeInto(b.slice(c),l).written);2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,b)):(d=l.length-m,d<b.byteLength&&
(0===d?q(a,l):(l.set(b.subarray(0,d),m),m+=d,q(a,l),b=b.subarray(d)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)));return p}var fa=new aa.TextEncoder,r=Symbol.for("react.client.reference"),ha=Symbol.for("react.server.reference");function t(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var ia=Function.prototype.bind,ja=Array.prototype.slice;
function ka(){var a=ia.apply(this,arguments);if(this.$$typeof===ha){var b=ja.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:ha},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ka}})}return a}
var la=Promise.prototype,ma={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},na={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=t(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=t({},a.$$id,!0),e=new Proxy(c,na);a.status="fulfilled";a.value=e;return a.then=t(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=t(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ma));return c},getPrototypeOf:function(){return la},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},va={prefetchDNS:oa,preconnect:pa,preload:qa,preloadModule:ra,preinitStyle:sa,preinitScript:ta,preinitModuleScript:ua};function oa(a){if("string"===typeof a&&a){var b=v();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),x(b,"D",a))}}}function pa(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?x(d,"C",[a,b]):x(d,"C",a))}}}
function qa(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=y(d))?x(c,"L",[a,b,d]):x(c,"L",[a,b]))}}}function ra(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"m",[a,b]):x(d,"m",a)}}}
function sa(a,b,d){if("string"===typeof a){var c=v();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=y(d))?x(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?x(c,"S",[a,b]):x(c,"S",a)}}}function ta(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"X",[a,b]):x(d,"X",a)}}}function ua(a,b){if("string"===typeof a){var d=v();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=y(b))?x(d,"M",[a,b]):x(d,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var wa=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,xa=new ba.AsyncLocalStorage,z=Symbol.for("react.element"),ya=Symbol.for("react.fragment"),za=Symbol.for("react.provider"),Aa=Symbol.for("react.server_context"),Ba=Symbol.for("react.forward_ref"),Ca=Symbol.for("react.suspense"),Da=Symbol.for("react.suspense_list"),Ea=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Fa=Symbol.for("react.default_value"),Ga=Symbol.for("react.memo_cache_sentinel"),B=Symbol.for("react.postpone"),
Ha=Symbol.iterator,C=null;function Ia(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ia(a,d);b.context._currentValue=b.value}}}function Ja(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Ja(a)}
function Ka(a){var b=a.parent;null!==b&&Ka(b);a.context._currentValue=a.value}function La(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ia(a,b):La(a,b)}
function Ma(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Ia(a,d):Ma(a,d);b.context._currentValue=b.value}function Na(a){var b=C;b!==a&&(null===b?Ka(a):null===a?Ja(b):b.depth===a.depth?Ia(b,a):b.depth>a.depth?La(b,a):Ma(b,a),C=a)}function Oa(a,b){var d=a._currentValue;a._currentValue=b;var c=C;return C=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var Pa=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Qa(){}function Ra(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Qa,Qa),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Sa=b;throw Pa;}}var Sa=null;
function Ta(){if(null===Sa)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Sa;Sa=null;return a}var D=null,Ua=0,E=null;function Va(){var a=E;E=null;return a}function Wa(a){return a._currentValue}
var $a={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:F,useTransition:F,readContext:Wa,useContext:Wa,useReducer:F,useRef:F,useState:F,useInsertionEffect:F,useLayoutEffect:F,useImperativeHandle:F,useEffect:F,useId:Xa,useSyncExternalStore:F,useCacheRefresh:function(){return Ya},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ga;return b},use:Za};
function F(){throw Error("This Hook is not supported in Server Components.");}function Ya(){throw Error("Refreshing the cache is not supported in Server Components.");}function Xa(){if(null===D)throw Error("useId can only be used while React is rendering");var a=D.identifierCount++;return":"+D.identifierPrefix+"S"+a.toString(32)+":"}
function Za(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ua;Ua+=1;null===E&&(E=[]);return Ra(E,a,b)}if(a.$$typeof===Aa)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function ab(){return(new AbortController).signal}function bb(){var a=v();return a?a.cache:new Map}
var cb={getCacheSignal:function(){var a=bb(),b=a.get(ab);void 0===b&&(b=ab(),a.set(ab,b));return b},getCacheForType:function(a){var b=bb(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},db=Array.isArray,eb=Object.getPrototypeOf;function fb(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function gb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(db(a))return"[...]";a=fb(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function hb(a){if("string"===typeof a)return a;switch(a){case Ca:return"Suspense";case Da:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case Ba:return hb(a.render);case Ea:return hb(a.type);case A:var b=a._payload;a=a._init;try{return hb(a(b))}catch(d){}}return""}
function G(a,b){var d=fb(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(db(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?G(g):gb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===z)e="<"+hb(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?G(h):
gb(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var ib=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,jb=ib.ContextRegistry,I=ca.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!I)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var kb=Object.prototype,J=JSON.stringify,lb=I.TaintRegistryObjects,K=I.TaintRegistryValues,mb=I.TaintRegistryByteLengths,nb=I.TaintRegistryPendingRequests,ob=I.ReactCurrentCache,pb=ib.ReactCurrentDispatcher;function L(a){throw Error(a);}
function qb(a){a=a.taintCleanupQueue;nb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=K.get(d);void 0!==c&&(1===c.count?K.delete(d):c.count--)}a.length=0}function rb(a){console.error(a)}function sb(){}
function tb(a,b,d,c,e,f){if(null!==ob.current&&ob.current!==cb)throw Error("Currently React only supports one RSC renderer at a time.");wa.current=va;ob.current=cb;var g=new Set,k=[],h=[];nb.add(h);var n=new Set,w={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:n,abortableTasks:g,pingedTasks:k,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:h,onError:void 0===d?rb:d,onPostpone:void 0===f?sb:f,toJSON:function(u,H){return ub(w,this,u,H)}};w.pendingChunks++;b=vb(c);a=wb(w,a,b,g);k.push(a);return w}var M=null;function v(){if(M)return M;var a=xa.getStore();return a?a:null}var xb={};
function yb(a,b){a.pendingChunks++;var d=wb(a,null,C,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,zb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===B?(N(a,c.message),O(a,d.id)):(c=P(a,c),Q(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;zb(a,d)},function(e){"object"===typeof e&&null!==e&&e.$$typeof===B?(N(a,e.message),O(a,d.id)):(d.status=4,e=P(a,e),Q(a,d.id,e));a.abortableTasks.delete(d);null!==a.destination&&R(a,a.destination)});return d.id}function x(a,b,d){d=J(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;a.completedHintChunks.push(b+d+"\n");Ab(a)}function Bb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function Cb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:Bb}}
function S(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[z,b,d,e];Ua=0;E=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:Cb(e):e}if("string"===typeof b)return[z,b,d,e];if("symbol"===typeof b)return b===ya?e.children:[z,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[z,b,d,e];switch(b.$$typeof){case A:var g=
b._init;b=g(b._payload);return S(a,b,d,c,e,f);case Ba:return a=b.render,Ua=0,E=f,a(e,void 0);case Ea:return S(a,b.type,d,c,e,f);case za:return Oa(b._context,e.value),[z,b,d,{value:e.value,children:e.children,__pop:xb}]}}throw Error("Unsupported Server Component type: "+gb(b));}function zb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Db(a)}))}
function wb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return zb(a,e)},thenableState:null};c.add(e);return e}function T(a){return"$"+a.toString(16)}function Eb(a,b,d){a=J(d);return b.toString(16)+":"+a+"\n"}
function Fb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===z&&"1"===d?"$L"+g.toString(16):T(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var n=k[h];if(n)g=n.name;else{var w=h.lastIndexOf("#");-1!==w&&(g=h.slice(w+1),n=k[h.slice(0,w)]);if(!n)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===c.$$async?[n.id,n.chunks,g,1]:[n.id,n.chunks,
g];a.pendingChunks++;var H=a.nextChunkId++,Zb=J(u),$b=H.toString(16)+":I"+Zb+"\n";a.completedImportChunks.push($b);f.set(e,H);return b[0]===z&&"1"===d?"$L"+H.toString(16):T(H)}catch(ac){return a.pendingChunks++,b=a.nextChunkId++,d=P(a,ac),Q(a,b,d),T(b)}}function U(a,b){a.pendingChunks++;b=wb(a,b,C,a.abortableTasks);Gb(a,b);return b.id}
function V(a,b,d){if(mb.has(d.byteLength)){var c=K.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&L(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;d=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);var e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";a.completedRegularChunks.push(b,d);return T(c)}var W=!1;
function ub(a,b,d,c){switch(c){case z:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===z||c.$$typeof===A);)try{switch(c.$$typeof){case z:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=U(a,c);return T(g)}if(W===c)W=null;else return T(f)}else e.set(c,-1);var k=c;c=S(a,k.type,k.key,k.ref,k.props,null);break;case A:var h=c._init;c=h(c._payload)}}catch(n){d=n===Pa?Ta():n;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=wb(a,c,C,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Va(),"$L"+a.id.toString(16);if(d.$$typeof===B)return c=d,a.pendingChunks++,d=a.nextChunkId++,N(a,c.message),O(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=P(a,d);Q(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=lb.get(c);void 0!==e&&L(e);if(c.$$typeof===r)return Fb(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(W===c)W=null;else return"$@"+e.toString(16);a=yb(a,c);b.set(c,
a);return"$@"+a.toString(16)}if(c.$$typeof===za)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=Eb(a,d,"$P"+c),a.completedRegularChunks.push(c)),T(d);if(c===xb){a=C;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===Fa?a.context._defaultValue:c;C=a.parent;return}if(void 0!==e){if(-1===e)return a=U(a,c),T(a);if(W===c)W=null;else return T(e)}else b.set(c,
-1);if(db(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+U(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+U(a,c).toString(16)}if(c instanceof ArrayBuffer)return V(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return V(a,"C",c);if(c instanceof
Uint8Array)return V(a,"c",c);if(c instanceof Uint8ClampedArray)return V(a,"U",c);if(c instanceof Int16Array)return V(a,"S",c);if(c instanceof Uint16Array)return V(a,"s",c);if(c instanceof Int32Array)return V(a,"L",c);if(c instanceof Uint32Array)return V(a,"l",c);if(c instanceof Float32Array)return V(a,"F",c);if(c instanceof Float64Array)return V(a,"D",c);if(c instanceof BigInt64Array)return V(a,"N",c);if(c instanceof BigUint64Array)return V(a,"m",c);if(c instanceof DataView)return V(a,"V",c);null===
c||"object"!==typeof c?a=null:(a=Ha&&c[Ha]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=eb(c);if(a!==kb&&(null===a||null!==eb(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=K.get(c);void 0!==e&&L(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,
b="string"===typeof c?Buffer.byteLength(c,"utf8"):c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",a.completedRegularChunks.push(b,c),T(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=lb.get(c);void 0!==e&&L(e);if(c.$$typeof===r)return Fb(a,b,d,c);if(c.$$typeof===
ha)return d=a.writtenServerReferences,b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=U(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+G(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+
G(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return T(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+G(b,d));a.pendingChunks++;d=a.nextChunkId++;b=Eb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return T(d)}if("bigint"===typeof c)return a=K.get(c),void 0!==a&&L(a.message),"$n"+c.toString(10);
throw Error("Type "+typeof c+" is not supported in Client Component props."+G(b,d));}function N(a,b){a=a.onPostpone;a(b)}function P(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Hb(a,b){qb(a);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function O(a,b){b=b.toString(16)+":P\n";a.completedErrorChunks.push(b)}function Q(a,b,d){d={digest:d};b=b.toString(16)+":E"+J(d)+"\n";a.completedErrorChunks.push(b)}
function Gb(a,b){if(0===b.status){Na(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===z){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=S(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===z;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=S(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;W=d;var g=J(d,a.toJSON),k=f.toString(16)+":"+g+"\n";a.completedRegularChunks.push(k);
a.abortableTasks.delete(b);b.status=1}catch(h){f=h===Pa?Ta():h;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Va();return}if(f.$$typeof===B){a.abortableTasks.delete(b);b.status=4;N(a,f.message);O(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=P(a,f);Q(a,b.id,f)}}}
function Db(a){var b=pb.current;pb.current=$a;var d=M;D=M=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Gb(a,c[e]);null!==a.destination&&R(a,a.destination)}catch(f){P(a,f),Hb(a,f)}finally{pb.current=b,D=null,M=d}}
function R(a,b){l=new Uint8Array(2048);m=0;p=!0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)if(a.pendingChunks--,!ea(b,d[c])){a.destination=null;c++;break}d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)if(!ea(b,e[c])){a.destination=null;c++;break}e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)if(a.pendingChunks--,!ea(b,f[c])){a.destination=null;c++;break}f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)if(a.pendingChunks--,!ea(b,
g[c])){a.destination=null;c++;break}g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,p=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&(qb(a),b.end())}function Ib(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return xa.run(a,Db,a)})}function Ab(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return R(a,b)})}}
function Jb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{R(a,b)}catch(d){P(a,d),Hb(a,d)}}}
function Kb(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===B)N(a,b.message),O(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=P(a,e);Q(a,c,f,e)}d.forEach(function(g){g.status=3;var k=T(c);g=Eb(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&R(a,a.destination)}catch(g){P(a,g),Hb(a,g)}}
function vb(a){if(a){var b=C;Na(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!jb[e]){var f={$$typeof:Aa,_currentValue:Fa,_currentValue2:Fa,_defaultValue:Fa,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:za,_context:f};jb[e]=f}Oa(jb[e],c)}a=C;Na(b);return a}return null}function Lb(a,b){var d=b.lastIndexOf("#");a=b.slice(0,d);b=b.slice(d+1);return{specifier:a,name:b}}var Mb=new Map;
function Nb(a){var b=Mb.get(a.specifier);if(b)return"fulfilled"===b.status?null:b;var d=import(a.specifier);a.async&&(d=d.then(function(c){return c.default}));d.then(function(c){var e=d;e.status="fulfilled";e.value=c},function(c){var e=d;e.status="rejected";e.reason=c});Mb.set(a.specifier,d);return d}function X(a){var b=Mb.get(a.specifier);if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a.name?b:""===a.name?b.default:b[a.name]}
function Ob(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Ob.prototype=Object.create(Promise.prototype);Ob.prototype.then=function(a,b){switch(this.status){case "resolved_model":Pb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function Qb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}function Rb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Qb(d,b)}}function Sb(a,b,d,c,e,f){var g=Lb(a._bundlerConfig,b);a=Nb(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=X(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return X(g)});else return X(g);d.then(Tb(c,e,f),Ub(c));return null}var Vb=null,Y=null;
function Pb(a){var b=Vb,d=Y;Vb=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{Vb=b,Y=d}}function Wb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Rb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Ob("resolved_model",c,null,a):new Ob("pending",null,null,a),d.set(b,c));return c}function Tb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Qb(e,c.value))}}function Ub(a){return function(b){return Rb(a,b)}}
function Xb(a,b){a=Z(a,b);"resolved_model"===a.status&&Pb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Yb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Xb(a,c),Sb(a,c.id,c.bound,Vb,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Xb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Pb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=Vb,a.then(Tb(c,b,d),Ub(c)),null;default:throw a.reason;}}return c}
function bc(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Yb(e,this,f,g):g}};return e}
function cc(a,b,d){a._formData.append(b,d);var c=a._prefix;if(b.startsWith(c)&&(a=a._chunks,b=+b.slice(c.length),(b=a.get(b))&&"pending"===b.status&&(c=b.value,a=b.reason,b.status="resolved_model",b.value=d,null!==c)))switch(Pb(b),b.status){case "fulfilled":Qb(c,b.value);break;case "pending":case "blocked":b.value=c;b.reason=a;break;case "rejected":a&&Qb(a,b.reason)}}function dc(a){Wb(a,Error("Connection closed."))}
function ec(a,b,d){var c=Lb(a,b);a=Nb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=X(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return X(c)}):Promise.resolve(X(c))}function fc(a,b,d){a=bc(b,d,a);dc(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function gc(a,b){return function(){return Jb(b,a)}}exports.createClientModuleProxy=function(a){a=t({},a,!1);return new Proxy(a,na)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=fc(a,b,e),c=ec(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=ec(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=bc(b,"",a);b=Z(a,0);dc(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var d=bc(b,""),c=0,e=[];a.on("field",function(f,g){0<c?e.push(f,g):cc(d,f,g)});a.on("file",function(f,g,k){var h=k.filename,n=k.mimeType;if("base64"===k.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");c++;var w=[];g.on("data",function(u){w.push(u)});g.on("end",function(){var u=
new Blob(w,{type:n});d._formData.append(f,u,h);c--;if(0===c){for(u=0;u<e.length;u+=2)cc(d,e[u],e[u+1]);e.length=0}})});a.on("finish",function(){dc(d)});a.on("error",function(f){Wb(d,f)});return Z(d,0)};exports.registerClientReference=function(a,b,d){return t(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:ha},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:ka}})};
exports.renderToPipeableStream=function(a,b,d){var c=tb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0),e=!1;Ib(c);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;Jb(c,f);f.on("drain",gc(f,c));return f},abort:function(f){Kb(c,f)}}};

//# sourceMappingURL=react-server-dom-turbopack-server.node.unbundled.production.min.js.map
