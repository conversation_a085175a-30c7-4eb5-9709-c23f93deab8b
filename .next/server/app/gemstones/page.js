(()=>{var e={};e.id=10,e.ids=[10],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9854:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var n=s(482),a=s(9108),i=s(2563),r=s.n(i),o=s(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["gemstones",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5300)),"/Users/<USER>/Downloads/astrology/src/app/gemstones/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/gemstones/page.tsx"],m="/gemstones/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/gemstones/page",pathname:"/gemstones",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5224:(e,t,s)=>{Promise.resolve().then(s.bind(s,8657))},8657:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var n=s(5344);s(3729);var a=s(7292),i=s(1902);let r=()=>(0,n.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,n.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,n.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[n.jsx("div",{className:"absolute top-10 left-10",children:n.jsx(i.ht,{size:100,className:"text-saffron-400 om-pulse"})}),n.jsx("div",{className:"absolute bottom-10 right-10",children:n.jsx(i.pO,{size:120,className:"text-lotus-pink-400"})})]}),n.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,n.jsxs)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[n.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Gemstone Consultation"}),n.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Discover the perfect gemstones to enhance your spiritual energy and well-being"})]})})]}),n.jsx("section",{className:"py-16 bg-white",children:n.jsx("div",{className:"spiritual-container",children:n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{name:"Ruby",nameHi:"माणिक्य",planet:"Sun",benefits:"Leadership, Confidence, Health"},{name:"Pearl",nameHi:"मोती",planet:"Moon",benefits:"Peace, Emotional Balance, Intuition"},{name:"Red Coral",nameHi:"मूंगा",planet:"Mars",benefits:"Courage, Energy, Protection"},{name:"Emerald",nameHi:"पन्ना",planet:"Mercury",benefits:"Intelligence, Communication, Business"},{name:"Yellow Sapphire",nameHi:"पुखराज",planet:"Jupiter",benefits:"Wisdom, Prosperity, Spirituality"},{name:"Diamond",nameHi:"हीरा",planet:"Venus",benefits:"Love, Luxury, Artistic Abilities"},{name:"Blue Sapphire",nameHi:"नीलम",planet:"Saturn",benefits:"Discipline, Focus, Success"},{name:"Hessonite",nameHi:"गोमेद",planet:"Rahu",benefits:"Clarity, Protection from Negativity"},{name:"Cat's Eye",nameHi:"लहसुनिया",planet:"Ketu",benefits:"Spiritual Growth, Intuition"}].map((e,t)=>(0,n.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"spiritual-card-enhanced text-center",children:[n.jsx("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:n.jsx("span",{className:"text-2xl",children:"\uD83D\uDC8E"})}),n.jsx("h3",{className:"text-xl font-serif font-bold mb-2",children:e.name}),n.jsx("p",{className:"text-sm text-gray-500 mb-3",children:e.nameHi}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Planet:"})," ",e.planet]}),(0,n.jsxs)("div",{children:[n.jsx("span",{className:"font-medium",children:"Benefits:"})," ",e.benefits]})]}),n.jsx("button",{className:"mt-4 spiritual-button text-sm py-2 px-4",children:"Learn More"})]},e.name))})})})]})},5300:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>r});let n=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/gemstones/page.tsx`),{__esModule:a,$$typeof:i}=n,r=n.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[651,713],()=>s(9854));module.exports=n})();