<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="mandala" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
      <circle cx="100" cy="100" r="80" fill="none" stroke="currentColor" stroke-width="1" opacity="0.3"/>
      <circle cx="100" cy="100" r="60" fill="none" stroke="currentColor" stroke-width="1" opacity="0.4"/>
      <circle cx="100" cy="100" r="40" fill="none" stroke="currentColor" stroke-width="1" opacity="0.5"/>
      <circle cx="100" cy="100" r="20" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6"/>
      <circle cx="100" cy="100" r="5" fill="currentColor" opacity="0.7"/>
      
      <!-- Petals -->
      <g opacity="0.4">
        <ellipse cx="100" cy="60" rx="8" ry="20" fill="currentColor"/>
        <ellipse cx="100" cy="140" rx="8" ry="20" fill="currentColor"/>
        <ellipse cx="60" cy="100" rx="20" ry="8" fill="currentColor"/>
        <ellipse cx="140" cy="100" rx="20" ry="8" fill="currentColor"/>
        <ellipse cx="129" cy="71" rx="8" ry="20" fill="currentColor" transform="rotate(45 129 71)"/>
        <ellipse cx="71" cy="129" rx="8" ry="20" fill="currentColor" transform="rotate(45 71 129)"/>
        <ellipse cx="71" cy="71" rx="8" ry="20" fill="currentColor" transform="rotate(-45 71 71)"/>
        <ellipse cx="129" cy="129" rx="8" ry="20" fill="currentColor" transform="rotate(-45 129 129)"/>
      </g>
    </pattern>
  </defs>
  <rect width="200" height="200" fill="url(#mandala)"/>
</svg>
