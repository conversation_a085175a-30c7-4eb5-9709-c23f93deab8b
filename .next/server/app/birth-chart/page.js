(()=>{var e={};e.id=588,e.ids=[588],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},375:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(482),i=t(9108),n=t(2563),r=t.n(n),l=t(8300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d=["",{children:["birth-chart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7359)),"/Users/<USER>/Downloads/astrology/src/app/birth-chart/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Downloads/astrology/src/app/birth-chart/page.tsx"],m="/birth-chart/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/birth-chart/page",pathname:"/birth-chart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4042:(e,s,t)=>{Promise.resolve().then(t.bind(t,1167))},1167:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(5344),i=t(3729),n=t(7292),r=t(9758),l=t(1902);let c=()=>{let{t:e}=(0,r.$G)(["common","home"]),[s,t]=(0,i.useState)({name:"",gender:"",dateOfBirth:"",timeOfBirth:"",placeOfBirth:"",latitude:"",longitude:""}),[c,d]=(0,i.useState)(!1),[o,m]=(0,i.useState)("chart"),x=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))};return(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[a.jsx("div",{className:"absolute top-10 left-10",children:a.jsx(l.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-10 right-10",children:a.jsx(l.pO,{size:120,className:"text-lotus-pink-400"})}),a.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:a.jsx(l.UW,{size:200,className:"text-divine-purple-300 mandala-rotate"})})]}),a.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[a.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Birth Chart Analysis"}),a.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Comprehensive natal chart calculations with detailed planetary positions, house placements, and aspect analysis"})]})})]}),a.jsx("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"spiritual-container",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Advanced Birth Chart Features"}),a.jsx("p",{className:"text-gray-600",children:"Detailed astrological analysis with modern calculations"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:l.N8,title:"Precise Calculations",titleHi:"सटीक गणना",description:"Accurate planetary positions using Swiss Ephemeris"},{icon:l.UW,title:"House Analysis",titleHi:"भाव विश्लेषण",description:"Detailed analysis of all 12 houses and their significance"},{icon:l.ht,title:"Aspect Patterns",titleHi:"दृष्टि योग",description:"Planetary aspects and their influences on your life"},{icon:l.pO,title:"Dasha Periods",titleHi:"दशा काल",description:"Planetary periods and their timing effects"}].map((e,s)=>{let t=e.icon;return(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(t,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-lg font-semibold mb-3",children:e.title}),a.jsx("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title)})})]})}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsx("div",{className:"spiritual-container max-w-2xl mx-auto",children:a.jsx(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h2",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:"Generate Your Birth Chart"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),d(!0)},className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),a.jsx("input",{type:"text",name:"name",value:s.name,onChange:x,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{name:"gender",value:s.gender,onChange:x,className:"spiritual-input",required:!0,children:[a.jsx("option",{value:"",children:"Select Gender"}),a.jsx("option",{value:"male",children:"Male"}),a.jsx("option",{value:"female",children:"Female"}),a.jsx("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),a.jsx("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:x,className:"spiritual-input",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time of Birth *"}),a.jsx("input",{type:"time",name:"timeOfBirth",value:s.timeOfBirth,onChange:x,className:"spiritual-input",required:!0})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Place of Birth *"}),a.jsx("input",{type:"text",name:"placeOfBirth",value:s.placeOfBirth,onChange:x,className:"spiritual-input",placeholder:"City, State, Country",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Latitude (Optional)"}),a.jsx("input",{type:"text",name:"latitude",value:s.latitude,onChange:x,className:"spiritual-input",placeholder:"e.g., 25.3176"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Longitude (Optional)"}),a.jsx("input",{type:"text",name:"longitude",value:s.longitude,onChange:x,className:"spiritual-input",placeholder:"e.g., 82.9739"})]})]}),a.jsx("div",{className:"text-center",children:a.jsx("button",{type:"submit",className:"spiritual-button text-lg py-3 px-8 sacred-glow",children:"Generate Birth Chart"})})]})]})})})}),c&&a.jsx("section",{className:"py-16 bg-white",children:a.jsx("div",{className:"spiritual-container",children:(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,a.jsxs)("h2",{className:"text-3xl font-serif font-bold mb-8 text-center gradient-text",children:["Birth Chart - ",s.name]}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("div",{className:"bg-gray-100 rounded-lg p-1 flex space-x-1",children:[{id:"chart",label:"Birth Chart",labelHi:"जन्म कुंडली"},{id:"planets",label:"Planetary Positions",labelHi:"ग्रह स्थिति"},{id:"houses",label:"House Analysis",labelHi:"भाव विश्लेषण"},{id:"aspects",label:"Aspects",labelHi:"दृष्टि योग"}].map(e=>a.jsx("button",{onClick:()=>m(e.id),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${o===e.id?"bg-bhagwa-400 text-white":"text-gray-600 hover:text-gray-900"}`,children:e.label},e.id))})}),"chart"===o&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4",children:"Birth Chart (Rashi Chart)"}),a.jsx("div",{className:"bg-gray-100 h-96 rounded-lg flex items-center justify-center relative",children:a.jsx("div",{className:"grid grid-cols-4 grid-rows-4 w-80 h-80 border-2 border-bhagwa-400",children:Array.from({length:16},(e,s)=>{let t=s<4?s+1:s<8?12-(s-4):s<12?9-(s-8):4+(s-12);return(0,a.jsxs)("div",{className:"border border-gray-300 flex items-center justify-center text-xs font-medium relative",children:[a.jsx("span",{className:"absolute top-1 left-1 text-bhagwa-600",children:t}),1===t&&a.jsx("span",{className:"text-red-600",children:"Ma"}),5===t&&a.jsx("span",{className:"text-orange-600",children:"Su"}),8===t&&a.jsx("span",{className:"text-blue-600",children:"Mo"}),6===t&&a.jsx("span",{className:"text-green-600",children:"Me"}),9===t&&a.jsx("span",{className:"text-purple-600",children:"Ju"}),4===t&&a.jsx("span",{className:"text-pink-600",children:"Ve"}),10===t&&a.jsx("span",{className:"text-gray-600",children:"Sa"})]},s)})})}),a.jsx("div",{className:"mt-4 text-sm text-gray-600",children:(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Legend:"})," Su-Sun, Mo-Moon, Ma-Mars, Me-Mercury, Ju-Jupiter, Ve-Venus, Sa-Saturn"]})})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4",children:"Chart Information"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Name:"}),a.jsx("span",{children:s.name})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Date of Birth:"}),a.jsx("span",{children:s.dateOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Time of Birth:"}),a.jsx("span",{children:s.timeOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Place of Birth:"}),a.jsx("span",{children:s.placeOfBirth})]}),a.jsx("hr",{className:"my-4"}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Ascendant (Lagna):"}),a.jsx("span",{children:"Gemini ♊ 12\xb034'"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Sun Sign:"}),a.jsx("span",{children:"Leo ♌"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Moon Sign:"}),a.jsx("span",{children:"Scorpio ♏"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Nakshatra:"}),a.jsx("span",{children:"Anuradha"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Tithi:"}),a.jsx("span",{children:"Shukla Paksha Saptami"})]})]})]})]}),"planets"===o&&(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-6",children:"Planetary Positions"}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200",children:[a.jsx("th",{className:"text-left py-3 px-4 font-semibold",children:"Planet"}),a.jsx("th",{className:"text-left py-3 px-4 font-semibold",children:"Sign"}),a.jsx("th",{className:"text-left py-3 px-4 font-semibold",children:"Degree"}),a.jsx("th",{className:"text-left py-3 px-4 font-semibold",children:"House"}),a.jsx("th",{className:"text-left py-3 px-4 font-semibold",children:"Status"})]})}),a.jsx("tbody",{children:[{planet:"Sun",planetHi:"सूर्य",sign:"Leo",signHi:"सिंह",degree:"15\xb023'",house:5,retrograde:!1},{planet:"Moon",planetHi:"चंद्र",sign:"Scorpio",signHi:"वृश्चिक",degree:"8\xb045'",house:8,retrograde:!1},{planet:"Mars",planetHi:"मंगल",sign:"Aries",signHi:"मेष",degree:"22\xb012'",house:1,retrograde:!1},{planet:"Mercury",planetHi:"बुध",sign:"Virgo",signHi:"कन्या",degree:"3\xb056'",house:6,retrograde:!0},{planet:"Jupiter",planetHi:"बृहस्पति",sign:"Sagittarius",signHi:"धनु",degree:"18\xb034'",house:9,retrograde:!1},{planet:"Venus",planetHi:"शुक्र",sign:"Cancer",signHi:"कर्क",degree:"12\xb028'",house:4,retrograde:!1},{planet:"Saturn",planetHi:"शनि",sign:"Capricorn",signHi:"मकर",degree:"25\xb017'",house:10,retrograde:!1},{planet:"Rahu",planetHi:"राहु",sign:"Gemini",signHi:"मिथुन",degree:"14\xb033'",house:3,retrograde:!0},{planet:"Ketu",planetHi:"केतु",sign:"Sagittarius",signHi:"धनु",degree:"14\xb033'",house:9,retrograde:!0}].map((e,s)=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[a.jsx("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.planet}),a.jsx("div",{className:"text-xs text-gray-500",children:e.planetHi})]})}),a.jsx("td",{className:"py-3 px-4",children:(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:e.sign}),a.jsx("div",{className:"text-xs text-gray-500",children:e.signHi})]})}),a.jsx("td",{className:"py-3 px-4 font-mono",children:e.degree}),a.jsx("td",{className:"py-3 px-4",children:e.house}),a.jsx("td",{className:"py-3 px-4",children:e.retrograde?a.jsx("span",{className:"text-red-600 text-xs",children:"Retrograde"}):a.jsx("span",{className:"text-green-600 text-xs",children:"Direct"})})]},s))})]})})]}),"houses"===o&&a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{number:1,name:"Ascendant",nameHi:"लग्न",significance:"Self, personality, physical appearance"},{number:2,name:"Wealth",nameHi:"धन",significance:"Money, family, speech"},{number:3,name:"Courage",nameHi:"साहस",significance:"Siblings, courage, communication"},{number:4,name:"Home",nameHi:"गृह",significance:"Mother, home, property"},{number:5,name:"Children",nameHi:"संतान",significance:"Children, creativity, education"},{number:6,name:"Enemies",nameHi:"शत्रु",significance:"Health, enemies, service"},{number:7,name:"Partnership",nameHi:"साझेदारी",significance:"Marriage, partnerships"},{number:8,name:"Longevity",nameHi:"आयु",significance:"Longevity, transformation"},{number:9,name:"Fortune",nameHi:"भाग्य",significance:"Father, luck, spirituality"},{number:10,name:"Career",nameHi:"कर्म",significance:"Career, reputation, status"},{number:11,name:"Gains",nameHi:"लाभ",significance:"Gains, friends, aspirations"},{number:12,name:"Loss",nameHi:"व्यय",significance:"Expenses, spirituality, foreign lands"}].map(e=>(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[a.jsx("div",{className:"w-8 h-8 bg-bhagwa-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3",children:e.number}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold",children:e.name}),a.jsx("p",{className:"text-xs text-gray-500",children:e.nameHi})]})]}),a.jsx("p",{className:"text-sm text-gray-600",children:e.significance}),(0,a.jsxs)("div",{className:"mt-3 text-xs",children:[a.jsx("span",{className:"font-medium",children:"Planets: "}),1===e.number&&a.jsx("span",{className:"text-red-600",children:"Mars"}),5===e.number&&a.jsx("span",{className:"text-orange-600",children:"Sun"}),8===e.number&&a.jsx("span",{className:"text-blue-600",children:"Moon"}),6===e.number&&a.jsx("span",{className:"text-green-600",children:"Mercury (R)"}),9===e.number&&a.jsx("span",{className:"text-purple-600",children:"Jupiter, Ketu"}),4===e.number&&a.jsx("span",{className:"text-pink-600",children:"Venus"}),10===e.number&&a.jsx("span",{className:"text-gray-600",children:"Saturn"}),3===e.number&&a.jsx("span",{className:"text-indigo-600",children:"Rahu"}),![1,3,4,5,6,8,9,10].includes(e.number)&&a.jsx("span",{className:"text-gray-400",children:"Empty"})]})]},e.number))}),"aspects"===o&&(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-6",children:"Planetary Aspects"}),a.jsx("div",{className:"space-y-4",children:[{from:"Sun",to:"Mars",type:"Trine",strength:"Strong",effect:"Positive"},{from:"Moon",to:"Venus",type:"Sextile",strength:"Moderate",effect:"Positive"},{from:"Mercury",to:"Jupiter",type:"Opposition",strength:"Weak",effect:"Challenging"},{from:"Saturn",to:"Mars",type:"Square",strength:"Strong",effect:"Challenging"}].map((e,s)=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"font-semibold",children:e.from}),a.jsx("span",{className:"text-gray-500",children:"→"}),a.jsx("span",{className:"font-semibold",children:e.to})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${"Positive"===e.effect?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.effect}),a.jsx("span",{className:"text-sm text-gray-600",children:e.type})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[a.jsx("span",{className:"font-medium",children:"Strength: "}),e.strength]})]},s))})]}),(0,a.jsxs)("div",{className:"text-center mt-8",children:[a.jsx("button",{className:"spiritual-button text-lg py-3 px-8 mr-4",children:"Download Detailed Report"}),a.jsx("button",{className:"spiritual-button-secondary text-lg py-3 px-8",children:"Book Consultation"})]})]})})})]})}},7359:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>r});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/birth-chart/page.tsx`),{__esModule:i,$$typeof:n}=a,r=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(375));module.exports=a})();