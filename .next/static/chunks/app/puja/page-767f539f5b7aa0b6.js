(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[672],{2774:function(e,s,a){Promise.resolve().then(a.bind(a,6420))},6420:function(e,s,a){"use strict";a.r(s);var i=a(3827),t=a(4090),n=a(8371),l=a(3665);s.default=()=>{var e,s,a,r,c,d,m,o,x;let[h,u]=(0,t.useState)(null),[p,j]=(0,t.useState)("details"),[g,f]=(0,t.useState)({name:"",email:"",phone:"",date:"",time:"",address:"",specialRequests:"",paymentMethod:"card"}),[b,N]=(0,t.useState)(!1),y=[{id:"ganesh",name:"<PERSON><PERSON><PERSON>",nameHi:"गणेश पूजा",description:"Remove obstacles and bring prosperity to your life",descriptionHi:"बाधाओं को दूर करें और अपने जीवन में समृद्धि लाएं",duration:"2-3 hours",price:2500,benefits:["Obstacle removal","Success in new ventures","Wisdom and prosperity"],image:"/images/ganesh-puja.jpg"},{id:"lakshmi",name:"Lakshmi Puja",nameHi:"लक्ष्मी पूजा",description:"Attract wealth, abundance and financial prosperity",descriptionHi:"धन, समृद्धि और वित्तीय खुशहाली को आकर्षित करें",duration:"3-4 hours",price:3500,benefits:["Wealth attraction","Financial stability","Business growth"],image:"/images/lakshmi-puja.jpg"},{id:"shiva",name:"Shiva Puja",nameHi:"शिव पूजा",description:"Seek blessings for spiritual growth and inner peace",descriptionHi:"आध्यात्मिक विकास और आंतरिक शांति के लिए आशीर्वाद मांगें",duration:"4-5 hours",price:4e3,benefits:["Spiritual awakening","Inner peace","Negative energy removal"],image:"/images/shiva-puja.jpg"},{id:"durga",name:"Durga Puja",nameHi:"दुर्गा पूजा",description:"Protection from negative forces and empowerment",descriptionHi:"नकारात्मक शक्तियों से सुरक्षा और सशक्तिकरण",duration:"5-6 hours",price:5e3,benefits:["Protection from evil","Strength and courage","Victory over enemies"],image:"/images/durga-puja.jpg"},{id:"saraswati",name:"Saraswati Puja",nameHi:"सरस्वती पूजा",description:"Enhance knowledge, wisdom and creative abilities",descriptionHi:"ज्ञान, बुद्धि और रचनात्मक क्षमताओं को बढ़ाएं",duration:"2-3 hours",price:2e3,benefits:["Enhanced learning","Creative inspiration","Academic success"],image:"/images/saraswati-puja.jpg"},{id:"hanuman",name:"Hanuman Puja",nameHi:"हनुमान पूजा",description:"Gain strength, courage and protection from difficulties",descriptionHi:"शक्ति, साहस और कठिनाइयों से सुरक्षा प्राप्त करें",duration:"3-4 hours",price:3e3,benefits:["Physical strength","Mental courage","Protection from harm"],image:"/images/hanuman-puja.jpg"}],v=e=>{let{name:s,value:a}=e.target;f(e=>({...e,[s]:a}))},w=async e=>{e.preventDefault(),"details"===p?j("payment"):"payment"===p&&(N(!0),await new Promise(e=>setTimeout(e,3e3)),N(!1),j("confirmation"))};return(0,i.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,i.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,i.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,i.jsx)("div",{className:"absolute top-10 left-10",children:(0,i.jsx)(l.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),(0,i.jsx)("div",{className:"absolute bottom-10 right-10",children:(0,i.jsx)(l.pO,{size:120,className:"text-lotus-pink-400"})})]}),(0,i.jsx)("div",{className:"spiritual-container text-center relative z-10",children:(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Sacred Puja Services"}),(0,i.jsx)("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Book authentic Hindu rituals and ceremonies performed by experienced priests for divine blessings"})]})})]}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsxs)("div",{className:"spiritual-container",children:[(0,i.jsxs)("div",{className:"text-center mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Choose Your Sacred Ritual"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Select from our collection of traditional pujas and ceremonies"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:y.map((e,s)=>(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{y:-8,scale:1.02},transition:{duration:.5,delay:.1*s,hover:{duration:.3}},viewport:{once:!0},className:"spiritual-card-enhanced cursor-pointer transition-all duration-300 ".concat(h===e.id?"ring-2 ring-bhagwa-500":""),onClick:()=>u(e.id),children:[(0,i.jsxs)("div",{className:"text-center mb-4",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,i.jsx)(l.X5,{className:"w-10 h-10 text-white"})}),(0,i.jsx)("h3",{className:"text-xl font-serif font-bold mb-2",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:e.nameHi}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:e.description})]}),(0,i.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"font-medium",children:"Duration:"}),(0,i.jsx)("span",{children:e.duration})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"font-medium",children:"Price:"}),(0,i.jsxs)("span",{className:"text-bhagwa-600 font-bold",children:["₹",e.price]})]})]}),(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-sm mb-2",children:"Benefits:"}),(0,i.jsx)("ul",{className:"space-y-1",children:e.benefits.map((e,s)=>(0,i.jsxs)("li",{className:"flex items-center text-xs text-gray-600",children:[(0,i.jsx)(l.UI,{className:"w-3 h-3 text-bhagwa-400 mr-2"}),e]},s))})]}),(0,i.jsx)("button",{className:"w-full spiritual-button text-sm py-2",children:"Book This Puja"})]},e.id))})]})}),h&&(0,i.jsx)("section",{className:"py-16 bg-gray-50",children:(0,i.jsx)("div",{className:"spiritual-container max-w-2xl mx-auto",children:(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"spiritual-card-enhanced",children:[(0,i.jsx)("div",{className:"flex items-center justify-center mb-8",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center ".concat("details"===p?"text-bhagwa-600":"payment"===p||"confirmation"===p?"text-green-600":"text-gray-400"),children:[(0,i.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ".concat("details"===p?"bg-bhagwa-600 text-white":"payment"===p||"confirmation"===p?"bg-green-600 text-white":"bg-gray-200"),children:"1"}),(0,i.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Details"})]}),(0,i.jsx)("div",{className:"w-8 h-0.5 bg-gray-300"}),(0,i.jsxs)("div",{className:"flex items-center ".concat("payment"===p?"text-bhagwa-600":"confirmation"===p?"text-green-600":"text-gray-400"),children:[(0,i.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ".concat("payment"===p?"bg-bhagwa-600 text-white":"confirmation"===p?"bg-green-600 text-white":"bg-gray-200"),children:"2"}),(0,i.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Payment"})]}),(0,i.jsx)("div",{className:"w-8 h-0.5 bg-gray-300"}),(0,i.jsxs)("div",{className:"flex items-center ".concat("confirmation"===p?"text-green-600":"text-gray-400"),children:[(0,i.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ".concat("confirmation"===p?"bg-green-600 text-white":"bg-gray-200"),children:"3"}),(0,i.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Confirmation"})]})]})}),(0,i.jsxs)("h3",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:["details"===p&&"Book Your ".concat(null===(e=y.find(e=>e.id===h))||void 0===e?void 0:e.name),"payment"===p&&"Payment Details","confirmation"===p&&"Booking Confirmed!"]}),"confirmation"===p?(0,i.jsxs)("div",{className:"text-center space-y-6",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-10 h-10 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-xl font-bold text-green-600 mb-2",children:"Booking Successful!"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Your puja has been booked successfully. You will receive a confirmation email shortly."}),(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg text-left max-w-md mx-auto",children:[(0,i.jsx)("h5",{className:"font-semibold mb-2",children:"Booking Details:"}),(0,i.jsxs)("div",{className:"text-sm space-y-1",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Booking ID:"}),(0,i.jsxs)("span",{className:"font-mono",children:["PJ",Date.now().toString().slice(-6)]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Puja:"}),(0,i.jsx)("span",{children:null===(s=y.find(e=>e.id===h))||void 0===s?void 0:s.name})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Date:"}),(0,i.jsx)("span",{children:g.date})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Time:"}),(0,i.jsx)("span",{children:g.time})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,i.jsx)("span",{children:"Amount Paid:"}),(0,i.jsxs)("span",{children:["₹",null===(a=y.find(e=>e.id===h))||void 0===a?void 0:a.price]})]})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,i.jsx)("button",{onClick:()=>{u(null),j("details"),f({name:"",email:"",phone:"",date:"",time:"",address:"",specialRequests:"",paymentMethod:"card"})},className:"spiritual-button-secondary py-2 px-6",children:"Book Another Puja"}),(0,i.jsx)("button",{className:"spiritual-button py-2 px-6",children:"Download Receipt"})]})]}):(0,i.jsxs)("form",{onSubmit:w,className:"space-y-6",children:["details"===p&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,i.jsx)("input",{type:"text",name:"name",value:g.name,onChange:v,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,i.jsx)("input",{type:"tel",name:"phone",value:g.phone,onChange:v,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX",required:!0})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,i.jsx)("input",{type:"email",name:"email",value:g.email,onChange:v,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Date *"}),(0,i.jsx)("input",{type:"date",name:"date",value:g.date,onChange:v,className:"spiritual-input",min:new Date().toISOString().split("T")[0],required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Preferred Time"}),(0,i.jsxs)("select",{name:"time",value:g.time,onChange:v,className:"spiritual-input",children:[(0,i.jsx)("option",{value:"",children:"Select time"}),(0,i.jsx)("option",{value:"morning",children:"Morning (6:00 AM - 12:00 PM)"}),(0,i.jsx)("option",{value:"afternoon",children:"Afternoon (12:00 PM - 6:00 PM)"}),(0,i.jsx)("option",{value:"evening",children:"Evening (6:00 PM - 10:00 PM)"})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Address for Puja *"}),(0,i.jsx)("textarea",{rows:3,name:"address",value:g.address,onChange:v,className:"spiritual-input",placeholder:"Enter complete address where puja will be performed",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Special Requirements"}),(0,i.jsx)("textarea",{rows:3,name:"specialRequests",value:g.specialRequests,onChange:v,className:"spiritual-input",placeholder:"Any specific requirements or requests for the puja..."})]})]}),"payment"===p&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg mb-6",children:[(0,i.jsx)("h4",{className:"font-semibold mb-3",children:"Booking Summary"}),(0,i.jsxs)("div",{className:"text-sm space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Name:"}),(0,i.jsx)("span",{children:g.name})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Puja:"}),(0,i.jsx)("span",{children:null===(r=y.find(e=>e.id===h))||void 0===r?void 0:r.name})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Date:"}),(0,i.jsx)("span",{children:g.date})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Time:"}),(0,i.jsx)("span",{children:g.time})]}),(0,i.jsx)("hr",{className:"my-2"}),(0,i.jsxs)("div",{className:"flex justify-between font-bold text-lg",children:[(0,i.jsx)("span",{children:"Total Amount:"}),(0,i.jsxs)("span",{className:"text-bhagwa-600",children:["₹",null===(c=y.find(e=>e.id===h))||void 0===c?void 0:c.price]})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Payment Method *"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"radio",id:"card",name:"paymentMethod",value:"card",checked:"card"===g.paymentMethod,onChange:v,className:"mr-3"}),(0,i.jsxs)("label",{htmlFor:"card",className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCB3"}),"Credit/Debit Card"]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"radio",id:"upi",name:"paymentMethod",value:"upi",checked:"upi"===g.paymentMethod,onChange:v,className:"mr-3"}),(0,i.jsxs)("label",{htmlFor:"upi",className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-2xl mr-2",children:"\uD83D\uDCF1"}),"UPI Payment"]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"radio",id:"netbanking",name:"paymentMethod",value:"netbanking",checked:"netbanking"===g.paymentMethod,onChange:v,className:"mr-3"}),(0,i.jsxs)("label",{htmlFor:"netbanking",className:"flex items-center",children:[(0,i.jsx)("span",{className:"text-2xl mr-2",children:"\uD83C\uDFE6"}),"Net Banking"]})]})]})]}),"card"===g.paymentMethod&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Number *"}),(0,i.jsx)("input",{type:"text",className:"spiritual-input",placeholder:"1234 5678 9012 3456",maxLength:19,required:!0})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiry Date *"}),(0,i.jsx)("input",{type:"text",className:"spiritual-input",placeholder:"MM/YY",maxLength:5,required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVV *"}),(0,i.jsx)("input",{type:"text",className:"spiritual-input",placeholder:"123",maxLength:3,required:!0})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cardholder Name *"}),(0,i.jsx)("input",{type:"text",className:"spiritual-input",placeholder:"Name as on card",required:!0})]})]}),"upi"===g.paymentMethod&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"UPI ID *"}),(0,i.jsx)("input",{type:"text",className:"spiritual-input",placeholder:"yourname@upi",required:!0})]})]}),"details"===p&&(0,i.jsxs)("div",{className:"bg-bhagwa-50 p-4 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold mb-2",children:"Puja Details:"}),(0,i.jsxs)("div",{className:"text-sm space-y-1",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Puja:"}),(0,i.jsx)("span",{children:null===(d=y.find(e=>e.id===h))||void 0===d?void 0:d.name})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Duration:"}),(0,i.jsx)("span",{children:null===(m=y.find(e=>e.id===h))||void 0===m?void 0:m.duration})]}),(0,i.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,i.jsx)("span",{children:"Total Amount:"}),(0,i.jsxs)("span",{children:["₹",null===(o=y.find(e=>e.id===h))||void 0===o?void 0:o.price]})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:["payment"===p&&(0,i.jsx)("button",{type:"button",onClick:()=>j("details"),className:"spiritual-button-secondary py-3 px-6",children:"Back to Details"}),(0,i.jsx)("button",{type:"submit",disabled:b,className:"spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed ".concat("payment"===p?"ml-auto":"mx-auto"),children:b?(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Processing Payment..."]}):"details"===p?"Proceed to Payment":"Pay ₹".concat(null===(x=y.find(e=>e.id===h))||void 0===x?void 0:x.price)})]})]})]})})}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsx)("div",{className:"spiritual-container",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-serif font-bold mb-8 gradient-text",children:"Why Choose Our Puja Services?"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,i.jsx)(l.ht,{className:"w-8 h-8 text-white"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Authentic Rituals"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:"Traditional ceremonies performed according to ancient Vedic scriptures"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,i.jsx)(l.X5,{className:"w-8 h-8 text-white"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Experienced Priests"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:"Certified and experienced priests with deep knowledge of rituals"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,i.jsx)(l.pO,{className:"w-8 h-8 text-white"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Sacred Materials"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:"All puja materials and offerings included in the service"})]})]})]})})})]})}}},function(e){e.O(0,[371,665,971,69,744],function(){return e(e.s=2774)}),_N_E=e.O()}]);