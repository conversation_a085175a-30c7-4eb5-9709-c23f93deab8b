(()=>{var e={};e.id=676,e.ids=[676],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8452:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=s(482),i=s(9108),r=s(2563),n=s.n(r),l=s(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["horoscope",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6677)),"/Users/<USER>/Downloads/astrology/src/app/horoscope/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/horoscope/page.tsx"],m="/horoscope/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/horoscope/page",pathname:"/horoscope",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4249:(e,t,s)=>{Promise.resolve().then(s.bind(s,119))},119:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(5344),i=s(3729),r=s(7292),n=s(9758),l=s(1902);let o=()=>{let{t:e}=(0,n.$G)(["common","home"]),[t,s]=(0,i.useState)("aries"),[o,c]=(0,i.useState)(new Date().toISOString().split("T")[0]),[d,m]=(0,i.useState)("daily"),x=[{id:"aries",name:"Aries",nameHi:"मेष",symbol:"♈",element:"Fire",elementHi:"अग्नि",dates:"Mar 21 - Apr 19",datesHi:"21 मार्च - 19 अप्रैल",ruling:"Mars",rulingHi:"मंगल",color:"text-red-500"},{id:"taurus",name:"Taurus",nameHi:"वृषभ",symbol:"♉",element:"Earth",elementHi:"पृथ्वी",dates:"Apr 20 - May 20",datesHi:"20 अप्रैल - 20 मई",ruling:"Venus",rulingHi:"शुक्र",color:"text-green-500"},{id:"gemini",name:"Gemini",nameHi:"मिथुन",symbol:"♊",element:"Air",elementHi:"वायु",dates:"May 21 - Jun 20",datesHi:"21 मई - 20 जून",ruling:"Mercury",rulingHi:"बुध",color:"text-yellow-500"},{id:"cancer",name:"Cancer",nameHi:"कर्क",symbol:"♋",element:"Water",elementHi:"जल",dates:"Jun 21 - Jul 22",datesHi:"21 जून - 22 जुलाई",ruling:"Moon",rulingHi:"चंद्र",color:"text-blue-500"},{id:"leo",name:"Leo",nameHi:"सिंह",symbol:"♌",element:"Fire",elementHi:"अग्नि",dates:"Jul 23 - Aug 22",datesHi:"23 जुलाई - 22 अगस्त",ruling:"Sun",rulingHi:"सूर्य",color:"text-orange-500"},{id:"virgo",name:"Virgo",nameHi:"कन्या",symbol:"♍",element:"Earth",elementHi:"पृथ्वी",dates:"Aug 23 - Sep 22",datesHi:"23 अगस्त - 22 सितंबर",ruling:"Mercury",rulingHi:"बुध",color:"text-green-600"},{id:"libra",name:"Libra",nameHi:"तुला",symbol:"♎",element:"Air",elementHi:"वायु",dates:"Sep 23 - Oct 22",datesHi:"23 सितंबर - 22 अक्टूबर",ruling:"Venus",rulingHi:"शुक्र",color:"text-pink-500"},{id:"scorpio",name:"Scorpio",nameHi:"वृश्चिक",symbol:"♏",element:"Water",elementHi:"जल",dates:"Oct 23 - Nov 21",datesHi:"23 अक्टूबर - 21 नवंबर",ruling:"Mars",rulingHi:"मंगल",color:"text-red-600"},{id:"sagittarius",name:"Sagittarius",nameHi:"धनु",symbol:"♐",element:"Fire",elementHi:"अग्नि",dates:"Nov 22 - Dec 21",datesHi:"22 नवंबर - 21 दिसंबर",ruling:"Jupiter",rulingHi:"बृहस्पति",color:"text-purple-500"},{id:"capricorn",name:"Capricorn",nameHi:"मकर",symbol:"♑",element:"Earth",elementHi:"पृथ्वी",dates:"Dec 22 - Jan 19",datesHi:"22 दिसंबर - 19 जनवरी",ruling:"Saturn",rulingHi:"शनि",color:"text-gray-600"},{id:"aquarius",name:"Aquarius",nameHi:"कुंभ",symbol:"♒",element:"Air",elementHi:"वायु",dates:"Jan 20 - Feb 18",datesHi:"20 जनवरी - 18 फरवरी",ruling:"Saturn",rulingHi:"शनि",color:"text-cyan-500"},{id:"pisces",name:"Pisces",nameHi:"मीन",symbol:"♓",element:"Water",elementHi:"जल",dates:"Feb 19 - Mar 20",datesHi:"19 फरवरी - 20 मार्च",ruling:"Jupiter",rulingHi:"बृहस्पति",color:"text-blue-600"}],h={aries:{daily:{today:"Today brings new opportunities for growth and expansion. Your natural leadership qualities will be highlighted, making it an excellent day for taking initiative in both personal and professional matters.",love:"Romance is in the air! Single Aries may encounter someone special, while those in relationships will experience deeper connection with their partner.",career:"Your ambitious nature will pay off today. A project you've been working on may receive recognition from superiors.",health:"High energy levels today, but remember to balance activity with rest. Stay hydrated and maintain a healthy diet.",lucky:{number:7,color:"Red",time:"2:00 PM - 4:00 PM"}},weekly:{today:"This week focuses on personal transformation and new beginnings. Mars energizes your sector of self-improvement.",love:"Mid-week brings romantic opportunities. Weekend is perfect for deepening existing relationships.",career:"Professional growth accelerates. New projects or responsibilities may come your way by Thursday.",health:"Maintain consistent exercise routine. Pay attention to stress levels during busy periods.",lucky:{number:3,color:"Orange",time:"Morning hours"}},monthly:{today:"March emphasizes career advancement and personal goals. Your ruling planet Mars supports ambitious endeavors.",love:"Relationship dynamics shift positively. Single Aries attract meaningful connections around mid-month.",career:"Major career opportunities emerge. Leadership roles or promotions are highly favored.",health:"Focus on building long-term healthy habits. Energy levels remain consistently high.",lucky:{number:9,color:"Crimson",time:"Afternoon"}}},taurus:{daily:{today:"Stability and comfort are your themes today. Focus on practical matters and enjoy simple pleasures.",love:"Steady progress in relationships. Show appreciation for your partner's loyalty and support.",career:"Methodical approach to work pays off. Avoid rushing important decisions.",health:"Good day for relaxation and self-care. Consider a massage or peaceful walk in nature.",lucky:{number:6,color:"Green",time:"Evening"}},weekly:{today:"Venus brings harmony to your week. Focus on beauty, comfort, and financial stability.",love:"Romantic gestures are well-received. Plan something special for the weekend.",career:"Steady progress in professional matters. Avoid major changes this week.",health:"Maintain regular eating habits. Your body craves consistency and nourishment.",lucky:{number:2,color:"Pink",time:"Late afternoon"}},monthly:{today:"Financial matters take center stage this month. Venus supports material growth and stability.",love:"Relationships deepen through shared values and goals. Commitment themes are strong.",career:"Slow but steady career progress. Focus on building solid foundations.",health:"Establish sustainable wellness routines. Your body responds well to gradual changes.",lucky:{number:8,color:"Earth tones",time:"Morning"}}}},u=x.find(e=>e.id===t),p=h[t]?.[d]||h.aries.daily,g=e=>{let t=new Date(o);"prev"===e?t.setDate(t.getDate()-1):t.setDate(t.getDate()+1),c(t.toISOString().split("T")[0])};return(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[a.jsx("div",{className:"absolute top-10 left-10",children:a.jsx(l.ht,{size:100,className:"text-saffron-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-10 right-10",children:a.jsx(l.pO,{size:120,className:"text-lotus-pink-400"})})]}),a.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[a.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Daily Horoscope"}),a.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Discover what the stars have in store for you today with personalized Vedic astrology insights"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-4 mb-6",children:[a.jsx("button",{onClick:()=>g("prev"),className:"p-2 rounded-full bg-bhagwa-100 text-bhagwa-600 hover:bg-bhagwa-200 transition-colors",children:"←"}),a.jsx("div",{className:"text-lg text-bhagwa-600 font-medium min-w-[200px] text-center",children:new Date(o).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),a.jsx("button",{onClick:()=>g("next"),className:"p-2 rounded-full bg-bhagwa-100 text-bhagwa-600 hover:bg-bhagwa-200 transition-colors",children:"→"})]}),a.jsx("div",{className:"flex justify-center mb-6",children:a.jsx("div",{className:"bg-gray-100 rounded-lg p-1 flex space-x-1",children:[{id:"daily",label:"Daily",labelHi:"दैनिक"},{id:"weekly",label:"Weekly",labelHi:"साप्ताहिक"},{id:"monthly",label:"Monthly",labelHi:"मासिक"}].map(e=>a.jsx("button",{onClick:()=>m(e.id),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${d===e.id?"bg-bhagwa-400 text-white":"text-gray-600 hover:text-gray-900"}`,children:e.label},e.id))})})]})})]}),a.jsx("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"spiritual-container",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Select Your Zodiac Sign"}),a.jsx("p",{className:"text-gray-600",children:"Choose your sun sign to get personalized predictions"})]}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-12",children:x.map((e,i)=>a.jsx(r.E.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},whileHover:{scale:1.05},transition:{duration:.3,delay:.05*i},viewport:{once:!0},className:`p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${t===e.id?"border-bhagwa-500 bg-bhagwa-50 shadow-lg":"border-gray-200 hover:border-bhagwa-300 hover:shadow-md"}`,onClick:()=>s(e.id),children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:`text-3xl mb-2 ${e.color}`,children:e.symbol}),a.jsx("h3",{className:"font-semibold text-sm",children:e.name}),a.jsx("p",{className:"text-xs text-gray-500",children:e.nameHi}),a.jsx("p",{className:"text-xs text-gray-400 mt-1",children:e.dates})]})},e.id))})]})}),u&&a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsx("div",{className:"spiritual-container",children:(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("div",{className:`text-6xl mb-4 ${u.color}`,children:u.symbol}),(0,a.jsxs)("h2",{className:"text-3xl font-serif font-bold mb-2 gradient-text",children:[u.name," (",u.nameHi,")"]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-8 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Element:"})," ",u.element," (",u.elementHi,")"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Ruling Planet:"})," ",u.ruling," (",u.rulingHi,")"]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"font-medium",children:"Dates:"})," ",u.dates]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx(l.UI,{className:"w-6 h-6 text-saffron-500 mr-2"}),a.jsx("h3",{className:"text-xl font-serif font-bold",children:"Today's Prediction"})]}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:p.today})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-2xl mr-2",children:"\uD83D\uDC95"}),a.jsx("h3",{className:"text-xl font-serif font-bold",children:"Love & Relationships"})]}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:p.love})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-2xl mr-2",children:"\uD83D\uDCBC"}),a.jsx("h3",{className:"text-xl font-serif font-bold",children:"Career & Finance"})]}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:p.career})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-2xl mr-2",children:"\uD83C\uDF3F"}),a.jsx("h3",{className:"text-xl font-serif font-bold",children:"Health & Wellness"})]}),a.jsx("p",{className:"text-gray-700 leading-relaxed",children:p.health})]})]}),(0,a.jsxs)("div",{className:"mt-8 spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4 text-center gradient-text",children:"Today's Lucky Elements"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-2xl mb-2",children:"\uD83D\uDD22"}),a.jsx("h4",{className:"font-semibold",children:"Lucky Number"}),a.jsx("p",{className:"text-bhagwa-600 font-bold text-xl",children:p.lucky.number})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-2xl mb-2",children:"\uD83C\uDFA8"}),a.jsx("h4",{className:"font-semibold",children:"Lucky Color"}),a.jsx("p",{className:"text-bhagwa-600 font-bold text-xl",children:p.lucky.color})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-2xl mb-2",children:"⏰"}),a.jsx("h4",{className:"font-semibold",children:"Lucky Time"}),a.jsx("p",{className:"text-bhagwa-600 font-bold text-xl",children:p.lucky.time})]})]})]})]},t)})}),a.jsx("section",{className:"py-16 bg-gradient-to-br from-gray-900 via-gray-800 to-saffron-900 text-white",children:a.jsx("div",{className:"spiritual-container text-center",children:(0,a.jsxs)(r.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[a.jsx("h2",{className:"text-3xl font-serif font-bold mb-6 gradient-text",children:"Want More Detailed Insights?"}),a.jsx("p",{className:"text-xl text-gray-300 mb-8 max-w-2xl mx-auto",children:"Get personalized consultations and detailed birth chart analysis from our expert astrologers"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[a.jsx("button",{className:"spiritual-button text-lg py-3 px-8 sacred-glow",children:"Book Personal Reading"}),a.jsx("button",{className:"spiritual-button-secondary text-lg py-3 px-8 border-white text-white hover:bg-white hover:text-gray-900",children:"Generate Kundli"})]})]})})})]})}},6677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>r,__esModule:()=>i,default:()=>n});let a=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/horoscope/page.tsx`),{__esModule:i,$$typeof:r}=a,n=a.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[651,713],()=>s(8452));module.exports=a})();