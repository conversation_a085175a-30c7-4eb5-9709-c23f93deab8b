(()=>{var e={};e.id=924,e.ids=[924],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1512:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(482),i=t(9108),n=t(2563),l=t.n(n),r=t(8300),c={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);t.d(s,c);let d=["",{children:["kundli",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9407)),"/Users/<USER>/Downloads/astrology/src/app/kundli/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],o=["/Users/<USER>/Downloads/astrology/src/app/kundli/page.tsx"],x="/kundli/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/kundli/page",pathname:"/kundli",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7890:(e,s,t)=>{Promise.resolve().then(t.bind(t,8235))},8235:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(5344),i=t(3729),n=t(7292),l=t(9758),r=t(1902);let c=()=>{let{t:e}=(0,l.$G)(["common","home"]),[s,t]=(0,i.useState)({name:"",gender:"",dateOfBirth:"",timeOfBirth:"",placeOfBirth:"",latitude:"",longitude:""}),[c,d]=(0,i.useState)(!1),[o,x]=(0,i.useState)("rashi"),[m,h]=(0,i.useState)(!1),p=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))},u=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,2e3)),h(!1),d(!0)},g=[{icon:r.N8,title:"Birth Chart Analysis",titleHi:"जन्म कुंडली विश्लेषण",description:"Detailed analysis of planetary positions at the time of birth"},{icon:r.UW,title:"Dasha Predictions",titleHi:"दशा भविष्यवाणी",description:"Planetary periods and their effects on your life"},{icon:r.ht,title:"Remedial Measures",titleHi:"उपचारात्मक उपाय",description:"Personalized remedies to overcome challenges"},{icon:r.pO,title:"Life Predictions",titleHi:"जीवन भविष्यवाणी",description:"Insights into career, marriage, health, and prosperity"}];return(0,a.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,a.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[a.jsx("div",{className:"absolute top-10 left-10",children:a.jsx(r.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-10 right-10",children:a.jsx(r.pO,{size:120,className:"text-lotus-pink-400"})}),a.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:a.jsx(r.UW,{size:200,className:"text-divine-purple-300 mandala-rotate"})})]}),a.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[a.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Kundli Analysis"}),a.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Generate your personalized birth chart and discover the cosmic blueprint of your life"})]})})]}),a.jsx("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"spiritual-container",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"What You'll Discover"}),a.jsx("p",{className:"text-gray-600",children:"Comprehensive insights from your birth chart analysis"})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:g.map((e,s)=>{let t=e.icon;return(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(t,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-lg font-semibold mb-3",children:e.title}),a.jsx("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title)})})]})}),a.jsx("section",{className:"py-16 bg-gray-50",children:a.jsx("div",{className:"spiritual-container max-w-2xl mx-auto",children:a.jsx(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h2",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:"Generate Your Kundli"}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),a.jsx("input",{type:"text",name:"name",value:s.name,onChange:p,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,a.jsxs)("select",{name:"gender",value:s.gender,onChange:p,className:"spiritual-input",required:!0,children:[a.jsx("option",{value:"",children:"Select Gender"}),a.jsx("option",{value:"male",children:"Male"}),a.jsx("option",{value:"female",children:"Female"}),a.jsx("option",{value:"other",children:"Other"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),a.jsx("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:p,className:"spiritual-input",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time of Birth *"}),a.jsx("input",{type:"time",name:"timeOfBirth",value:s.timeOfBirth,onChange:p,className:"spiritual-input",required:!0})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Place of Birth *"}),a.jsx("input",{type:"text",name:"placeOfBirth",value:s.placeOfBirth,onChange:p,className:"spiritual-input",placeholder:"City, State, Country",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Latitude (Optional)"}),a.jsx("input",{type:"text",name:"latitude",value:s.latitude,onChange:p,className:"spiritual-input",placeholder:"e.g., 25.3176"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Longitude (Optional)"}),a.jsx("input",{type:"text",name:"longitude",value:s.longitude,onChange:p,className:"spiritual-input",placeholder:"e.g., 82.9739"})]})]}),a.jsx("div",{className:"text-center",children:a.jsx("button",{type:"submit",disabled:m,className:"spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Generating Kundli..."]}):"Generate Kundli"})})]})]})})})}),c&&a.jsx("section",{className:"py-16 bg-white",children:a.jsx("div",{className:"spiritual-container",children:(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,a.jsxs)("h2",{className:"text-3xl font-serif font-bold mb-8 text-center gradient-text",children:["Your Kundli - ",s.name]}),a.jsx("div",{className:"flex justify-center mb-8",children:a.jsx("div",{className:"bg-gray-100 rounded-lg p-1 flex space-x-1",children:[{id:"rashi",label:"Rashi Chart",labelHi:"राशि चार्ट"},{id:"navamsa",label:"Navamsa Chart",labelHi:"नवांश चार्ट"},{id:"dashamsa",label:"Dashamsa Chart",labelHi:"दशांश चार्ट"}].map(e=>a.jsx("button",{onClick:()=>x(e.id),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${o===e.id?"bg-bhagwa-400 text-white":"text-gray-600 hover:text-gray-900"}`,children:e.label},e.id))})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,a.jsxs)("h3",{className:"text-xl font-serif font-bold mb-4",children:["rashi"===o&&"Rashi Chart (Birth Chart)","navamsa"===o&&"Navamsa Chart (D9)","dashamsa"===o&&"Dashamsa Chart (D10)"]}),a.jsx("div",{className:"bg-gray-100 h-80 rounded-lg flex items-center justify-center relative",children:a.jsx("div",{className:"grid grid-cols-4 grid-rows-4 w-72 h-72 border-2 border-bhagwa-400",children:Array.from({length:16},(e,s)=>{let t=s<4?s+1:s<8?12-(s-4):s<12?9-(s-8):4+(s-12);return(0,a.jsxs)("div",{className:"border border-gray-300 flex items-center justify-center text-xs font-medium relative bg-white",children:[a.jsx("span",{className:"absolute top-1 left-1 text-bhagwa-600 text-xs",children:t}),"rashi"===o&&(0,a.jsxs)(a.Fragment,{children:[1===t&&a.jsx("span",{className:"text-red-600 text-xs",children:"Ma"}),5===t&&a.jsx("span",{className:"text-orange-600 text-xs",children:"Su"}),8===t&&a.jsx("span",{className:"text-blue-600 text-xs",children:"Mo"}),6===t&&a.jsx("span",{className:"text-green-600 text-xs",children:"Me"}),9===t&&a.jsx("span",{className:"text-purple-600 text-xs",children:"Ju"}),4===t&&a.jsx("span",{className:"text-pink-600 text-xs",children:"Ve"}),10===t&&a.jsx("span",{className:"text-gray-600 text-xs",children:"Sa"})]}),"navamsa"===o&&(0,a.jsxs)(a.Fragment,{children:[2===t&&a.jsx("span",{className:"text-red-600 text-xs",children:"Ma"}),7===t&&a.jsx("span",{className:"text-orange-600 text-xs",children:"Su"}),11===t&&a.jsx("span",{className:"text-blue-600 text-xs",children:"Mo"}),3===t&&a.jsx("span",{className:"text-green-600 text-xs",children:"Me"}),1===t&&a.jsx("span",{className:"text-purple-600 text-xs",children:"Ju"}),9===t&&a.jsx("span",{className:"text-pink-600 text-xs",children:"Ve"}),12===t&&a.jsx("span",{className:"text-gray-600 text-xs",children:"Sa"})]}),"dashamsa"===o&&(0,a.jsxs)(a.Fragment,{children:[3===t&&a.jsx("span",{className:"text-red-600 text-xs",children:"Ma"}),8===t&&a.jsx("span",{className:"text-orange-600 text-xs",children:"Su"}),2===t&&a.jsx("span",{className:"text-blue-600 text-xs",children:"Mo"}),7===t&&a.jsx("span",{className:"text-green-600 text-xs",children:"Me"}),11===t&&a.jsx("span",{className:"text-purple-600 text-xs",children:"Ju"}),5===t&&a.jsx("span",{className:"text-pink-600 text-xs",children:"Ve"}),1===t&&a.jsx("span",{className:"text-gray-600 text-xs",children:"Sa"})]})]},s)})})}),a.jsx("div",{className:"mt-4 text-sm text-gray-600",children:(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Legend:"})," Su-Sun, Mo-Moon, Ma-Mars, Me-Mercury, Ju-Jupiter, Ve-Venus, Sa-Saturn"]})})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4",children:"Birth Details"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Name:"}),a.jsx("span",{children:s.name})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Date of Birth:"}),a.jsx("span",{children:s.dateOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Time of Birth:"}),a.jsx("span",{children:s.timeOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Place of Birth:"}),a.jsx("span",{children:s.placeOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Sun Sign:"}),a.jsx("span",{children:"Leo ♌"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Moon Sign:"}),a.jsx("span",{children:"Scorpio ♏"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"font-medium",children:"Ascendant:"}),a.jsx("span",{children:"Gemini ♊"})]})]})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4",children:"Planetary Positions"}),a.jsx("div",{className:"space-y-2",children:[{planet:"Sun (सूर्य)",sign:"Leo",degree:"15\xb023'"},{planet:"Moon (चंद्र)",sign:"Scorpio",degree:"8\xb045'"},{planet:"Mars (मंगल)",sign:"Aries",degree:"22\xb012'"},{planet:"Mercury (बुध)",sign:"Virgo",degree:"3\xb056'"},{planet:"Jupiter (बृहस्पति)",sign:"Sagittarius",degree:"18\xb034'"},{planet:"Venus (शुक्र)",sign:"Cancer",degree:"12\xb028'"},{planet:"Saturn (शनि)",sign:"Capricorn",degree:"25\xb017'"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.planet,":"]}),(0,a.jsxs)("span",{children:[e.sign," ",e.degree]})]},s))})]}),(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-xl font-serif font-bold mb-4",children:"Key Insights"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Personality Traits"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Strong leadership qualities, creative nature, and natural charisma. You have a magnetic personality that draws people to you."})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Career Prospects"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Excellent potential in creative fields, management, or entrepreneurship. Your innovative ideas will bring success."})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Relationships"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Deep emotional connections are important to you. You seek meaningful relationships and are a loyal partner."})]})]})]})]}),(0,a.jsxs)("div",{className:"text-center mt-8",children:[a.jsx("button",{className:"spiritual-button text-lg py-3 px-8 mr-4",children:"Download Full Report"}),a.jsx("button",{className:"spiritual-button-secondary text-lg py-3 px-8",children:"Book Consultation"})]})]})})})]})}},9407:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/kundli/page.tsx`),{__esModule:i,$$typeof:n}=a,l=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(1512));module.exports=a})();