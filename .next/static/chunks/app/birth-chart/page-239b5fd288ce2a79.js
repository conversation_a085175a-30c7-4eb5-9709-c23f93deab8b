(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[588],{6960:function(e,s,a){Promise.resolve().then(a.bind(a,6594))},6594:function(e,s,a){"use strict";a.r(s);var t=a(3827),i=a(4090),n=a(8371),l=a(2635),r=a(3665);s.default=()=>{let{t:e}=(0,l.$G)(["common","home"]),[s,a]=(0,i.useState)({name:"",gender:"",dateOfBirth:"",timeOfBirth:"",placeOfBirth:"",latitude:"",longitude:""}),[c,d]=(0,i.useState)(!1),[m,o]=(0,i.useState)("chart"),x=e=>{let{name:s,value:t}=e.target;a(e=>({...e,[s]:t}))};return(0,t.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,t.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,t.jsx)("div",{className:"absolute top-10 left-10",children:(0,t.jsx)(r.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),(0,t.jsx)("div",{className:"absolute bottom-10 right-10",children:(0,t.jsx)(r.pO,{size:120,className:"text-lotus-pink-400"})}),(0,t.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:(0,t.jsx)(r.UW,{size:200,className:"text-divine-purple-300 mandala-rotate"})})]}),(0,t.jsx)("div",{className:"spiritual-container text-center relative z-10",children:(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,t.jsx)("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Birth Chart Analysis"}),(0,t.jsx)("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Comprehensive natal chart calculations with detailed planetary positions, house placements, and aspect analysis"})]})})]}),(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsxs)("div",{className:"spiritual-container",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Advanced Birth Chart Features"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Detailed astrological analysis with modern calculations"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:r.N8,title:"Precise Calculations",titleHi:"सटीक गणना",description:"Accurate planetary positions using Swiss Ephemeris"},{icon:r.UW,title:"House Analysis",titleHi:"भाव विश्लेषण",description:"Detailed analysis of all 12 houses and their significance"},{icon:r.ht,title:"Aspect Patterns",titleHi:"दृष्टि योग",description:"Planetary aspects and their influences on your life"},{icon:r.pO,title:"Dasha Periods",titleHi:"दशा काल",description:"Planetary periods and their timing effects"}].map((e,s)=>{let a=e.icon;return(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,t.jsx)(a,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title)})})]})}),(0,t.jsx)("section",{className:"py-16 bg-gray-50",children:(0,t.jsx)("div",{className:"spiritual-container max-w-2xl mx-auto",children:(0,t.jsx)(n.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h2",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:"Generate Your Birth Chart"}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),d(!0)},className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,t.jsx)("input",{type:"text",name:"name",value:s.name,onChange:x,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,t.jsxs)("select",{name:"gender",value:s.gender,onChange:x,className:"spiritual-input",required:!0,children:[(0,t.jsx)("option",{value:"",children:"Select Gender"}),(0,t.jsx)("option",{value:"male",children:"Male"}),(0,t.jsx)("option",{value:"female",children:"Female"}),(0,t.jsx)("option",{value:"other",children:"Other"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,t.jsx)("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:x,className:"spiritual-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time of Birth *"}),(0,t.jsx)("input",{type:"time",name:"timeOfBirth",value:s.timeOfBirth,onChange:x,className:"spiritual-input",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Place of Birth *"}),(0,t.jsx)("input",{type:"text",name:"placeOfBirth",value:s.placeOfBirth,onChange:x,className:"spiritual-input",placeholder:"City, State, Country",required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Latitude (Optional)"}),(0,t.jsx)("input",{type:"text",name:"latitude",value:s.latitude,onChange:x,className:"spiritual-input",placeholder:"e.g., 25.3176"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Longitude (Optional)"}),(0,t.jsx)("input",{type:"text",name:"longitude",value:s.longitude,onChange:x,className:"spiritual-input",placeholder:"e.g., 82.9739"})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("button",{type:"submit",className:"spiritual-button text-lg py-3 px-8 sacred-glow",children:"Generate Birth Chart"})})]})]})})})}),c&&(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsx)("div",{className:"spiritual-container",children:(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,t.jsxs)("h2",{className:"text-3xl font-serif font-bold mb-8 text-center gradient-text",children:["Birth Chart - ",s.name]}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-1 flex space-x-1",children:[{id:"chart",label:"Birth Chart",labelHi:"जन्म कुंडली"},{id:"planets",label:"Planetary Positions",labelHi:"ग्रह स्थिति"},{id:"houses",label:"House Analysis",labelHi:"भाव विश्लेषण"},{id:"aspects",label:"Aspects",labelHi:"दृष्टि योग"}].map(e=>(0,t.jsx)("button",{onClick:()=>o(e.id),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(m===e.id?"bg-bhagwa-400 text-white":"text-gray-600 hover:text-gray-900"),children:e.label},e.id))})}),"chart"===m&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-4",children:"Birth Chart (Rashi Chart)"}),(0,t.jsx)("div",{className:"bg-gray-100 h-96 rounded-lg flex items-center justify-center relative",children:(0,t.jsx)("div",{className:"grid grid-cols-4 grid-rows-4 w-80 h-80 border-2 border-bhagwa-400",children:Array.from({length:16},(e,s)=>{let a=s<4?s+1:s<8?12-(s-4):s<12?9-(s-8):4+(s-12);return(0,t.jsxs)("div",{className:"border border-gray-300 flex items-center justify-center text-xs font-medium relative",children:[(0,t.jsx)("span",{className:"absolute top-1 left-1 text-bhagwa-600",children:a}),1===a&&(0,t.jsx)("span",{className:"text-red-600",children:"Ma"}),5===a&&(0,t.jsx)("span",{className:"text-orange-600",children:"Su"}),8===a&&(0,t.jsx)("span",{className:"text-blue-600",children:"Mo"}),6===a&&(0,t.jsx)("span",{className:"text-green-600",children:"Me"}),9===a&&(0,t.jsx)("span",{className:"text-purple-600",children:"Ju"}),4===a&&(0,t.jsx)("span",{className:"text-pink-600",children:"Ve"}),10===a&&(0,t.jsx)("span",{className:"text-gray-600",children:"Sa"})]},s)})})}),(0,t.jsx)("div",{className:"mt-4 text-sm text-gray-600",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Legend:"})," Su-Sun, Mo-Moon, Ma-Mars, Me-Mercury, Ju-Jupiter, Ve-Venus, Sa-Saturn"]})})]}),(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-4",children:"Chart Information"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Name:"}),(0,t.jsx)("span",{children:s.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Date of Birth:"}),(0,t.jsx)("span",{children:s.dateOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Time of Birth:"}),(0,t.jsx)("span",{children:s.timeOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Place of Birth:"}),(0,t.jsx)("span",{children:s.placeOfBirth})]}),(0,t.jsx)("hr",{className:"my-4"}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Ascendant (Lagna):"}),(0,t.jsx)("span",{children:"Gemini ♊ 12\xb034'"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Sun Sign:"}),(0,t.jsx)("span",{children:"Leo ♌"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Moon Sign:"}),(0,t.jsx)("span",{children:"Scorpio ♏"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Nakshatra:"}),(0,t.jsx)("span",{children:"Anuradha"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Tithi:"}),(0,t.jsx)("span",{children:"Shukla Paksha Saptami"})]})]})]})]}),"planets"===m&&(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-6",children:"Planetary Positions"}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full text-sm",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"text-left py-3 px-4 font-semibold",children:"Planet"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-semibold",children:"Sign"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-semibold",children:"Degree"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-semibold",children:"House"}),(0,t.jsx)("th",{className:"text-left py-3 px-4 font-semibold",children:"Status"})]})}),(0,t.jsx)("tbody",{children:[{planet:"Sun",planetHi:"सूर्य",sign:"Leo",signHi:"सिंह",degree:"15\xb023'",house:5,retrograde:!1},{planet:"Moon",planetHi:"चंद्र",sign:"Scorpio",signHi:"वृश्चिक",degree:"8\xb045'",house:8,retrograde:!1},{planet:"Mars",planetHi:"मंगल",sign:"Aries",signHi:"मेष",degree:"22\xb012'",house:1,retrograde:!1},{planet:"Mercury",planetHi:"बुध",sign:"Virgo",signHi:"कन्या",degree:"3\xb056'",house:6,retrograde:!0},{planet:"Jupiter",planetHi:"बृहस्पति",sign:"Sagittarius",signHi:"धनु",degree:"18\xb034'",house:9,retrograde:!1},{planet:"Venus",planetHi:"शुक्र",sign:"Cancer",signHi:"कर्क",degree:"12\xb028'",house:4,retrograde:!1},{planet:"Saturn",planetHi:"शनि",sign:"Capricorn",signHi:"मकर",degree:"25\xb017'",house:10,retrograde:!1},{planet:"Rahu",planetHi:"राहु",sign:"Gemini",signHi:"मिथुन",degree:"14\xb033'",house:3,retrograde:!0},{planet:"Ketu",planetHi:"केतु",sign:"Sagittarius",signHi:"धनु",degree:"14\xb033'",house:9,retrograde:!0}].map((e,s)=>(0,t.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.planet}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.planetHi})]})}),(0,t.jsx)("td",{className:"py-3 px-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.sign}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.signHi})]})}),(0,t.jsx)("td",{className:"py-3 px-4 font-mono",children:e.degree}),(0,t.jsx)("td",{className:"py-3 px-4",children:e.house}),(0,t.jsx)("td",{className:"py-3 px-4",children:e.retrograde?(0,t.jsx)("span",{className:"text-red-600 text-xs",children:"Retrograde"}):(0,t.jsx)("span",{className:"text-green-600 text-xs",children:"Direct"})})]},s))})]})})]}),"houses"===m&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{number:1,name:"Ascendant",nameHi:"लग्न",significance:"Self, personality, physical appearance"},{number:2,name:"Wealth",nameHi:"धन",significance:"Money, family, speech"},{number:3,name:"Courage",nameHi:"साहस",significance:"Siblings, courage, communication"},{number:4,name:"Home",nameHi:"गृह",significance:"Mother, home, property"},{number:5,name:"Children",nameHi:"संतान",significance:"Children, creativity, education"},{number:6,name:"Enemies",nameHi:"शत्रु",significance:"Health, enemies, service"},{number:7,name:"Partnership",nameHi:"साझेदारी",significance:"Marriage, partnerships"},{number:8,name:"Longevity",nameHi:"आयु",significance:"Longevity, transformation"},{number:9,name:"Fortune",nameHi:"भाग्य",significance:"Father, luck, spirituality"},{number:10,name:"Career",nameHi:"कर्म",significance:"Career, reputation, status"},{number:11,name:"Gains",nameHi:"लाभ",significance:"Gains, friends, aspirations"},{number:12,name:"Loss",nameHi:"व्यय",significance:"Expenses, spirituality, foreign lands"}].map(e=>(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsxs)("div",{className:"flex items-center mb-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-bhagwa-400 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3",children:e.number}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.nameHi})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.significance}),(0,t.jsxs)("div",{className:"mt-3 text-xs",children:[(0,t.jsx)("span",{className:"font-medium",children:"Planets: "}),1===e.number&&(0,t.jsx)("span",{className:"text-red-600",children:"Mars"}),5===e.number&&(0,t.jsx)("span",{className:"text-orange-600",children:"Sun"}),8===e.number&&(0,t.jsx)("span",{className:"text-blue-600",children:"Moon"}),6===e.number&&(0,t.jsx)("span",{className:"text-green-600",children:"Mercury (R)"}),9===e.number&&(0,t.jsx)("span",{className:"text-purple-600",children:"Jupiter, Ketu"}),4===e.number&&(0,t.jsx)("span",{className:"text-pink-600",children:"Venus"}),10===e.number&&(0,t.jsx)("span",{className:"text-gray-600",children:"Saturn"}),3===e.number&&(0,t.jsx)("span",{className:"text-indigo-600",children:"Rahu"}),![1,3,4,5,6,8,9,10].includes(e.number)&&(0,t.jsx)("span",{className:"text-gray-400",children:"Empty"})]})]},e.number))}),"aspects"===m&&(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-6",children:"Planetary Aspects"}),(0,t.jsx)("div",{className:"space-y-4",children:[{from:"Sun",to:"Mars",type:"Trine",strength:"Strong",effect:"Positive"},{from:"Moon",to:"Venus",type:"Sextile",strength:"Moderate",effect:"Positive"},{from:"Mercury",to:"Jupiter",type:"Opposition",strength:"Weak",effect:"Challenging"},{from:"Saturn",to:"Mars",type:"Square",strength:"Strong",effect:"Challenging"}].map((e,s)=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-semibold",children:e.from}),(0,t.jsx)("span",{className:"text-gray-500",children:"→"}),(0,t.jsx)("span",{className:"font-semibold",children:e.to})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("Positive"===e.effect?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.effect}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:e.type})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Strength: "}),e.strength]})]},s))})]}),(0,t.jsxs)("div",{className:"text-center mt-8",children:[(0,t.jsx)("button",{className:"spiritual-button text-lg py-3 px-8 mr-4",children:"Download Detailed Report"}),(0,t.jsx)("button",{className:"spiritual-button-secondary text-lg py-3 px-8",children:"Book Consultation"})]})]})})})]})}}},function(e){e.O(0,[371,635,665,971,69,744],function(){return e(e.s=6960)}),_N_E=e.O()}]);