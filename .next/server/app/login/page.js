(()=>{var e={};e.id=626,e.ids=[626],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7578:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(482),r=t(9108),n=t(2563),i=t.n(n),l=t(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1613)),"/Users/<USER>/Downloads/astrology/src/app/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/login/page.tsx"],m="/login/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5937:(e,s,t)=>{Promise.resolve().then(t.bind(t,9682))},9682:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(5344),r=t(3729),n=t(6506),i=t(7292),l=t(9758),o=t(1902),c=t(7623),d=t(7993);let m=()=>{let{t:e}=(0,l.$G)(["common"]),[s,t]=(0,r.useState)({email:"",password:""}),[m,x]=(0,r.useState)(!1),[u,p]=(0,r.useState)(!1),h=e=>{let{name:s,value:a}=e.target;t(e=>({...e,[s]:a}))},g=async e=>{e.preventDefault(),p(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Login successful! Welcome to Kashi Vedic."),p(!1)};return(0,a.jsxs)("div",{className:"min-h-screen pt-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30",children:[(0,a.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[a.jsx("div",{className:"absolute top-20 left-10 opacity-10",children:a.jsx(o.ht,{size:150,className:"text-saffron-400 om-pulse"})}),a.jsx("div",{className:"absolute bottom-20 right-10 opacity-10",children:a.jsx(o.pO,{size:180,className:"text-lotus-pink-400"})})]}),a.jsx("div",{className:"spiritual-container py-20 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"spiritual-card-enhanced",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:a.jsx(o.ht,{className:"w-8 h-8 text-white"})}),a.jsx("h1",{className:"text-2xl font-serif font-bold gradient-text mb-2",children:"Welcome Back"}),a.jsx("p",{className:"text-gray-600",children:"Sign in to your spiritual journey"})]}),(0,a.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),a.jsx("input",{type:"email",name:"email",value:s.email,onChange:h,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("input",{type:m?"text":"password",name:"password",value:s.password,onChange:h,className:"spiritual-input pr-10",placeholder:"Enter your password",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!m),children:m?a.jsx(c.Z,{className:"h-5 w-5 text-gray-400"}):a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-saffron-600 focus:ring-saffron-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),a.jsx("div",{className:"text-sm",children:a.jsx(n.default,{href:"/forgot-password",className:"text-saffron-600 hover:text-saffron-500",children:"Forgot password?"})})]}),a.jsx("button",{type:"submit",disabled:u,className:"w-full spiritual-button py-3 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Signing in...":"Sign In"})]}),a.jsx("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-sm",children:a.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,a.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[a.jsx("span",{className:"text-lg mr-2",children:"\uD83D\uDCE7"}),"Google"]}),(0,a.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50",children:[a.jsx("span",{className:"text-lg mr-2",children:"\uD83D\uDCF1"}),"Phone"]})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",a.jsx(n.default,{href:"/register",className:"text-saffron-600 hover:text-saffron-500 font-medium",children:"Create one here"})]})})]}),a.jsx(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mt-8 text-center",children:(0,a.jsxs)("div",{className:"spiritual-card-enhanced",children:[a.jsx("h3",{className:"text-lg font-serif font-bold mb-3 gradient-text",children:"Why Join Kashi Vedic?"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Personalized horoscope and predictions"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Access to expert astrologers"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Detailed kundli analysis and reports"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("span",{className:"text-saffron-500 mr-2",children:"✓"}),"Spiritual guidance and remedies"]})]})]})})]})})]})}},1613:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let a=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/login/page.tsx`),{__esModule:r,$$typeof:n}=a,i=a.default},7993:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(3729);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},7623:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(3729);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[651,713],()=>t(7578));module.exports=a})();