"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[635],{2377:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},2635:function(e,n,t){let a;t.d(n,{a3:function(){return O},Db:function(){return v},$G:function(){return L}});var s=t(4090);t(2377),Object.create(null);let r={};function o(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];d(n[0])&&r[n[0]]||(d(n[0])&&(r[n[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];d(n[0])&&(n[0]="react-i18next:: ".concat(n[0])),console.warn(...n)}}(...n))}let i=(e,n)=>()=>{if(e.isInitialized)n();else{let t=()=>{setTimeout(()=>{e.off("initialized",t)},0),n()};e.on("initialized",t)}},c=(e,n,t)=>{e.loadNamespaces(n,i(e,t))},l=(e,n,t,a)=>{d(t)&&(t=[t]),t.forEach(n=>{0>e.options.ns.indexOf(n)&&e.options.ns.push(n)}),e.loadLanguages(n,i(e,a))},u=function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=n.languages[0],s=!!n.options&&n.options.fallbackLng,r=n.languages[n.languages.length-1];if("cimode"===a.toLowerCase())return!0;let o=(e,t)=>{let a=n.services.backendConnector.state["".concat(e,"|").concat(t)];return -1===a||2===a};return(!(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1)||!n.services.backendConnector.backend||!n.isLanguageChangingTo||!!o(n.isLanguageChangingTo,e))&&!!(n.hasResourceBundle(a,e)||!n.services.backendConnector.backend||n.options.resources&&!n.options.partialBundledLanguages||o(a,e)&&(!s||o(r,e)))},g=function(e,n){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.languages&&n.languages.length?void 0!==n.options.ignoreJSONStructure?n.hasLoadedNamespace(e,{lng:t.lng,precheck:(n,a)=>{if(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!a(n.isLanguageChangingTo,e))return!1}}):u(e,n,t):(o("i18n.languages were undefined or empty",n.languages),!0)},d=e=>"string"==typeof e,p=e=>"object"==typeof e&&null!==e,f=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,h={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},b=e=>h[e],m={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(f,b)},N=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};m={...m,...e}},y=()=>m,k=e=>{a=e},C=()=>a,v={type:"3rdParty",init(e){N(e.options.react),k(e)}},w=(0,s.createContext)();class x{addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}constructor(){this.getUsedNamespaces=()=>Object.keys(this.usedNamespaces),this.usedNamespaces={}}}let E=(e,n)=>{let t=(0,s.useRef)();return(0,s.useEffect)(()=>{t.current=n?t.current:e},[e,n]),t.current},I=(e,n,t,a)=>e.getFixedT(n,t,a),S=(e,n,t,a)=>(0,s.useCallback)(I(e,n,t,a),[e,n,t,a]),L=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{i18n:t}=n,{i18n:a,defaultNS:r}=(0,s.useContext)(w)||{},i=t||a||C();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new x),!i){o("You will need to pass in an i18next instance by using initReactI18next");let e=(e,n)=>d(n)?n:p(n)&&d(n.defaultValue)?n.defaultValue:Array.isArray(e)?e[e.length-1]:e,n=[e,{},!1];return n.t=e,n.i18n={},n.ready=!1,n}i.options.react&&void 0!==i.options.react.wait&&o("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let u={...y(),...i.options.react,...n},{useSuspense:f,keyPrefix:h}=u,b=e||r||i.options&&i.options.defaultNS;b=d(b)?[b]:b||["translation"],i.reportNamespaces.addUsedNamespaces&&i.reportNamespaces.addUsedNamespaces(b);let m=(i.isInitialized||i.initializedStoreOnce)&&b.every(e=>g(e,i,u)),N=S(i,n.lng||null,"fallback"===u.nsMode?b:b[0],h),k=()=>N,v=()=>I(i,n.lng||null,"fallback"===u.nsMode?b:b[0],h),[L,O]=(0,s.useState)(k),T=b.join();n.lng&&(T="".concat(n.lng).concat(T));let z=E(T),j=(0,s.useRef)(!0);(0,s.useEffect)(()=>{let{bindI18n:e,bindI18nStore:t}=u;j.current=!0,m||f||(n.lng?l(i,n.lng,b,()=>{j.current&&O(v)}):c(i,b,()=>{j.current&&O(v)})),m&&z&&z!==T&&j.current&&O(v);let a=()=>{j.current&&O(v)};return e&&i&&i.on(e,a),t&&i&&i.store.on(t,a),()=>{j.current=!1,e&&i&&e.split(" ").forEach(e=>i.off(e,a)),t&&i&&t.split(" ").forEach(e=>i.store.off(e,a))}},[i,T]),(0,s.useEffect)(()=>{j.current&&m&&O(k)},[i,h,m]);let A=[L,i,m];if(A.t=L,A.i18n=i,A.ready=m,m||!m&&!f)return A;throw new Promise(e=>{n.lng?l(i,n.lng,b,()=>e()):c(i,b,()=>e())})};function O(e){let{i18n:n,defaultNS:t,children:a}=e,r=(0,s.useMemo)(()=>({i18n:n,defaultNS:t}),[n,t]);return(0,s.createElement)(w.Provider,{value:r},a)}}}]);