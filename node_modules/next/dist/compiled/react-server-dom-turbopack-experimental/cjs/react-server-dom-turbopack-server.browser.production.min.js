/*
 React
 react-server-dom-turbopack-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react-dom"),ba=require("react"),l=null,n=0;function p(a,b){if(0!==b.byteLength)if(512<b.byteLength)0<n&&(a.enqueue(new Uint8Array(l.buffer,0,n)),l=new Uint8Array(512),n=0),a.enqueue(b);else{var d=l.length-n;d<b.byteLength&&(0===d?a.enqueue(l):(l.set(b.subarray(0,d),n),a.enqueue(l),b=b.subarray(d)),l=new Uint8Array(512),n=0);l.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function u(a,b,d){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:d}})}var da=Function.prototype.bind,ea=Array.prototype.slice;function fa(){var a=da.apply(this,arguments);if(this.$$typeof===t){var b=ea.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:fa}})}return a}
var ha=Promise.prototype,ia={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ja={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=u({},a.$$id,!0),e=new Proxy(c,ja);a.status="fulfilled";a.value=e;return a.then=u(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,ia));return c},getPrototypeOf:function(){return ha},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},ra={prefetchDNS:ka,preconnect:la,preload:ma,preloadModule:na,preinitStyle:oa,preinitScript:pa,preinitModuleScript:qa};function ka(a){if("string"===typeof a&&a){var b=v?v:null;if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),w(b,"D",a))}}}function la(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?w(d,"C",[a,b]):w(d,"C",a))}}}
function ma(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=x(d))?w(c,"L",[a,b,d]):w(c,"L",[a,b]))}}}function na(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"m",[a,b]):w(d,"m",a)}}}
function oa(a,b,d){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=x(d))?w(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?w(c,"S",[a,b]):w(c,"S",a)}}}function pa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"X",[a,b]):w(d,"X",a)}}}
function qa(a,b){if("string"===typeof a){var d=v?v:null;if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=x(b))?w(d,"M",[a,b]):w(d,"M",a)}}}function x(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}
var ta=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,y=Symbol.for("react.element"),ua=Symbol.for("react.fragment"),va=Symbol.for("react.provider"),wa=Symbol.for("react.server_context"),xa=Symbol.for("react.forward_ref"),ya=Symbol.for("react.suspense"),za=Symbol.for("react.suspense_list"),Aa=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),Ba=Symbol.for("react.default_value"),Ca=Symbol.for("react.memo_cache_sentinel"),B=Symbol.for("react.postpone"),Da=Symbol.iterator,C=null;
function Ea(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Ea(a,d);b.context._currentValue=b.value}}}function Fa(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Fa(a)}
function Ga(a){var b=a.parent;null!==b&&Ga(b);a.context._currentValue=a.value}function Ha(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Ea(a,b):Ha(a,b)}
function Ia(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?Ea(a,d):Ia(a,d);b.context._currentValue=b.value}function Ja(a){var b=C;b!==a&&(null===b?Ga(a):null===a?Fa(b):b.depth===a.depth?Ea(b,a):b.depth>a.depth?Ha(b,a):Ia(b,a),C=a)}function Ka(a,b){var d=a._currentValue;a._currentValue=b;var c=C;return C=a={parent:c,depth:null===c?0:c.depth+1,context:a,parentValue:d,value:b}}var La=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ma(){}function Na(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Ma,Ma),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Oa=b;throw La;}}var Oa=null;
function Pa(){if(null===Oa)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Oa;Oa=null;return a}var D=null,Qa=0,E=null;function Ra(){var a=E;E=null;return a}function Sa(a){return a._currentValue}
var Wa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:F,useTransition:F,readContext:Sa,useContext:Sa,useReducer:F,useRef:F,useState:F,useInsertionEffect:F,useLayoutEffect:F,useImperativeHandle:F,useEffect:F,useId:Ta,useSyncExternalStore:F,useCacheRefresh:function(){return Ua},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Ca;return b},use:Va};
function F(){throw Error("This Hook is not supported in Server Components.");}function Ua(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ta(){if(null===D)throw Error("useId can only be used while React is rendering");var a=D.identifierCount++;return":"+D.identifierPrefix+"S"+a.toString(32)+":"}
function Va(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Qa;Qa+=1;null===E&&(E=[]);return Na(E,a,b)}if(a.$$typeof===wa)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Xa(){return(new AbortController).signal}function Ya(){var a=v?v:null;return a?a.cache:new Map}
var Za={getCacheSignal:function(){var a=Ya(),b=a.get(Xa);void 0===b&&(b=Xa(),a.set(Xa,b));return b},getCacheForType:function(a){var b=Ya(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},$a=Array.isArray,ab=Object.getPrototypeOf;function bb(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function cb(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if($a(a))return"[...]";a=bb(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function db(a){if("string"===typeof a)return a;switch(a){case ya:return"Suspense";case za:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case xa:return db(a.render);case Aa:return db(a.type);case z:var b=a._payload;a=a._init;try{return db(a(b))}catch(d){}}return""}
function H(a,b){var d=bb(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if($a(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?H(g):cb(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===y)e="<"+db(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?H(h):
cb(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var eb=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fb=eb.ContextRegistry,I=ba.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!I)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var gb=Object.prototype,J=JSON.stringify,hb=I.TaintRegistryObjects,K=I.TaintRegistryValues,ib=I.TaintRegistryByteLengths,jb=I.TaintRegistryPendingRequests,kb=I.ReactCurrentCache,lb=eb.ReactCurrentDispatcher;function L(a){throw Error(a);}
function mb(a){a=a.taintCleanupQueue;jb.delete(a);for(var b=0;b<a.length;b++){var d=a[b],c=K.get(d);void 0!==c&&(1===c.count?K.delete(d):c.count--)}a.length=0}function nb(a){console.error(a)}function ob(){}
function pb(a,b,d,c,e,f){if(null!==kb.current&&kb.current!==Za)throw Error("Currently React only supports one RSC renderer at a time.");ta.current=ra;kb.current=Za;var g=new Set,k=[],h=[];jb.add(h);var m=new Set,A={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:m,abortableTasks:g,pingedTasks:k,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:h,onError:void 0===d?nb:d,onPostpone:void 0===f?ob:f,toJSON:function(sa,G){return qb(A,this,sa,G)}};A.pendingChunks++;b=rb(c);a=sb(A,a,b,g);k.push(a);return A}var v=null,tb={};
function ub(a,b){a.pendingChunks++;var d=sb(a,null,C,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,vb(a,d),d.id;case "rejected":var c=b.reason;"object"===typeof c&&null!==c&&c.$$typeof===B?(M(a,c.message),N(a,d.id)):(c=O(a,c),P(a,d.id,c));return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=
e;vb(a,d)},function(e){"object"===typeof e&&null!==e&&e.$$typeof===B?(M(a,e.message),N(a,d.id)):(d.status=4,e=O(a,e),P(a,d.id,e));a.abortableTasks.delete(d);null!==a.destination&&Q(a,a.destination)});return d.id}function w(a,b,d){d=J(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;d=q.encode(b+d+"\n");a.completedHintChunks.push(d);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(d=a.destination,a.flushScheduled=!0,Q(a,d))}
function wb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}function xb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:z,_payload:a,_init:wb}}
function R(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===r)return[y,b,d,e];Qa=0;E=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:xb(e):e}if("string"===typeof b)return[y,b,d,e];if("symbol"===typeof b)return b===ua?e.children:[y,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===r)return[y,b,d,e];switch(b.$$typeof){case z:var g=
b._init;b=g(b._payload);return R(a,b,d,c,e,f);case xa:return a=b.render,Qa=0,E=f,a(e,void 0);case Aa:return R(a,b.type,d,c,e,f);case va:return Ka(b._context,e.value),[y,b,d,{value:e.value,children:e.children,__pop:tb}]}}throw Error("Unsupported Server Component type: "+cb(b));}function vb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,yb(a))}
function sb(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return vb(a,e)},thenableState:null};c.add(e);return e}function S(a){return"$"+a.toString(16)}function zb(a,b,d){a=J(d);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function Ab(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===y&&"1"===d?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var m=k[h];if(m)g=m.name;else{var A=h.lastIndexOf("#");-1!==A&&(g=h.slice(A+1),m=k[h.slice(0,A)]);if(!m)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var sa=!0===c.$$async?[m.id,m.chunks,g,1]:[m.id,m.chunks,
g];a.pendingChunks++;var G=a.nextChunkId++,Sb=J(sa),Tb=G.toString(16)+":I"+Sb+"\n",Ub=q.encode(Tb);a.completedImportChunks.push(Ub);f.set(e,G);return b[0]===y&&"1"===d?"$L"+G.toString(16):S(G)}catch(Vb){return a.pendingChunks++,b=a.nextChunkId++,d=O(a,Vb),P(a,b,d),S(b)}}function T(a,b){a.pendingChunks++;b=sb(a,b,C,a.abortableTasks);Bb(a,b);return b.id}
function U(a,b,d){if(ib.has(d.byteLength)){var c=K.get(String.fromCharCode.apply(String,new Uint8Array(d.buffer,d.byteOffset,d.byteLength)));void 0!==c&&L(c.message)}a.pendingChunks+=2;c=a.nextChunkId++;var e=new Uint8Array(d.buffer,d.byteOffset,d.byteLength);d=512<d.byteLength?e.slice():e;e=d.byteLength;b=c.toString(16)+":"+b+e.toString(16)+",";b=q.encode(b);a.completedRegularChunks.push(b,d);return S(c)}var V=!1;
function qb(a,b,d,c){switch(c){case y:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===y||c.$$typeof===z);)try{switch(c.$$typeof){case y:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=T(a,c);return S(g)}if(V===c)V=null;else return S(f)}else e.set(c,-1);var k=c;c=R(a,k.type,k.key,k.ref,k.props,null);break;case z:var h=c._init;c=h(c._payload)}}catch(m){d=m===La?Pa():m;if("object"===typeof d&&null!==d){if("function"===typeof d.then)return a.pendingChunks++,a=sb(a,c,C,a.abortableTasks),
c=a.ping,d.then(c,c),a.thenableState=Ra(),"$L"+a.id.toString(16);if(d.$$typeof===B)return c=d,a.pendingChunks++,d=a.nextChunkId++,M(a,c.message),N(a,d),"$L"+d.toString(16)}a.pendingChunks++;c=a.nextChunkId++;d=O(a,d);P(a,c,d);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){e=hb.get(c);void 0!==e&&L(e);if(c.$$typeof===r)return Ab(a,b,d,c);b=a.writtenObjects;e=b.get(c);if("function"===typeof c.then){if(void 0!==e)if(V===c)V=null;else return"$@"+e.toString(16);a=ub(a,c);b.set(c,
a);return"$@"+a.toString(16)}if(c.$$typeof===va)return c=c._context._globalName,b=a.writtenProviders,d=b.get(d),void 0===d&&(a.pendingChunks++,d=a.nextChunkId++,b.set(c,d),c=zb(a,d,"$P"+c),a.completedRegularChunks.push(c)),S(d);if(c===tb){a=C;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===Ba?a.context._defaultValue:c;C=a.parent;return}if(void 0!==e){if(-1===e)return a=T(a,c),S(a);if(V===c)V=null;else return S(e)}else b.set(c,
-1);if($a(c))return c;if(c instanceof Map){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d][0],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$Q"+T(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(d=0;d<c.length;d++)b=c[d],"object"===typeof b&&null!==b&&(e=a.writtenObjects,void 0===e.get(b)&&e.set(b,-1));return"$W"+T(a,c).toString(16)}if(c instanceof ArrayBuffer)return U(a,"A",new Uint8Array(c));if(c instanceof Int8Array)return U(a,"C",c);if(c instanceof
Uint8Array)return U(a,"c",c);if(c instanceof Uint8ClampedArray)return U(a,"U",c);if(c instanceof Int16Array)return U(a,"S",c);if(c instanceof Uint16Array)return U(a,"s",c);if(c instanceof Int32Array)return U(a,"L",c);if(c instanceof Uint32Array)return U(a,"l",c);if(c instanceof Float32Array)return U(a,"F",c);if(c instanceof Float64Array)return U(a,"D",c);if(c instanceof BigInt64Array)return U(a,"N",c);if(c instanceof BigUint64Array)return U(a,"m",c);if(c instanceof DataView)return U(a,"V",c);null===
c||"object"!==typeof c?a=null:(a=Da&&c[Da]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=ab(c);if(a!==gb&&(null===a||null!==ab(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){e=K.get(c);void 0!==e&&L(e.message);if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,d=a.nextChunkId++,
c=q.encode(c),b=c.byteLength,b=d.toString(16)+":T"+b.toString(16)+",",b=q.encode(b),a.completedRegularChunks.push(b,c),S(d);a="$"===c[0]?"$"+c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){e=hb.get(c);void 0!==e&&L(e);if(c.$$typeof===r)return Ab(a,b,d,c);if(c.$$typeof===t)return d=a.writtenServerReferences,
b=d.get(c),void 0!==b?a="$F"+b.toString(16):(b=c.$$bound,b={id:c.$$id,bound:b?Promise.resolve(b):null},a=T(a,b),d.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+H(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+H(b,d));}if("symbol"===typeof c){e=
a.writtenSymbols;f=e.get(c);if(void 0!==f)return S(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+H(b,d));a.pendingChunks++;d=a.nextChunkId++;b=zb(a,d,"$S"+f);a.completedImportChunks.push(b);e.set(c,d);return S(d)}if("bigint"===typeof c)return a=K.get(c),void 0!==a&&L(a.message),"$n"+c.toString(10);throw Error("Type "+typeof c+
" is not supported in Client Component props."+H(b,d));}function M(a,b){a=a.onPostpone;a(b)}function O(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}
function Cb(a,b){mb(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}function N(a,b){b=b.toString(16)+":P\n";b=q.encode(b);a.completedErrorChunks.push(b)}function P(a,b,d){d={digest:d};b=b.toString(16)+":E"+J(d)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}
function Bb(a,b){if(0===b.status){Ja(b.context);try{var d=b.model;if("object"===typeof d&&null!==d&&d.$$typeof===y){a.writtenObjects.set(d,b.id);var c=d,e=b.thenableState;b.model=d;d=R(a,c.type,c.key,c.ref,c.props,e);for(b.thenableState=null;"object"===typeof d&&null!==d&&d.$$typeof===y;)a.writtenObjects.set(d,b.id),c=d,b.model=d,d=R(a,c.type,c.key,c.ref,c.props,null)}"object"===typeof d&&null!==d&&a.writtenObjects.set(d,b.id);var f=b.id;V=d;var g=J(d,a.toJSON),k=f.toString(16)+":"+g+"\n",h=q.encode(k);
a.completedRegularChunks.push(h);a.abortableTasks.delete(b);b.status=1}catch(m){f=m===La?Pa():m;if("object"===typeof f&&null!==f){if("function"===typeof f.then){a=b.ping;f.then(a,a);b.thenableState=Ra();return}if(f.$$typeof===B){a.abortableTasks.delete(b);b.status=4;M(a,f.message);N(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;f=O(a,f);P(a,b.id,f)}}}
function yb(a){var b=lb.current;lb.current=Wa;var d=v;D=v=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)Bb(a,c[e]);null!==a.destination&&Q(a,a.destination)}catch(f){O(a,f),Cb(a,f)}finally{lb.current=b,D=null,v=d}}
function Q(a,b){l=new Uint8Array(512);n=0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)a.pendingChunks--,p(b,d[c]);d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)p(b,e[c]);e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)a.pendingChunks--,p(b,f[c]);f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)a.pendingChunks--,p(b,g[c]);g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<n&&(b.enqueue(new Uint8Array(l.buffer,0,n)),l=null,n=0)}0===a.pendingChunks&&
(mb(a),b.close())}function Db(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===B)M(a,b.message),N(a,c,b);else{var e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=O(a,e);P(a,c,f,e)}d.forEach(function(g){g.status=3;var k=S(c);g=zb(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&Q(a,a.destination)}catch(g){O(a,g),Cb(a,g)}}
function rb(a){if(a){var b=C;Ja(null);for(var d=0;d<a.length;d++){var c=a[d],e=c[0];c=c[1];if(!fb[e]){var f={$$typeof:wa,_currentValue:Ba,_currentValue2:Ba,_defaultValue:Ba,_threadCount:0,Provider:null,Consumer:null,_globalName:e};f.Provider={$$typeof:va,_context:f};fb[e]=f}Ka(fb[e],c)}a=C;Ja(b);return a}return null}
function Eb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var Fb=new Map;
function Gb(a){var b=__turbopack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function Hb(){}
function Ib(a){for(var b=a[1],d=[],c=0;c<b.length;c++){var e=b[c],f=Fb.get(e);if(void 0===f){f=__turbopack_load__(e);d.push(f);var g=Fb.set.bind(Fb,e,null);f.then(g,Hb);Fb.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?Gb(a[0]):Promise.all(d).then(function(){return Gb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=__turbopack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Jb(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Jb.prototype=Object.create(Promise.prototype);
Jb.prototype.then=function(a,b){switch(this.status){case "resolved_model":Kb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Lb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Mb(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Lb(d,b)}}function Nb(a,b,d,c,e,f){var g=Eb(a._bundlerConfig,b);a=Ib(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=W(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Ob(c,e,f),Pb(c));return null}var X=null,Y=null;
function Kb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Qb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Mb(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Jb("resolved_model",c,null,a):new Jb("pending",null,null,a),d.set(b,c));return c}function Ob(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Lb(e,c.value))}}function Pb(a){return function(b){return Mb(a,b)}}
function Rb(a,b){a=Z(a,b);"resolved_model"===a.status&&Kb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Wb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Rb(a,c),Nb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Rb(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Rb(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Kb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Ob(c,b,d),Pb(c)),null;default:throw a.reason;}}return c}
function Xb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Wb(e,this,f,g):g}};return e}function Yb(a){Qb(a,Error("Connection closed."))}function Zb(a,b,d){var c=Eb(a,b);a=Ib(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}
function $b(a,b,d){a=Xb(b,d,a);Yb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ja)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=$b(a,b,e),c=Zb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Zb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Xb(b,"",a);b=Z(a,0);Yb(a);return b};
exports.registerClientReference=function(a,b,d){return u(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:fa}})};
exports.renderToReadableStream=function(a,b,d){var c=pb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0);if(d&&d.signal){var e=d.signal;if(e.aborted)Db(c,e.reason);else{var f=function(){Db(c,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){c.flushScheduled=null!==c.destination;yb(c)},pull:function(g){if(1===c.status)c.status=2,ca(g,c.fatalError);else if(2!==c.status&&null===
c.destination){c.destination=g;try{Q(c,g)}catch(k){O(c,k),Cb(c,k)}}},cancel:function(){}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-turbopack-server.browser.production.min.js.map
