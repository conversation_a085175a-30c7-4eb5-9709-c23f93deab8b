(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[330],{7907:function(e,t,i){"use strict";var s=i(5313);i.o(s,"usePathname")&&i.d(t,{usePathname:function(){return s.usePathname}})},5726:function(e){e.exports={style:{fontFamily:"'__Inter_a044e1', '__Inter_Fallback_a044e1'",fontStyle:"normal"},className:"__className_a044e1",variable:"__variable_a044e1"}},2854:function(e){e.exports={style:{fontFamily:"'__Noto_Sans_Devanagari_71c671', '__Noto_Sans_Devanagari_Fallback_71c671'",fontStyle:"normal"},className:"__className_71c671",variable:"__variable_71c671"}},6618:function(e){e.exports={style:{fontFamily:"'__Noto_Serif_Devanagari_4b142a', '__Noto_Serif_Devanagari_Fallback_4b142a'",fontStyle:"normal"},className:"__className_4b142a",variable:"__variable_4b142a"}},7960:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});t.Z=n},1701:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"}))});t.Z=n},8752:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))});t.Z=n},9626:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))});t.Z=n},8005:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});t.Z=n},163:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"}))});t.Z=n},4508:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9 9 10.5-3m0 6.553v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 1 1-.99-3.467l2.31-.66a2.25 2.25 0 0 0 1.632-2.163Zm0 0V2.25L9 5.25v10.303m0 0v3.75a2.25 2.25 0 0 1-1.632 2.163l-1.32.377a1.803 1.803 0 0 1-.99-3.467l2.31-.66A2.25 2.25 0 0 0 9 15.553Z"}))});t.Z=n},190:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"}))});t.Z=n},3251:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))});t.Z=n},7985:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))});t.Z=n},461:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))});t.Z=n},1070:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"}))});t.Z=n},6738:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.25 9.75 19.5 12m0 0 2.25 2.25M19.5 12l2.25-2.25M19.5 12l-2.25 2.25m-10.5-6 4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"}))});t.Z=n},8736:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=n},8621:function(e,t,i){"use strict";var s=i(4090);let n=s.forwardRef(function(e,t){let{title:i,titleId:n,...r}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},r),i?s.createElement("title",{id:n},i):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});t.Z=n},1505:function(e,t,i){"use strict";i.d(t,{ZP:function(){return et}});let s=e=>"string"==typeof e,n=()=>{let e,t;let i=new Promise((i,s)=>{e=i,t=s});return i.resolve=e,i.reject=t,i},r=e=>null==e?"":""+e,a=(e,t,i)=>{e.forEach(e=>{t[e]&&(i[e]=t[e])})},o=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(o,"."):e,c=e=>!e||s(e),h=(e,t,i)=>{let n=s(t)?t.split("."):t,r=0;for(;r<n.length-1;){if(c(e))return{};let t=l(n[r]);!e[t]&&i&&(e[t]=new i),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++r}return c(e)?{}:{obj:e,k:l(n[r])}},u=(e,t,i)=>{let{obj:s,k:n}=h(e,t,Object);if(void 0!==s||1===t.length){s[n]=i;return}let r=t[t.length-1],a=t.slice(0,t.length-1),o=h(e,a,Object);for(;void 0===o.obj&&a.length;)r="".concat(a[a.length-1],".").concat(r),(o=h(e,a=a.slice(0,a.length-1),Object))&&o.obj&&void 0!==o.obj["".concat(o.k,".").concat(r)]&&(o.obj=void 0);o.obj["".concat(o.k,".").concat(r)]=i},d=(e,t,i,s)=>{let{obj:n,k:r}=h(e,t,Object);n[r]=n[r]||[],n[r].push(i)},g=(e,t)=>{let{obj:i,k:s}=h(e,t);if(i)return i[s]},p=(e,t,i)=>{let s=g(e,i);return void 0!==s?s:g(t,i)},f=(e,t,i)=>{for(let n in t)"__proto__"!==n&&"constructor"!==n&&(n in e?s(e[n])||e[n]instanceof String||s(t[n])||t[n]instanceof String?i&&(e[n]=t[n]):f(e[n],t[n],i):e[n]=t[n]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var v={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let b=e=>s(e)?e.replace(/[&<>"'\/]/g,e=>v[e]):e;class y{getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let i=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,i),this.regExpQueue.push(e),i}constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}}let x=[" ",",","?","!",";"],k=new y(20),w=(e,t,i)=>{t=t||"",i=i||"";let s=x.filter(e=>0>t.indexOf(e)&&0>i.indexOf(e));if(0===s.length)return!0;let n=k.getRegExp("(".concat(s.map(e=>"?"===e?"\\?":e).join("|"),")")),r=!n.test(e);if(!r){let t=e.indexOf(i);t>0&&!n.test(e.substring(0,t))&&(r=!0)}return r},L=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let s=t.split(i),n=e;for(let e=0;e<s.length;){let t;if(!n||"object"!=typeof n)return;let r="";for(let a=e;a<s.length;++a)if(a!==e&&(r+=i),r+=s[a],void 0!==(t=n[r])){if(["string","number","boolean"].indexOf(typeof t)>-1&&a<s.length-1)continue;e+=a-e+1;break}n=t}return n},S=e=>e&&e.replace("_","-"),O={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class C{init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||O,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,i,n){return n&&!this.debug?null:(s(e[0])&&(e[0]="".concat(i).concat(this.prefix," ").concat(e[0])),this.logger[t](e))}create(e){return new C(this.logger,{prefix:"".concat(this.prefix,":").concat(e,":"),...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new C(this.logger,e)}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}}var R=new C;class N{on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let i=this.observers[e].get(t)||0;this.observers[e].set(t,i+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,s]=e;for(let e=0;e<s;e++)t(...i)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[s,n]=t;for(let t=0;t<n;t++)s.apply(s,[e,...i])})}constructor(){this.observers={}}}class E extends N{addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,i){let n,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,o=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?n=e.split("."):(n=[e,t],i&&(Array.isArray(i)?n.push(...i):s(i)&&a?n.push(...i.split(a)):n.push(i)));let l=g(this.data,n);return(!l&&!t&&!i&&e.indexOf(".")>-1&&(e=n[0],t=n[1],i=n.slice(2).join(".")),!l&&o&&s(i))?L(this.data&&this.data[e]&&this.data[e][t],i,a):l}addResource(e,t,i,s){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},r=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=[e,t];i&&(a=a.concat(r?i.split(r):i)),e.indexOf(".")>-1&&(a=e.split("."),s=t,t=a[1]),this.addNamespaces(t),u(this.data,a,s),n.silent||this.emit("added",e,t,i,s)}addResources(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let n in i)(s(i[n])||Array.isArray(i[n]))&&this.addResource(e,t,n,i[n],{silent:!0});n.silent||this.emit("added",e,t,i)}addResourceBundle(e,t,i,s,n){let r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),s=i,i=t,t=a[1]),this.addNamespaces(t);let o=g(this.data,a)||{};r.skipCopy||(i=JSON.parse(JSON.stringify(i))),s?f(o,i,n):o={...o,...i},u(this.data,a,o),r.silent||this.emit("added",e,t,i)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}}var j={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,i,s,n){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,i,s,n))}),t}};let P={};class A extends N{changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let i=this.resolve(e,t);return i&&void 0!==i.res}extractFromKey(e,t){let i=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===i&&(i=":");let n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,r=t.ns||this.options.defaultNS||[],a=i&&e.indexOf(i)>-1,o=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!w(e,i,n);if(a&&!o){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:s(r)?[r]:r};let a=e.split(i);(i!==n||i===n&&this.options.ns.indexOf(a[0])>-1)&&(r=a.shift()),e=a.join(n)}return{key:e,namespaces:s(r)?[r]:r}}translate(e,t,i){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let n=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:a,namespaces:o}=this.extractFromKey(e[e.length-1],t),l=o[o.length-1],c=t.lng||this.language,h=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(c&&"cimode"===c.toLowerCase()){if(h){let e=t.nsSeparator||this.options.nsSeparator;return n?{res:"".concat(l).concat(e).concat(a),usedKey:a,exactUsedKey:a,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:"".concat(l).concat(e).concat(a)}return n?{res:a,usedKey:a,exactUsedKey:a,usedLng:c,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:a}let u=this.resolve(e,t),d=u&&u.res,g=u&&u.usedKey||a,p=u&&u.exactUsedKey||a,f=Object.prototype.toString.apply(d),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,v=!this.i18nFormat||this.i18nFormat.handleAsObject,b=!s(d)&&"boolean"!=typeof d&&"number"!=typeof d;if(v&&d&&b&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(s(m)&&Array.isArray(d))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,d,{...t,ns:o}):"key '".concat(a," (").concat(this.language,")' returned an object instead of string.");return n?(u.res=e,u.usedParams=this.getUsedParamsDetails(t),u):e}if(r){let e=Array.isArray(d),i=e?[]:{},s=e?p:g;for(let e in d)if(Object.prototype.hasOwnProperty.call(d,e)){let n="".concat(s).concat(r).concat(e);i[e]=this.translate(n,{...t,joinArrays:!1,ns:o}),i[e]===n&&(i[e]=d[e])}d=i}}else if(v&&s(m)&&Array.isArray(d))(d=d.join(m))&&(d=this.extendTranslation(d,e,t,i));else{let n=!1,o=!1,h=void 0!==t.count&&!s(t.count),g=A.hasDefaultValue(t),p=h?this.pluralResolver.getSuffix(c,t.count,t):"",f=t.ordinal&&h?this.pluralResolver.getSuffix(c,t.count,{ordinal:!1}):"",m=h&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),v=m&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]||t["defaultValue".concat(p)]||t["defaultValue".concat(f)]||t.defaultValue;!this.isValidLookup(d)&&g&&(n=!0,d=v),this.isValidLookup(d)||(o=!0,d=a);let b=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&o?void 0:d,y=g&&v!==d&&this.options.updateMissing;if(o||n||y){if(this.logger.log(y?"updateKey":"missingKey",c,l,a,y?v:d),r){let e=this.resolve(a,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],i=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&i&&i[0])for(let t=0;t<i.length;t++)e.push(i[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let s=(e,i,s)=>{let n=g&&s!==d?s:b;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,i,n,y,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,i,n,y,t),this.emit("missingKey",e,l,i,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&h?e.forEach(e=>{let i=this.pluralResolver.getSuffixes(e,t);m&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]&&0>i.indexOf("".concat(this.options.pluralSeparator,"zero"))&&i.push("".concat(this.options.pluralSeparator,"zero")),i.forEach(i=>{s([e],a+i,t["defaultValue".concat(i)]||v)})}):s(e,a,v))}d=this.extendTranslation(d,e,t,u,i),o&&d===a&&this.options.appendNamespaceToMissingKey&&(d="".concat(l,":").concat(a)),(o||n)&&this.options.parseMissingKeyHandler&&(d="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?"".concat(l,":").concat(a):a,n?d:void 0):this.options.parseMissingKeyHandler(d))}return n?(u.res=d,u.usedParams=this.getUsedParamsDetails(t),u):d}extendTranslation(e,t,i,n,r){var a=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!i.skipInterpolation){let o;i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});let l=s(e)&&(i&&i.interpolation&&void 0!==i.interpolation.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);o=t&&t.length}let c=i.replace&&!s(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(c={...this.options.interpolation.defaultVariables,...c}),e=this.interpolator.interpolate(e,c,i.lng||this.language||n.usedLng,i),l){let t=e.match(this.interpolator.nestingRegexp);o<(t&&t.length)&&(i.nest=!1)}!i.lng&&"v1"!==this.options.compatibilityAPI&&n&&n.res&&(i.lng=this.language||n.usedLng),!1!==i.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,s=Array(e),n=0;n<e;n++)s[n]=arguments[n];return r&&r[0]===s[0]&&!i.context?(a.logger.warn("It seems you are nesting recursively key: ".concat(s[0]," in key: ").concat(t[0])),null):a.translate(...s,t)},i)),i.interpolation&&this.interpolator.reset()}let o=i.postProcess||this.options.postProcess,l=s(o)?[o]:o;return null!=e&&l&&l.length&&!1!==i.applyPostProcessor&&(e=j.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),e}resolve(e){let t,i,n,r,a,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,o),c=l.key;i=c;let h=l.namespaces;this.options.fallbackNS&&(h=h.concat(this.options.fallbackNS));let u=void 0!==o.count&&!s(o.count),d=u&&!o.ordinal&&0===o.count&&this.pluralResolver.shouldUseIntlApi(),g=void 0!==o.context&&(s(o.context)||"number"==typeof o.context)&&""!==o.context,p=o.lngs?o.lngs:this.languageUtils.toResolveHierarchy(o.lng||this.language,o.fallbackLng);h.forEach(e=>{this.isValidLookup(t)||(a=e,!P["".concat(p[0],"-").concat(e)]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(a)&&(P["".concat(p[0],"-").concat(e)]=!0,this.logger.warn('key "'.concat(i,'" for languages "').concat(p.join(", "),'" won\'t get resolved as namespace "').concat(a,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(i=>{let s;if(this.isValidLookup(t))return;r=i;let a=[c];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(a,c,i,e,o);else{let e;u&&(e=this.pluralResolver.getSuffix(i,o.count,o));let t="".concat(this.options.pluralSeparator,"zero"),s="".concat(this.options.pluralSeparator,"ordinal").concat(this.options.pluralSeparator);if(u&&(a.push(c+e),o.ordinal&&0===e.indexOf(s)&&a.push(c+e.replace(s,this.options.pluralSeparator)),d&&a.push(c+t)),g){let i="".concat(c).concat(this.options.contextSeparator).concat(o.context);a.push(i),u&&(a.push(i+e),o.ordinal&&0===e.indexOf(s)&&a.push(i+e.replace(s,this.options.pluralSeparator)),d&&a.push(i+t))}}for(;s=a.pop();)this.isValidLookup(t)||(n=s,t=this.getResource(i,e,s,o))}))})}),{res:t,usedKey:i,exactUsedKey:n,usedLng:r,usedNS:a}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,i,s):this.resourceStore.getResource(e,t,i,s)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!s(e.replace),i=t?e.replace:e;if(t&&void 0!==e.count&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!t)for(let e of(i={...i},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete i[e];return i}static hasDefaultValue(e){let t="defaultValue";for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t===i.substring(0,t.length)&&void 0!==e[i])return!0;return!1}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),a(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=R.create("translator")}}let I=e=>e.charAt(0).toUpperCase()+e.slice(1);class _{getScriptPartFromCode(e){if(!(e=S(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=S(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(s(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],i=e.split("-");return this.options.lowerCaseLng?i=i.map(e=>e.toLowerCase()):2===i.length?(i[0]=i[0].toLowerCase(),i[1]=i[1].toUpperCase(),t.indexOf(i[1].toLowerCase())>-1&&(i[1]=I(i[1].toLowerCase()))):3===i.length&&(i[0]=i[0].toLowerCase(),2===i[1].length&&(i[1]=i[1].toUpperCase()),"sgn"!==i[0]&&2===i[2].length&&(i[2]=i[2].toUpperCase()),t.indexOf(i[1].toLowerCase())>-1&&(i[1]=I(i[1].toLowerCase())),t.indexOf(i[2].toLowerCase())>-1&&(i[2]=I(i[2].toLowerCase()))),i.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let i=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(i))&&(t=i)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(e=>{if(e===i||!(0>e.indexOf("-")&&0>i.indexOf("-"))&&(e.indexOf("-")>0&&0>i.indexOf("-")&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),s(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let i=e[t];return i||(i=e[this.getScriptPartFromCode(t)]),i||(i=e[this.formatLanguageCode(t)]),i||(i=e[this.getLanguagePartFromCode(t)]),i||(i=e.default),i||[]}toResolveHierarchy(e,t){let i=this.getFallbackCodes(t||this.options.fallbackLng||[],e),n=[],r=e=>{e&&(this.isSupportedCode(e)?n.push(e):this.logger.warn("rejecting language code not found in supportedLngs: ".concat(e)))};return s(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&r(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&r(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&r(this.getLanguagePartFromCode(e))):s(e)&&r(this.formatLanguageCode(e)),i.forEach(e=>{0>n.indexOf(e)&&r(this.formatLanguageCode(e))}),n}constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}}let F=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],M={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},V=["v1","v2","v3"],D=["v4"],U={zero:0,one:1,two:2,few:3,many:4,other:5},T=()=>{let e={};return F.forEach(t=>{t.lngs.forEach(i=>{e[i]={numbers:t.nr,plurals:M[t.fc]}})}),e};class Z{addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let i;let s=S("dev"===e?"en":e),n=t.ordinal?"ordinal":"cardinal",r=JSON.stringify({cleanedCode:s,type:n});if(r in this.pluralRulesCache)return this.pluralRulesCache[r];try{i=new Intl.PluralRules(s,{type:n})}catch(n){if(!e.match(/-|_/))return;let s=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(s,t)}return this.pluralRulesCache[r]=i,i}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=this.getRule(e,t);return this.shouldUseIntlApi()?i&&i.resolvedOptions().pluralCategories.length>1:i&&i.numbers.length>1}getPluralFormsOfKey(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,i).map(e=>"".concat(t).concat(e))}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=this.getRule(e,t);return i?this.shouldUseIntlApi()?i.resolvedOptions().pluralCategories.sort((e,t)=>U[e]-U[t]).map(e=>"".concat(this.options.prepend).concat(t.ordinal?"ordinal".concat(this.options.prepend):"").concat(e)):i.numbers.map(i=>this.getSuffix(e,i,t)):[]}getSuffix(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=this.getRule(e,i);return s?this.shouldUseIntlApi()?"".concat(this.options.prepend).concat(i.ordinal?"ordinal".concat(this.options.prepend):"").concat(s.select(t)):this.getSuffixRetroCompatible(s,t):(this.logger.warn("no plural rule found for: ".concat(e)),"")}getSuffixRetroCompatible(e,t){let i=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),s=e.numbers[i];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===s?s="plural":1===s&&(s=""));let n=()=>this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString();return"v1"===this.options.compatibilityJSON?1===s?"":"number"==typeof s?"_plural_".concat(s.toString()):n():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?n():this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString()}shouldUseIntlApi(){return!V.includes(this.options.compatibilityJSON)}constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),(!this.options.compatibilityJSON||D.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=T(),this.pluralRulesCache={}}}let H=function(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",r=!(arguments.length>4)||void 0===arguments[4]||arguments[4],a=p(e,t,i);return!a&&r&&s(i)&&void 0===(a=L(e,i,n))&&(a=L(t,i,n)),a},B=e=>e.replace(/\$/g,"$$$$");class K{init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:i,useRawValueToEscape:s,prefix:n,prefixEscaped:r,suffix:a,suffixEscaped:o,formatSeparator:l,unescapeSuffix:c,unescapePrefix:h,nestingPrefix:u,nestingPrefixEscaped:d,nestingSuffix:g,nestingSuffixEscaped:p,nestingOptionsSeparator:f,maxReplaces:v,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:b,this.escapeValue=void 0===i||i,this.useRawValueToEscape=void 0!==s&&s,this.prefix=n?m(n):r||"{{",this.suffix=a?m(a):o||"}}",this.formatSeparator=l||",",this.unescapePrefix=c?"":h||"-",this.unescapeSuffix=this.unescapePrefix?"":c||"",this.nestingPrefix=u?m(u):d||m("$t("),this.nestingSuffix=g?m(g):p||m(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=v||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,"".concat(this.prefix,"(.+?)").concat(this.suffix)),this.regexpUnescape=e(this.regexpUnescape,"".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix)),this.nestingRegexp=e(this.nestingRegexp,"".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix))}interpolate(e,t,i,n){let a,o,l;let c=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},h=e=>{if(0>e.indexOf(this.formatSeparator)){let s=H(t,c,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(s,void 0,i,{...n,...t,interpolationkey:e}):s}let s=e.split(this.formatSeparator),r=s.shift().trim(),a=s.join(this.formatSeparator).trim();return this.format(H(t,c,r,this.options.keySeparator,this.options.ignoreJSONStructure),a,i,{...n,...t,interpolationkey:r})};this.resetRegExp();let u=n&&n.missingInterpolationHandler||this.options.missingInterpolationHandler,d=n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>B(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?B(this.escape(e)):B(e)}].forEach(t=>{for(l=0;a=t.regex.exec(e);){let i=a[1].trim();if(void 0===(o=h(i))){if("function"==typeof u){let t=u(e,a,n);o=s(t)?t:""}else if(n&&Object.prototype.hasOwnProperty.call(n,i))o="";else if(d){o=a[0];continue}else this.logger.warn("missed to pass in variable ".concat(i," for interpolating ").concat(e)),o=""}else s(o)||this.useRawValueToEscape||(o=r(o));let c=t.safeValue(o);if(e=e.replace(a[0],c),d?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=a[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let i,n,a,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let i=this.nestingOptionsSeparator;if(0>e.indexOf(i))return e;let s=e.split(new RegExp("".concat(i,"[ ]*{"))),n="{".concat(s[1]);e=s[0];let r=(n=this.interpolate(n,a)).match(/'/g),o=n.match(/"/g);(r&&r.length%2==0&&!o||o.length%2!=0)&&(n=n.replace(/'/g,'"'));try{a=JSON.parse(n),t&&(a={...t,...a})}catch(t){return this.logger.warn("failed parsing options string in nesting for key ".concat(e),t),"".concat(e).concat(i).concat(n)}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,e};for(;i=this.nestingRegexp.exec(e);){let c=[];(a=(a={...o}).replace&&!s(a.replace)?a.replace:a).applyPostProcessor=!1,delete a.defaultValue;let h=!1;if(-1!==i[0].indexOf(this.formatSeparator)&&!/{.*}/.test(i[1])){let e=i[1].split(this.formatSeparator).map(e=>e.trim());i[1]=e.shift(),c=e,h=!0}if((n=t(l.call(this,i[1].trim(),a),a))&&i[0]===e&&!s(n))return n;s(n)||(n=r(n)),n||(this.logger.warn("missed to resolve ".concat(i[1]," for nesting ").concat(e)),n=""),h&&(n=c.reduce((e,t)=>this.format(e,t,o.lng,{...o,interpolationkey:i[1].trim()}),n.trim())),e=e.replace(i[0],n),this.regexp.lastIndex=0}return e}constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}}let z=e=>{let t=e.toLowerCase().trim(),i={};if(e.indexOf("(")>-1){let s=e.split("(");t=s[0].toLowerCase().trim();let n=s[1].substring(0,s[1].length-1);"currency"===t&&0>n.indexOf(":")?i.currency||(i.currency=n.trim()):"relativetime"===t&&0>n.indexOf(":")?i.range||(i.range=n.trim()):n.split(";").forEach(e=>{if(e){let[t,...s]=e.split(":"),n=s.join(":").trim().replace(/^'+|'+$/g,""),r=t.trim();i[r]||(i[r]=n),"false"===n&&(i[r]=!1),"true"===n&&(i[r]=!0),isNaN(n)||(i[r]=parseInt(n,10))}})}return{formatName:t,formatOptions:i}},J=e=>{let t={};return(i,s,n)=>{let r=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(r={...r,[n.interpolationkey]:void 0});let a=s+JSON.stringify(r),o=t[a];return o||(o=e(S(s),n),t[a]=o),o(i)}};class W{init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=J(t)}format(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=t.split(this.formatSeparator);if(n.length>1&&n[0].indexOf("(")>1&&0>n[0].indexOf(")")&&n.find(e=>e.indexOf(")")>-1)){let e=n.findIndex(e=>e.indexOf(")")>-1);n[0]=[n[0],...n.splice(1,e)].join(this.formatSeparator)}return n.reduce((e,t)=>{let{formatName:n,formatOptions:r}=z(t);if(this.formats[n]){let t=e;try{let a=s&&s.formatParams&&s.formatParams[s.interpolationkey]||{},o=a.locale||a.lng||s.locale||s.lng||i;t=this.formats[n](e,o,{...r,...s,...a})}catch(e){this.logger.warn(e)}return t}return this.logger.warn("there was no format function for ".concat(n)),e},e)}constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=R.create("formatter"),this.options=e,this.formats={number:J((e,t)=>{let i=new Intl.NumberFormat(e,{...t});return e=>i.format(e)}),currency:J((e,t)=>{let i=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>i.format(e)}),datetime:J((e,t)=>{let i=new Intl.DateTimeFormat(e,{...t});return e=>i.format(e)}),relativetime:J((e,t)=>{let i=new Intl.RelativeTimeFormat(e,{...t});return e=>i.format(e,t.range||"day")}),list:J((e,t)=>{let i=new Intl.ListFormat(e,{...t});return e=>i.format(e)})},this.init(e)}}let q=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class $ extends N{queueLoad(e,t,i,s){let n={},r={},a={},o={};return e.forEach(e=>{let s=!0;t.forEach(t=>{let a="".concat(e,"|").concat(t);!i.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===r[a]&&(r[a]=!0):(this.state[a]=1,s=!1,void 0===r[a]&&(r[a]=!0),void 0===n[a]&&(n[a]=!0),void 0===o[t]&&(o[t]=!0)))}),s||(a[e]=!0)}),(Object.keys(n).length||Object.keys(r).length)&&this.queue.push({pending:r,pendingCount:Object.keys(r).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(n),pending:Object.keys(r),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(o)}}loaded(e,t,i){let s=e.split("|"),n=s[0],r=s[1];t&&this.emit("failedLoading",n,r,t),!t&&i&&this.store.addResourceBundle(n,r,i,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&i&&(this.state[e]=0);let a={};this.queue.forEach(i=>{d(i.loaded,[n],r),q(i,e),t&&i.errors.push(t),0!==i.pendingCount||i.done||(Object.keys(i.loaded).forEach(e=>{a[e]||(a[e]={});let t=i.loaded[e];t.length&&t.forEach(t=>{void 0===a[e][t]&&(a[e][t]=!0)})}),i.done=!0,i.errors.length?i.callback(i.errors):i.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(e=>!e.done)}read(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,r=arguments.length>5?arguments[5]:void 0;if(!e.length)return r(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:i,tried:s,wait:n,callback:r});return}this.readingCalls++;let a=(a,o)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(a&&o&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,i,s+1,2*n,r)},n);return}r(a,o)},o=this.backend[i].bind(this.backend);if(2===o.length){try{let i=o(e,t);i&&"function"==typeof i.then?i.then(e=>a(null,e)).catch(a):a(null,i)}catch(e){a(e)}return}return o(e,t,a)}prepareLoading(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();s(e)&&(e=this.languageUtils.toResolveHierarchy(e)),s(t)&&(t=[t]);let r=this.queueLoad(e,t,i,n);if(!r.toLoad.length)return r.pending.length||n(),null;r.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,i){this.prepareLoading(e,t,{},i)}reload(e,t,i){this.prepareLoading(e,t,{reload:!0},i)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=e.split("|"),s=i[0],n=i[1];this.read(s,n,"read",void 0,void 0,(i,r)=>{i&&this.logger.warn("".concat(t,"loading namespace ").concat(n," for language ").concat(s," failed"),i),!i&&r&&this.logger.log("".concat(t,"loaded namespace ").concat(n," for language ").concat(s),r),this.loaded(e,i,r)})}saveMissing(e,t,i,s,n){let r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn('did not save key "'.concat(i,'" as the namespace "').concat(t,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=i&&""!==i){if(this.backend&&this.backend.create){let o={...r,isUpdate:n},l=this.backend.create.bind(this.backend);if(l.length<6)try{let n;(n=5===l.length?l(e,t,i,s,o):l(e,t,i,s))&&"function"==typeof n.then?n.then(e=>a(null,e)).catch(a):a(null,n)}catch(e){a(e)}else l(e,t,i,s,a,o)}e&&e[0]&&this.store.addResource(e[0],t,i,s)}}constructor(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=i,this.languageUtils=i.languageUtils,this.options=s,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(i,s.backend,s)}}let Y=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),s(e[1])&&(t.defaultValue=e[1]),s(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let i=e[3]||e[2];Object.keys(i).forEach(e=>{t[e]=i[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Q=e=>(s(e.ns)&&(e.ns=[e.ns]),s(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),s(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),G=()=>{},X=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class ee extends N{init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(i=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(s(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let r=Y();this.options={...r,...this.options,...Q(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...r.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let a=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?R.init(a(this.modules.logger),this.options):R.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=W);let i=new _(this.options);this.store=new E(this.options.resources,this.options);let s=this.services;s.logger=R,s.resourceStore=this.store,s.languageUtils=i,s.pluralResolver=new Z(i,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===r.interpolation.format)&&(s.formatter=a(t),s.formatter.init(s,this.options),this.options.interpolation.format=s.formatter.format.bind(s.formatter)),s.interpolator=new K(this.options),s.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},s.backendConnector=new $(a(this.modules.backend),s.resourceStore,s,this.options),s.backendConnector.on("*",function(t){for(var i=arguments.length,s=Array(i>1?i-1:0),n=1;n<i;n++)s[n-1]=arguments[n];e.emit(t,...s)}),this.modules.languageDetector&&(s.languageDetector=a(this.modules.languageDetector),s.languageDetector.init&&s.languageDetector.init(s,this.options.detection,this.options)),this.modules.i18nFormat&&(s.i18nFormat=a(this.modules.i18nFormat),s.i18nFormat.init&&s.i18nFormat.init(this)),this.translator=new A(this.services,this.options),this.translator.on("*",function(t){for(var i=arguments.length,s=Array(i>1?i-1:0),n=1;n<i;n++)s[n-1]=arguments[n];e.emit(t,...s)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,i||(i=G),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let o=n(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(t),i(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),o}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G,i=t,n=s(e)?e:this.language;if("function"==typeof e&&(i=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return i();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};n?t(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),i(e)})}else i(null)}reloadResources(e,t,i){let s=n();return"function"==typeof e&&(i=e,e=void 0),"function"==typeof t&&(i=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),i||(i=G),this.services.backendConnector.reload(e,t,e=>{s.resolve(),i(e)}),s}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&j.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var i=this;this.isLanguageChangingTo=e;let r=n();this.emit("languageChanging",e);let a=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(e,s)=>{s?(a(s),this.translator.changeLanguage(s),this.isLanguageChangingTo=void 0,this.emit("languageChanged",s),this.logger.log("languageChanged",s)):this.isLanguageChangingTo=void 0,r.resolve(function(){return i.t(...arguments)}),t&&t(e,function(){return i.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let i=s(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);i&&(this.language||a(i),this.translator.language||this.translator.changeLanguage(i),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(i)),this.loadResources(i,e=>{o(e,i)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),r}getFixedT(e,t,i){var n=this;let r=function(e,t){let s,a;if("object"!=typeof t){for(var o=arguments.length,l=Array(o>2?o-2:0),c=2;c<o;c++)l[c-2]=arguments[c];s=n.options.overloadTranslationOptionHandler([e,t].concat(l))}else s={...t};s.lng=s.lng||r.lng,s.lngs=s.lngs||r.lngs,s.ns=s.ns||r.ns,""!==s.keyPrefix&&(s.keyPrefix=s.keyPrefix||i||r.keyPrefix);let h=n.options.keySeparator||".";return a=s.keyPrefix&&Array.isArray(e)?e.map(e=>"".concat(s.keyPrefix).concat(h).concat(e)):s.keyPrefix?"".concat(s.keyPrefix).concat(h).concat(e):e,n.t(a,s)};return s(e)?r.lng=e:r.lngs=e,r.ns=t,r.keyPrefix=i,r}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let i=t.lng||this.resolvedLanguage||this.languages[0],s=!!this.options&&this.options.fallbackLng,n=this.languages[this.languages.length-1];if("cimode"===i.toLowerCase())return!0;let r=(e,t)=>{let i=this.services.backendConnector.state["".concat(e,"|").concat(t)];return -1===i||0===i||2===i};if(t.precheck){let e=t.precheck(this,r);if(void 0!==e)return e}return!!(this.hasResourceBundle(i,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||r(i,e)&&(!s||r(n,e)))}loadNamespaces(e,t){let i=n();return this.options.ns?(s(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{i.resolve(),t&&t(e)}),i):(t&&t(),Promise.resolve())}loadLanguages(e,t){let i=n();s(e)&&(e=[e]);let r=this.options.preload||[],a=e.filter(e=>0>r.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return a.length?(this.options.preload=r.concat(a),this.loadResources(e=>{i.resolve(),t&&t(e)}),i):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services&&this.services.languageUtils||new _(Y())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ee(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G,i=e.forkResourceStore;i&&delete e.forkResourceStore;let s={...this.options,...e,isClone:!0},n=new ee(s);return(void 0!==e.debug||void 0!==e.prefix)&&(n.logger=n.logger.clone(e)),["store","services","language"].forEach(e=>{n[e]=this[e]}),n.services={...this.services},n.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},i&&(n.store=new E(this.store.data,s),n.services.resourceStore=n.store),n.translator=new A(n.services,s),n.translator.on("*",function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];n.emit(e,...i)}),n.init(s,t),n.translator.options=s,n.translator.backendConnector.services.utils={hasLoadedNamespace:n.hasLoadedNamespace.bind(n)},n}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Q(e),this.services={},this.logger=R,this.modules={external:[]},X(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}}let et=ee.createInstance();et.createInstance=ee.createInstance,et.createInstance,et.dir,et.init,et.loadResources,et.reloadResources,et.use,et.changeLanguage,et.getFixedT,et.t,et.exists,et.setDefaultNamespace,et.hasLoadedNamespace,et.loadNamespaces,et.loadLanguages}}]);