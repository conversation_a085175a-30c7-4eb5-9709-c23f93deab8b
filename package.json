{"name": "kashi-vedic-astrology", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "deploy": "npm run export && touch out/.nojekyll"}, "dependencies": {"@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@lottiefiles/react-lottie-player": "^3.6.0", "autoprefixer": "^10.4.17", "axios": "^1.6.7", "date-fns": "^3.3.1", "framer-motion": "^11.0.3", "howler": "^2.2.4", "i18next": "^23.8.2", "lottie-web": "^5.13.0", "next": "14.1.0", "next-auth": "^4.24.5", "next-i18next": "^15.2.0", "next-themes": "^0.2.1", "postcss": "^8.4.35", "react": "^18.2.0", "react-audio-player": "^0.17.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "react-i18next": "^14.0.5", "react-icons": "^5.0.1", "react-intersection-observer": "^9.5.3", "swiper": "^11.0.5", "tailwindcss": "^3.4.1", "zod": "^3.22.4"}, "devDependencies": {"@types/howler": "^2.2.12", "@types/node": "^20.11.16", "@types/react": "^18.2.52", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "typescript": "^5.3.3"}}