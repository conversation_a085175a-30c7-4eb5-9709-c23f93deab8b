/*
 React
 react-dom-server-legacy.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react"),ba=require("react-dom"),ea=require("stream");
function ja(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&4294967295;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=3432918353*(f&65535)+((3432918353*(f>>>16)&65535)<<16)&4294967295,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&4294967295}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&4294967295;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&4294967295;return(e^e>>>16)>>>0}
var u=Object.assign,x=Object.prototype.hasOwnProperty,ka=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),la={},ya={};
function za(a){if(x.call(ya,a))return!0;if(x.call(la,a))return!1;if(ka.test(a))return ya[a]=!0;la[a]=!0;return!1}
var Aa=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ba=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ca=/["'&<>]/;
function y(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ca.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Da=/([A-Z])/g,Ja=/^ms-/,Ka=Array.isArray,La=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ma={pending:!1,data:null,method:null,action:null},Na=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,kb={prefetchDNS:Oa,preconnect:Pa,preload:Qa,preloadModule:gb,preinitStyle:hb,preinitScript:ib,preinitModuleScript:jb},B=[],lb=/(<\/|<)(s)(cript)/gi;function mb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function nb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function G(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function ob(a){return G("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function pb(a,b,c){switch(b){case "noscript":return G(2,null,a.tagScope|1);case "select":return G(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return G(3,null,a.tagScope);case "picture":return G(2,null,a.tagScope|2);case "math":return G(4,null,a.tagScope);case "foreignObject":return G(2,null,a.tagScope);case "table":return G(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return G(6,null,a.tagScope);case "colgroup":return G(8,null,a.tagScope);case "tr":return G(7,null,a.tagScope)}return 5<=
a.insertionMode?G(2,null,a.tagScope):0===a.insertionMode?"html"===b?G(1,null,a.tagScope):G(2,null,a.tagScope):1===a.insertionMode?G(2,null,a.tagScope):a}var qb=new Map;
function rb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(x.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=y(d);e=y((""+e).trim())}else f=qb.get(d),void 0===f&&(f=y(d.replace(Da,"-$1").toLowerCase().replace(Ja,"-ms-")),qb.set(d,f)),e="number"===typeof e?0===e||Aa.has(d)?""+e:e+"px":
y((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function Bb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function J(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',y(c),'"')}function Cb(a){var b=a.nextFormID++;return a.idPrefix+b}var Db=y("javascript:throw new Error('A React form was unexpectedly submitted.')");
function Eb(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");J(this,"name",b);J(this,"value",a);this.push("/>")}
function Fb(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Cb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',Db,'"'),g=f=e=d=h=null,Gb(b,c)));null!=h&&K(a,"name",h);null!=d&&K(a,"formAction",d);null!=e&&K(a,"formEncType",e);null!=f&&K(a,"formMethod",f);null!=g&&K(a,"formTarget",g);return k}
function K(a,b,c){switch(b){case "className":J(a,"class",c);break;case "tabIndex":J(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":J(a,b,c);break;case "style":rb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',y(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Bb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',y(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',y(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',y(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',y(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',y(c),'"');break;case "xlinkActuate":J(a,"xlink:actuate",
c);break;case "xlinkArcrole":J(a,"xlink:arcrole",c);break;case "xlinkRole":J(a,"xlink:role",c);break;case "xlinkShow":J(a,"xlink:show",c);break;case "xlinkTitle":J(a,"xlink:title",c);break;case "xlinkType":J(a,"xlink:type",c);break;case "xmlBase":J(a,"xml:base",c);break;case "xmlLang":J(a,"xml:lang",c);break;case "xmlSpace":J(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ba.get(b)||b,za(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',y(c),'"')}}}function N(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function Hb(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function Gb(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});',"\x3c/script>"))}
function Ib(a,b,c,d,e,f,g){var h=b.rel,k=b.href,l=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof k||""===k)return O(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof l||null!=b.disabled||b.onLoad||b.onError)return O(a,b);f=d.styles.get(l);g=c.styleResources.hasOwnProperty(k)?c.styleResources[k]:void 0;null!==g?(c.styleResources[k]=null,f||(f={precedence:y(l),rules:[],hrefs:[],sheets:new Map},d.styles.set(l,f)),b={state:0,props:u({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&Jb(b.props,g),(c=d.preloads.stylesheets.get(k))&&0<c.length?c.length=0:b.state=1),f.sheets.set(k,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(k=f.sheets.get(k))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(k);e&&a.push("\x3c!-- --\x3e");return null}if(b.onLoad||b.onError)return O(a,b);e&&a.push("\x3c!-- --\x3e");switch(b.rel){case "preconnect":case "dns-prefetch":return O(d.preconnectChunks,b);case "preload":return O(d.preloadChunks,
b);default:return O(d.hoistableChunks,b)}}function O(a,b){a.push(P("link"));for(var c in b)if(x.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,c,d)}}a.push("/>");return null}
function Kb(a,b,c){a.push(P(c));for(var d in b)if(x.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,d,e)}}a.push("/>");return null}
function Lb(a,b){a.push(P("title"));var c=null,d=null,e;for(e in b)if(x.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(y(""+b));N(a,d,c);a.push(Mb("title"));return null}
function Nb(a,b){a.push(P("script"));var c=null,d=null,e;for(e in b)if(x.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");N(a,d,c);"string"===typeof c&&a.push(y(c));a.push(Mb("script"));return null}
function Ob(a,b,c){a.push(P(c));var d=c=null,e;for(e in b)if(x.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");N(a,d,c);return"string"===typeof c?(a.push(y(c)),null):c}var Pb=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Qb=new Map;function P(a){var b=Qb.get(a);if(void 0===b){if(!Pb.test(a))throw Error("Invalid tag: "+a);b="<"+a;Qb.set(a,b)}return b}
function Rb(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(P("select"));var h=null,k=null,l;for(l in c)if(x.call(c,l)){var n=c[l];if(null!=n)switch(l){case "children":h=n;break;case "dangerouslySetInnerHTML":k=n;break;case "defaultValue":case "value":break;default:K(a,l,n)}}a.push(">");N(a,k,h);return h;case "option":var m=f.selectedValue;a.push(P("option"));var p=null,q=null,C=null,z=null,A;for(A in c)if(x.call(c,
A)){var t=c[A];if(null!=t)switch(A){case "children":p=t;break;case "selected":C=t;break;case "dangerouslySetInnerHTML":z=t;break;case "value":q=t;default:K(a,A,t)}}if(null!=m){var r=null!==q?""+q:Hb(p);if(Ka(m))for(var D=0;D<m.length;D++){if(""+m[D]===r){a.push(' selected=""');break}}else""+m===r&&a.push(' selected=""')}else C&&a.push(' selected=""');a.push(">");N(a,z,p);return p;case "textarea":a.push(P("textarea"));var w=null,v=null,E=null,Q;for(Q in c)if(x.call(c,Q)){var F=c[Q];if(null!=F)switch(Q){case "children":E=
F;break;case "value":w=F;break;case "defaultValue":v=F;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:K(a,Q,F)}}null===w&&null!==v&&(w=v);a.push(">");if(null!=E){if(null!=w)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ka(E)){if(1<E.length)throw Error("<textarea> can only have at most one child.");w=""+E[0]}w=""+E}"string"===typeof w&&"\n"===w[0]&&a.push("\n");null!==w&&a.push(y(""+w));
return null;case "input":a.push(P("input"));var V=null,ma=null,fa=null,L=null,H=null,Y=null,Ra=null,Sa=null,Ta=null,na;for(na in c)if(x.call(c,na)){var S=c[na];if(null!=S)switch(na){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":V=S;break;case "formAction":ma=S;break;case "formEncType":fa=S;break;case "formMethod":L=S;break;case "formTarget":H=S;break;case "defaultChecked":Ta=
S;break;case "defaultValue":Ra=S;break;case "checked":Sa=S;break;case "value":Y=S;break;default:K(a,na,S)}}var sb=Fb(a,d,e,ma,fa,L,H,V);null!==Sa?Bb(a,"checked",Sa):null!==Ta&&Bb(a,"checked",Ta);null!==Y?K(a,"value",Y):null!==Ra&&K(a,"value",Ra);a.push("/>");null!==sb&&sb.forEach(Eb,a);return null;case "button":a.push(P("button"));var oa=null,pa=null,ca=null,qa=null,ra=null,Ua=null,sa=null,Va;for(Va in c)if(x.call(c,Va)){var da=c[Va];if(null!=da)switch(Va){case "children":oa=da;break;case "dangerouslySetInnerHTML":pa=
da;break;case "name":ca=da;break;case "formAction":qa=da;break;case "formEncType":ra=da;break;case "formMethod":Ua=da;break;case "formTarget":sa=da;break;default:K(a,Va,da)}}var Tc=Fb(a,d,e,qa,ra,Ua,sa,ca);a.push(">");null!==Tc&&Tc.forEach(Eb,a);N(a,pa,oa);if("string"===typeof oa){a.push(y(oa));var Uc=null}else Uc=oa;return Uc;case "form":a.push(P("form"));var Wa=null,Vc=null,ha=null,Xa=null,Ya=null,Za=null,$a;for($a in c)if(x.call(c,$a)){var ia=c[$a];if(null!=ia)switch($a){case "children":Wa=ia;
break;case "dangerouslySetInnerHTML":Vc=ia;break;case "action":ha=ia;break;case "encType":Xa=ia;break;case "method":Ya=ia;break;case "target":Za=ia;break;default:K(a,$a,ia)}}var Yb=null,Zb=null;if("function"===typeof ha)if("function"===typeof ha.$$FORM_ACTION){var Ge=Cb(d),Ea=ha.$$FORM_ACTION(Ge);ha=Ea.action||"";Xa=Ea.encType;Ya=Ea.method;Za=Ea.target;Yb=Ea.data;Zb=Ea.name}else a.push(" ","action",'="',Db,'"'),Za=Ya=Xa=ha=null,Gb(d,e);null!=ha&&K(a,"action",ha);null!=Xa&&K(a,"encType",Xa);null!=
Ya&&K(a,"method",Ya);null!=Za&&K(a,"target",Za);a.push(">");null!==Zb&&(a.push('<input type="hidden"'),J(a,"name",Zb),a.push("/>"),null!==Yb&&Yb.forEach(Eb,a));N(a,Vc,Wa);if("string"===typeof Wa){a.push(y(Wa));var Wc=null}else Wc=Wa;return Wc;case "menuitem":a.push(P("menuitem"));for(var tb in c)if(x.call(c,tb)){var Xc=c[tb];if(null!=Xc)switch(tb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:K(a,tb,Xc)}}a.push(">");
return null;case "title":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Yc=Lb(a,c);else Lb(e.hoistableChunks,c),Yc=null;return Yc;case "link":return Ib(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var $b=c.async;if("string"!==typeof c.src||!c.src||!$b||"function"===typeof $b||"symbol"===typeof $b||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Zc=Nb(a,c);else{var ub=c.src;if("module"===c.type){var vb=d.moduleScriptResources;var $c=e.preloads.moduleScripts}else vb=
d.scriptResources,$c=e.preloads.scripts;var wb=vb.hasOwnProperty(ub)?vb[ub]:void 0;if(null!==wb){vb[ub]=null;var ac=c;if(wb){2===wb.length&&(ac=u({},c),Jb(ac,wb));var ad=$c.get(ub);ad&&(ad.length=0)}var bd=[];e.scripts.add(bd);Nb(bd,ac)}g&&a.push("\x3c!-- --\x3e");Zc=null}return Zc;case "style":var xb=c.precedence,ta=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof xb||"string"!==typeof ta||""===ta){a.push(P("style"));var Fa=null,cd=null,ab;for(ab in c)if(x.call(c,
ab)){var yb=c[ab];if(null!=yb)switch(ab){case "children":Fa=yb;break;case "dangerouslySetInnerHTML":cd=yb;break;default:K(a,ab,yb)}}a.push(">");var bb=Array.isArray(Fa)?2>Fa.length?Fa[0]:null:Fa;"function"!==typeof bb&&"symbol"!==typeof bb&&null!==bb&&void 0!==bb&&a.push(y(""+bb));N(a,cd,Fa);a.push(Mb("style"));var dd=null}else{var ua=e.styles.get(xb);if(null!==(d.styleResources.hasOwnProperty(ta)?d.styleResources[ta]:void 0)){d.styleResources[ta]=null;ua?ua.hrefs.push(y(ta)):(ua={precedence:y(xb),
rules:[],hrefs:[y(ta)],sheets:new Map},e.styles.set(xb,ua));var ed=ua.rules,Ga=null,fd=null,zb;for(zb in c)if(x.call(c,zb)){var bc=c[zb];if(null!=bc)switch(zb){case "children":Ga=bc;break;case "dangerouslySetInnerHTML":fd=bc}}var cb=Array.isArray(Ga)?2>Ga.length?Ga[0]:null:Ga;"function"!==typeof cb&&"symbol"!==typeof cb&&null!==cb&&void 0!==cb&&ed.push(y(""+cb));N(ed,fd,Ga)}ua&&e.boundaryResources&&e.boundaryResources.styles.add(ua);g&&a.push("\x3c!-- --\x3e");dd=void 0}return dd;case "meta":if(3===
f.insertionMode||f.tagScope&1||null!=c.itemProp)var gd=Kb(a,c,"meta");else g&&a.push("\x3c!-- --\x3e"),gd="string"===typeof c.charSet?Kb(e.charsetChunks,c,"meta"):"viewport"===c.name?Kb(e.preconnectChunks,c,"meta"):Kb(e.hoistableChunks,c,"meta");return gd;case "listing":case "pre":a.push(P(b));var db=null,eb=null,fb;for(fb in c)if(x.call(c,fb)){var Ab=c[fb];if(null!=Ab)switch(fb){case "children":db=Ab;break;case "dangerouslySetInnerHTML":eb=Ab;break;default:K(a,fb,Ab)}}a.push(">");if(null!=eb){if(null!=
db)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof eb||!("__html"in eb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var va=eb.__html;null!==va&&void 0!==va&&("string"===typeof va&&0<va.length&&"\n"===va[0]?a.push("\n",va):a.push(""+va))}"string"===typeof db&&"\n"===db[0]&&a.push("\n");return db;case "img":var M=c.src,
I=c.srcSet;if(!("lazy"===c.loading||!M&&!I||"string"!==typeof M&&null!=M||"string"!==typeof I&&null!=I)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof M||":"!==M[4]||"d"!==M[0]&&"D"!==M[0]||"a"!==M[1]&&"A"!==M[1]||"t"!==M[2]&&"T"!==M[2]||"a"!==M[3]&&"A"!==M[3])&&("string"!==typeof I||":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var hd="string"===typeof c.sizes?c.sizes:void 0,Ha=I?I+"\n"+(hd||""):M,cc=e.preloads.images,
wa=cc.get(Ha);if(wa){if("high"===c.fetchPriority||10>e.highImagePreloads.size)cc.delete(Ha),e.highImagePreloads.add(wa)}else if(!d.imageResources.hasOwnProperty(Ha)){d.imageResources[Ha]=B;var dc=c.crossOrigin;var id="string"===typeof dc?"use-credentials"===dc?dc:"":void 0;var Z=e.headers,ec;Z&&0<Z.remainingCapacity&&("high"===c.fetchPriority||500>Z.highImagePreloads.length)&&(ec=Sb(M,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:id,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,
referrerPolicy:c.refererPolicy}),2<=(Z.remainingCapacity-=ec.length))?(e.resets.image[Ha]=B,Z.highImagePreloads&&(Z.highImagePreloads+=", "),Z.highImagePreloads+=ec):(wa=[],O(wa,{rel:"preload",as:"image",href:I?void 0:M,imageSrcSet:I,imageSizes:hd,crossOrigin:id,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(wa):(e.bulkPreloads.add(wa),cc.set(Ha,wa)))}}return Kb(a,c,"img");
case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Kb(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var jd=Ob(e.headChunks,c,"head")}else jd=Ob(a,c,"head");return jd;case "html":if(0===f.insertionMode&&null===
e.htmlChunks){e.htmlChunks=[""];var kd=Ob(e.htmlChunks,c,"html")}else kd=Ob(a,c,"html");return kd;default:if(-1!==b.indexOf("-")){a.push(P(b));var fc=null,ld=null,Ia;for(Ia in c)if(x.call(c,Ia)){var xa=c[Ia];if(null!=xa){var He=Ia;switch(Ia){case "children":fc=xa;break;case "dangerouslySetInnerHTML":ld=xa;break;case "style":rb(a,xa);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:za(Ia)&&"function"!==typeof xa&&"symbol"!==typeof xa&&a.push(" ",He,'="',y(xa),
'"')}}}a.push(">");N(a,ld,fc);return fc}}return Ob(a,c,b)}var Tb=new Map;function Mb(a){var b=Tb.get(a);void 0===b&&(b="</"+a+">",Tb.set(a,b));return b}function Ub(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}
function Vb(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Wb(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function Xb(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var gc=/[<\u2028\u2029]/g;
function hc(a){return JSON.stringify(a).replace(gc,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var ic=/[&><\u2028\u2029]/g;
function jc(a){return JSON.stringify(a).replace(ic,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var kc=!1,lc=!0;
function mc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);lc=this.push("</style>");kc=!0;b.length=0;c.length=0}}function nc(a){return 2!==a.state?kc=!0:!1}function oc(a,b,c){kc=!1;lc=!0;b.styles.forEach(mc,a);b.stylesheets.forEach(nc);kc&&(c.stylesToHoist=!0);return lc}
function R(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var pc=[];function qc(a){O(pc,a.props);for(var b=0;b<pc.length;b++)this.push(pc[b]);pc.length=0;a.state=2}
function rc(a){var b=0<a.sheets.size;a.sheets.forEach(qc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function sc(a){if(0===a.state){a.state=1;var b=a.props;O(pc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<pc.length;a++)this.push(pc[a]);pc.length=0}}function tc(a){a.sheets.forEach(sc,this);a.sheets.clear()}
function uc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=jc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=jc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=jc(e);a.push(e);for(var h in f)if(x.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=jc(k);e.push(k);e.push(",");g=
jc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function vc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=y(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=y(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=y(JSON.stringify(e));a.push(e);for(var h in f)if(x.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!za(h))break a;g=""+g}e.push(",");k=y(JSON.stringify(k));e.push(k);
e.push(",");g=y(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function Oa(a){var b=T?T:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(wc,xc)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],O(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}yc(b)}}}
function Pa(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(wc,xc)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(zc,Ac);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],O(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}yc(c)}}}
function Qa(a,b,c){var d=T?T:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var l=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(l))return;e.imageResources[l]=B;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Sb(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[l]=B,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],O(e,u({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(l,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];O(g,u({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
O(g,u({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?B:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=B;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(l=Sb(a,b,c),2<=(e.remainingCapacity-=l.length)))f.resets.font[a]=B,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=l;else switch(e=[],
a=u({rel:"preload",href:a,as:b},c),O(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}yc(d)}}}
function gb(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?B:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=B}O(f,u({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);yc(c)}}}
function hb(a,b,c){var d=T?T:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:y(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:u({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Jb(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),yc(d))}}}
function ib(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=u({src:a,async:!0},b),f&&(2===f.length&&Jb(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Nb(a,b),yc(c))}}}
function jb(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=u({src:a,type:"module",async:!0},b),f&&(2===f.length&&Jb(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),Nb(a,b),yc(c))}}}function Jb(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Sb(a,b,c){a=(""+a).replace(wc,xc);b=(""+b).replace(zc,Ac);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)x.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(zc,Ac)+'"'));return b}var wc=/[<>\r\n]/g;
function xc(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var zc=/["';,\r\n]/g;
function Ac(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Bc(a){this.styles.add(a)}function Cc(a){this.stylesheets.add(a)}
function Dc(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(lb,mb),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,l=new Set,n=new Set,m=new Map,p=new Set,q=new Set,C=new Set,z={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var A=0;A<f.length;A++){var t=f[A],r,D=void 0,w=void 0,v={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof t?v.href=r=t:(v.href=r=t.src,v.integrity=w="string"===typeof t.integrity?t.integrity:void 0,v.crossOrigin=D="string"===typeof t||null==t.crossOrigin?void 0:"use-credentials"===t.crossOrigin?"use-credentials":"");t=a;var E=r;t.scriptResources[E]=null;t.moduleScriptResources[E]=null;t=[];O(t,v);p.add(t);d.push('<script src="',y(r));"string"===typeof w&&d.push('" integrity="',y(w));"string"===typeof D&&d.push('" crossorigin="',y(D));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)v=
g[f],D=r=void 0,w={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof v?w.href=A=v:(w.href=A=v.src,w.integrity=D="string"===typeof v.integrity?v.integrity:void 0,w.crossOrigin=r="string"===typeof v||null==v.crossOrigin?void 0:"use-credentials"===v.crossOrigin?"use-credentials":""),v=a,t=A,v.scriptResources[t]=null,v.moduleScriptResources[t]=null,v=[],O(v,w),p.add(v),d.push('<script type="module" src="',y(A)),"string"===typeof D&&d.push('" integrity="',y(D)),"string"===typeof r&&
d.push('" crossorigin="',y(r)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:[],preloadChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:l,highImagePreloads:n,styles:m,
bootstrapScripts:p,scripts:q,bulkPreloads:C,preloads:z,boundaryResources:null,stylesToHoist:!1,generateStaticMarkup:b}}function Ec(a,b,c,d){if(c.generateStaticMarkup)return a.push(y(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(y(b)),a=!0);return a}
var Fc=Symbol.for("react.element"),Gc=Symbol.for("react.portal"),Hc=Symbol.for("react.fragment"),Ic=Symbol.for("react.strict_mode"),Jc=Symbol.for("react.profiler"),Kc=Symbol.for("react.provider"),Lc=Symbol.for("react.context"),Mc=Symbol.for("react.server_context"),Nc=Symbol.for("react.forward_ref"),Oc=Symbol.for("react.suspense"),Pc=Symbol.for("react.suspense_list"),Qc=Symbol.for("react.memo"),Rc=Symbol.for("react.lazy"),Sc=Symbol.for("react.scope"),md=Symbol.for("react.debug_trace_mode"),nd=Symbol.for("react.offscreen"),
od=Symbol.for("react.legacy_hidden"),pd=Symbol.for("react.cache"),qd=Symbol.for("react.default_value"),rd=Symbol.iterator,sd=Symbol.for("react.client.reference");
function td(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===sd?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case Hc:return"Fragment";case Gc:return"Portal";case Jc:return"Profiler";case Ic:return"StrictMode";case Oc:return"Suspense";case Pc:return"SuspenseList";case pd:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case Lc:return(a.displayName||"Context")+".Consumer";case Kc:return(a._context.displayName||"Context")+".Provider";case Nc:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Qc:return b=a.displayName||null,null!==b?b:td(a.type)||"Memo";case Rc:b=a._payload;a=a._init;try{return td(a(b))}catch(c){}}return null}var ud;function vd(a){if(void 0===ud)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);ud=b&&b[1]||""}return"\n"+ud+a}var wd=!1;
function xd(a,b){if(!a||wd)return"";wd=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var m=function(){throw Error();};Object.defineProperty(m.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(m,[])}catch(q){var p=q}Reflect.construct(a,[],m)}else{try{m.call()}catch(q){p=q}a.call(m.prototype)}}else{try{throw Error();}catch(q){p=q}(m=a())&&"function"===typeof m.catch&&
m.catch(function(){})}}catch(q){if(q&&p&&"string"===typeof q.stack)return[q.stack,p.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var k=g.split("\n"),l=h.split("\n");for(e=d=0;d<k.length&&!k[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<l.length&&!l[e].includes("DetermineComponentFrameRoot");)e++;if(d===k.length||e===l.length)for(d=k.length-1,e=l.length-1;1<=d&&0<=e&&k[d]!==l[e];)e--;for(;1<=d&&0<=e;d--,e--)if(k[d]!==l[e]){if(1!==d||1!==e){do if(d--,e--,0>e||k[d]!==l[e]){var n="\n"+k[d].replace(" at new "," at ");a.displayName&&n.includes("<anonymous>")&&(n=n.replace("<anonymous>",a.displayName));return n}while(1<=d&&0<=e)}break}}}finally{wd=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?vd(c):""}
var yd={};function zd(a,b){a=a.contextTypes;if(!a)return yd;var c={},d;for(d in a)c[d]=b[d];return c}var Ad=null;function Bd(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Bd(a,c)}b.context._currentValue2=b.value}}
function Cd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&Cd(a)}function Dd(a){var b=a.parent;null!==b&&Dd(b);a.context._currentValue2=a.value}function Ed(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Bd(a,b):Ed(a,b)}
function Fd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Bd(a,c):Fd(a,c);b.context._currentValue2=b.value}function Gd(a){var b=Ad;b!==a&&(null===b?Dd(a):null===a?Cd(b):b.depth===a.depth?Bd(b,a):b.depth>a.depth?Ed(b,a):Fd(b,a),Ad=a)}
var Hd={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Id(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Hd;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:u({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Hd.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=u({},f,h)):u(f,h))}a.state=f}else f.queue=null}
var Jd={id:1,overflow:""};function Kd(a,b,c){var d=a.id;a=a.overflow;var e=32-Ld(d)-1;d&=~(1<<e);c+=1;var f=32-Ld(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Ld(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Ld=Math.clz32?Math.clz32:Md,Nd=Math.log,Od=Math.LN2;function Md(a){a>>>=0;return 0===a?32:31-(Nd(a)/Od|0)|0}var Pd=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Qd(){}function Rd(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Qd,Qd),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Sd=b;throw Pd;}}var Sd=null;
function Td(){if(null===Sd)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Sd;Sd=null;return a}function Ud(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var Vd="function"===typeof Object.is?Object.is:Ud,Wd=null,Xd=null,Yd=null,Zd=null,$d=null,U=null,ae=!1,be=!1,ce=0,de=0,ee=-1,fe=0,ge=null,he=null,ie=0;
function je(){if(null===Wd)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return Wd}
function ke(){if(0<ie)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function le(){null===U?null===$d?(ae=!1,$d=U=ke()):(ae=!0,U=$d):null===U.next?(ae=!1,U=U.next=ke()):(ae=!0,U=U.next);return U}function me(a,b,c,d){for(;be;)be=!1,de=ce=0,ee=-1,fe=0,ie+=1,U=null,c=a(b,d);ne();return c}function oe(){var a=ge;ge=null;return a}function ne(){Zd=Yd=Xd=Wd=null;be=!1;$d=null;ie=0;U=he=null}
function pe(a,b){return"function"===typeof b?b(a):b}function qe(a,b,c){Wd=je();U=le();if(ae){var d=U.queue;b=d.dispatch;if(null!==he&&(c=he.get(d),void 0!==c)){he.delete(d);d=U.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);U.memoizedState=d;return[d,b]}return[U.memoizedState,b]}a=a===pe?"function"===typeof b?b():b:void 0!==c?c(b):b;U.memoizedState=a;a=U.queue={last:null,dispatch:null};a=a.dispatch=re.bind(null,Wd,a);return[U.memoizedState,a]}
function se(a,b){Wd=je();U=le();b=void 0===b?null:b;if(null!==U){var c=U.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!Vd(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();U.memoizedState=[a,b];return a}
function re(a,b,c){if(25<=ie)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===Wd)if(be=!0,a={action:c,next:null},null===he&&(he=new Map),c=he.get(b),void 0===c)he.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function te(){throw Error("startTransition cannot be called during server rendering.");}function ue(){throw Error("Cannot update optimistic state while rendering.");}
function ve(a){var b=fe;fe+=1;null===ge&&(ge=[]);return Rd(ge,a,b)}function we(){throw Error("Cache cannot be refreshed during server rendering.");}function xe(){}
var ze={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return ve(a);if(a.$$typeof===Lc||a.$$typeof===Mc)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){je();return a._currentValue2},useMemo:se,useReducer:qe,useRef:function(a){Wd=je();U=le();var b=U.memoizedState;return null===b?(a={current:a},U.memoizedState=a):b},useState:function(a){return qe(pe,a)},
useInsertionEffect:xe,useLayoutEffect:xe,useCallback:function(a,b){return se(function(){return a},b)},useImperativeHandle:xe,useEffect:xe,useDebugValue:xe,useDeferredValue:function(a){je();return a},useTransition:function(){je();return[!1,te]},useId:function(){var a=Xd.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Ld(a)-1)).toString(32)+b;var c=ye;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=ce++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return we},useHostTransitionStatus:function(){je();return Ma},useOptimistic:function(a){je();return[a,ue]},useFormState:function(a,b,c){je();var d=de++,e=Yd;if("function"===typeof a.$$FORM_ACTION){var f=null,g=Zd;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0),k===f&&(ee=d,b=e[0]))}var l=a.bind(null,b);a=function(m){l(m)};"function"===typeof l.$$FORM_ACTION&&(a.$$FORM_ACTION=function(m){m=l.$$FORM_ACTION(m);void 0!==c&&(c+="",m.action=c);var p=m.data;p&&(null===f&&(f=void 0!==c?"p"+c:"k"+ja(JSON.stringify([g,null,d]),0)),p.append("$ACTION_KEY",f));return m});return[b,a]}var n=a.bind(null,b);return[b,function(m){n(m)}]}},ye=null,Ae=
{getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},Be=La.ReactCurrentDispatcher,Ce=La.ReactCurrentCache;function De(a){console.error(a);return null}function Ee(){}
function Fe(a,b,c,d,e,f,g,h,k,l,n,m){Na.current=kb;var p=[],q=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:q,pingedTasks:p,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?De:f,onPostpone:void 0===n?Ee:n,onAllReady:void 0===g?
Ee:g,onShellReady:void 0===h?Ee:h,onShellError:void 0===k?Ee:k,onFatalError:void 0===l?Ee:l,formState:void 0===m?null:m};c=Ie(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Je(b,null,a,-1,null,c,q,null,d,yd,null,Jd,null);p.push(a);return b}var T=null;function Ke(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,Le(a))}
function Me(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Je(a,b,c,d,e,f,g,h,k,l,n,m,p){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var q={replay:null,node:c,childIndex:d,ping:function(){return Ke(a,q)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:m,componentStack:p,thenableState:b};g.add(q);return q}
function Ne(a,b,c,d,e,f,g,h,k,l,n,m,p){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var q={replay:c,node:d,childIndex:e,ping:function(){return Ke(a,q)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:k,legacyContext:l,context:n,treeContext:m,componentStack:p,thenableState:b};g.add(q);return q}
function Ie(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function Oe(a,b){return{tag:0,parent:a.componentStack,type:b}}
function Pe(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=vd(b.type,null,null);break;case 1:a+=xd(b.type,!1);break;case 2:a+=xd(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function W(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function Qe(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Re(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((td(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=u({},c,d)}b.legacyContext=e;X(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,X(a,b,null,f,-1),b.keyPath=e}
function Se(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var l=0;l<f;l++)l===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Kd(c,1,0),Te(a,b,d,-1),b.treeContext=c):h?Te(a,b,d,-1):X(a,b,null,d,-1);b.keyPath=f}function Ue(a,b){if(a&&a.defaultProps){b=u({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function Ve(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:e};g=zd(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue2:g);Id(h,e,f,g);Re(a,b,c,h,e);b.componentStack=d}else{g=zd(e,b.legacyContext);h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e};Wd={};Xd=b;Yd=a;Zd=c;de=ce=0;ee=-1;fe=0;ge=d;d=e(f,g);d=me(e,f,d,g);var k=0!==
ce,l=de,n=ee;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Id(d,e,f,g),Re(a,b,c,d,e)):Se(a,b,c,d,k,l,n);b.componentStack=h}else if("string"===typeof e){d=b.componentStack;b.componentStack=Oe(b,e);g=b.blockedSegment;if(null===g)g=f.children,h=b.formatContext,k=b.keyPath,b.formatContext=pb(h,e,f),b.keyPath=c,Te(a,b,g,-1),b.formatContext=h,b.keyPath=k;else{k=Rb(g.chunks,e,f,a.resumableState,a.renderState,b.formatContext,g.lastPushedText);g.lastPushedText=!1;h=b.formatContext;
l=b.keyPath;b.formatContext=pb(h,e,f);b.keyPath=c;Te(a,b,k,-1);b.formatContext=h;b.keyPath=l;a:{c=g.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(Mb(e))}g.lastPushedText=
!1}b.componentStack=d}else{switch(e){case od:case md:case Ic:case Jc:case Hc:e=b.keyPath;b.keyPath=c;X(a,b,null,f.children,-1);b.keyPath=e;return;case nd:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,X(a,b,null,f.children,-1),b.keyPath=e);return;case Pc:e=b.componentStack;b.componentStack=Oe(b,"SuspenseList");d=b.keyPath;b.keyPath=c;X(a,b,null,f.children,-1);b.keyPath=d;b.componentStack=e;return;case Sc:throw Error("ReactDOMServer does not yet support scope components.");case Oc:a:if(null!==b.replay){e=
b.keyPath;b.keyPath=c;c=f.children;try{Te(a,b,c,-1)}finally{b.keyPath=e}}else{var m=b.componentStack;e=b.componentStack=Oe(b,"Suspense");var p=b.keyPath;d=b.blockedBoundary;var q=b.blockedSegment;g=f.fallback;var C=f.children;f=new Set;l=Me(a,f);null!==a.trackedPostpones&&(l.trackedContentKeyPath=c);n=Ie(a,q.chunks.length,l,b.formatContext,!1,!1);q.children.push(n);q.lastPushedText=!1;var z=Ie(a,0,null,b.formatContext,!1,!1);z.parentFlushed=!0;b.blockedBoundary=l;b.blockedSegment=z;a.renderState.boundaryResources=
l.resources;b.keyPath=c;try{if(Te(a,b,C,-1),a.renderState.generateStaticMarkup||z.lastPushedText&&z.textEmbedded&&z.chunks.push("\x3c!-- --\x3e"),z.status=1,We(l,z),0===l.pendingTasks&&0===l.status){l.status=1;b.componentStack=m;break a}}catch(A){z.status=4,l.status=4,h=Pe(a,b.componentStack),k=W(a,A,h),l.errorDigest=k,Xe(a,l)}finally{a.renderState.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=q,b.keyPath=p,b.componentStack=m}h=[c[0],"Suspense Fallback",c[2]];k=a.trackedPostpones;
null!==k&&(m=[h[1],h[2],[],null],k.workingMap.set(h,m),5===l.status?k.workingMap.get(c)[4]=m:l.trackedFallbackNode=m);b=Je(a,null,g,-1,d,n,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext,e);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case Nc:h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e.render};e=e.render;Wd={};Xd=b;Yd=a;Zd=c;de=ce=0;ee=-1;fe=0;ge=d;d=e(f,g);f=me(e,f,d,g);Se(a,b,c,f,0!==ce,de,ee);b.componentStack=h;return;
case Qc:e=e.type;f=Ue(e,f);Ve(a,b,c,d,e,f,g);return;case Kc:g=f.children;d=b.keyPath;e=e._context;f=f.value;h=e._currentValue2;e._currentValue2=f;k=Ad;Ad=f={parent:k,depth:null===k?0:k.depth+1,context:e,parentValue:h,value:f};b.context=f;b.keyPath=c;X(a,b,null,g,-1);a=Ad;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue2=c===qd?a.context._defaultValue:c;a=Ad=a.parent;b.context=a;b.keyPath=d;return;case Lc:f=f.children;
f=f(e._currentValue2);e=b.keyPath;b.keyPath=c;X(a,b,null,f,-1);b.keyPath=e;return;case Rc:g=b.componentStack;b.componentStack=Oe(b,"Lazy");h=e._init;e=h(e._payload);f=Ue(e,f);Ve(a,b,c,d,e,f,void 0);b.componentStack=g;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==e?e:typeof e)+"."));}}
function Ye(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Ie(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Te(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(We(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function X(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)Ye(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case Fc:var f=d.type,g=d.key,h=d.props,k=d.ref,l=td(f),n=null==g?-1===e?0:e:g;g=[b.keyPath,l,n];if(null!==b.replay)a:{var m=b.replay;e=m.nodes;for(d=0;d<e.length;d++){var p=e[d];if(n===p[1]){if(4===p.length){if(null!==l&&l!==p[0])throw Error("Expected the resume to render <"+p[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");var q=p[2];l=p[3];p=b.node;b.replay={nodes:q,slots:l,pendingTasks:1};try{Ve(a,b,g,c,f,h,k);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(v){if("object"===typeof v&&null!==v&&(v===Pd||"function"===typeof v.then))throw b.node===p&&(b.replay=m),
v;b.replay.pendingTasks--;h=Pe(a,b.componentStack);g=a;a=b.blockedBoundary;c=v;h=W(g,c,h);Ze(g,a,q,l,c,h)}b.replay=m}else{if(f!==Oc)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(td(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{m=void 0;c=p[5];f=p[2];k=p[3];l=null===p[4]?[]:p[4][2];p=null===p[4]?null:p[4][3];n=b.componentStack;var C=b.componentStack=Oe(b,"Suspense"),z=b.keyPath,A=b.replay,t=b.blockedBoundary,
r=h.children;h=h.fallback;var D=new Set,w=Me(a,D);w.parentFlushed=!0;w.rootSegmentID=c;b.blockedBoundary=w;b.replay={nodes:f,slots:k,pendingTasks:1};a.renderState.boundaryResources=w.resources;try{Te(a,b,r,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===w.pendingTasks&&0===w.status){w.status=1;a.completedBoundaries.push(w);
break b}}catch(v){w.status=4,q=Pe(a,b.componentStack),m=W(a,v,q),w.errorDigest=m,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(w)}finally{a.renderState.boundaryResources=t?t.resources:null,b.blockedBoundary=t,b.replay=A,b.keyPath=z,b.componentStack=n}b=Ne(a,null,{nodes:l,slots:p,pendingTasks:0},h,-1,t,D,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,C);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else Ve(a,b,g,c,f,h,k);return;case Gc:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case Rc:h=b.componentStack;b.componentStack=Oe(b,"Lazy");g=d._init;d=g(d._payload);b.componentStack=h;X(a,b,null,d,e);return}if(Ka(d)){$e(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=rd&&d[rd]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);$e(a,b,g,e)}return}if("function"===typeof d.then)return X(a,b,null,ve(d),e);if(d.$$typeof===Lc||d.$$typeof===Mc)return X(a,b,null,d._currentValue2,e);e=Object.prototype.toString.call(d);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ec(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ec(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function $e(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{$e(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(m){if("object"===typeof m&&
null!==m&&(m===Pd||"function"===typeof m.then))throw m;b.replay.pendingTasks--;c=Pe(a,b.componentStack);var l=b.blockedBoundary,n=m;c=W(a,n,c);Ze(a,l,d,k,n,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)k=c[d],b.treeContext=Kd(f,g,d),l=h[d],"number"===typeof l?(Ye(a,b,l,k,d),delete h[d]):Te(a,b,k,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Kd(f,g,h),
Te(a,b,d,h);b.treeContext=f;b.keyPath=e}function Xe(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function Te(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,l=b.componentStack,n=b.blockedSegment;if(null===n)try{return X(a,b,null,c,d)}catch(q){if(ne(),c=q===Pd?Td():q,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=oe();a=Ne(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=l;Gd(g);return}}else{var m=n.children.length,p=n.chunks.length;try{return X(a,b,null,c,d)}catch(q){if(ne(),n.children.length=m,n.chunks.length=p,c=q===Pd?Td():q,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=oe();n=b.blockedSegment;m=Ie(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(m);n.lastPushedText=!1;a=Je(a,d,b.node,b.childIndex,b.blockedBoundary,m,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,
b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=l;Gd(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Gd(g);throw c;}function af(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,bf(this,b,a))}
function Ze(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)Ze(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,l=f,n=Me(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=l;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var m in d)delete d[m]}}
function cf(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){W(b,c,d);Qe(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=W(b,c,d),Ze(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&df(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=Pe(b,a.componentStack),a=W(b,c,a),d.errorDigest=a,Xe(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return cf(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&ef(b)}
function ff(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),l=k.next();0<e.remainingCapacity&&!l.done;l=k.next()){var n=l.value,m=n.props,p=m.href,q=n.props,C=Sb(q.href,"style",{crossOrigin:q.crossOrigin,integrity:q.integrity,
nonce:q.nonce,type:q.type,fetchPriority:q.fetchPriority,referrerPolicy:q.referrerPolicy,media:q.media});if(2<=(e.remainingCapacity-=C.length))c.resets.style[p]=B,f&&(f+=", "),f+=C,c.resets.style[p]="string"===typeof m.crossOrigin||"string"===typeof m.integrity?[m.crossOrigin,m.integrity]:B;else break b}}f?d({Link:f}):d({})}}}catch(z){W(a,z,{})}}function df(a){null===a.trackedPostpones&&ff(a,!0);a.onShellError=Ee;a=a.onShellReady;a()}
function ef(a){ff(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function We(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&We(a,c)}else a.completedSegments.push(b)}
function bf(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&df(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&We(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(af,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(We(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&ef(a)}
function Le(a){if(2!==a.status){var b=Ad,c=Be.current;Be.current=ze;var d=Ce.current;Ce.current=Ae;var e=T;T=a;var f=ye;ye=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],l=a,n=k.blockedBoundary;l.renderState.boundaryResources=n?n.resources:null;var m=k.blockedSegment;if(null===m){var p=l;if(0!==k.replay.pendingTasks){Gd(k.context);try{var q=k.thenableState;k.thenableState=null;X(p,k,q,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);bf(p,k.blockedBoundary,null)}catch(H){ne();var C=H===Pd?Td():H;if("object"===typeof C&&null!==C&&"function"===typeof C.then){var z=k.ping;C.then(z,z);k.thenableState=oe()}else{k.replay.pendingTasks--;k.abortSet.delete(k);var A=Pe(p,k.componentStack);l=void 0;var t=p,r=k.blockedBoundary,D=C,w=k.replay.nodes,v=k.replay.slots;l=W(t,D,A);Ze(t,r,w,v,D,l);p.pendingRootTasks--;0===p.pendingRootTasks&&df(p);p.allPendingTasks--;0===p.allPendingTasks&&ef(p)}}finally{p.renderState.boundaryResources=
null}}}else if(p=void 0,t=m,0===t.status){Gd(k.context);var E=t.children.length,Q=t.chunks.length;try{var F=k.thenableState;k.thenableState=null;X(l,k,F,k.node,k.childIndex);l.renderState.generateStaticMarkup||t.lastPushedText&&t.textEmbedded&&t.chunks.push("\x3c!-- --\x3e");k.abortSet.delete(k);t.status=1;bf(l,k.blockedBoundary,t)}catch(H){ne();t.children.length=E;t.chunks.length=Q;var V=H===Pd?Td():H;if("object"===typeof V&&null!==V&&"function"===typeof V.then){var ma=k.ping;V.then(ma,ma);k.thenableState=
oe()}else{var fa=Pe(l,k.componentStack);k.abortSet.delete(k);t.status=4;var L=k.blockedBoundary;p=W(l,V,fa);null===L?Qe(l,V):(L.pendingTasks--,4!==L.status&&(L.status=4,L.errorDigest=p,Xe(l,L),L.parentFlushed&&l.clientRenderedBoundaries.push(L)));l.allPendingTasks--;0===l.allPendingTasks&&ef(l)}}finally{l.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&gf(a,a.destination)}catch(H){W(a,H,{}),Qe(a,H)}finally{ye=f,Be.current=c,Ce.current=d,c===ze&&Gd(b),T=e}}}
function hf(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;b.push('<template id="');b.push(a.placeholderPrefix);a=d.toString(16);b.push(a);return b.push('"></template>');case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)b.push(d[f]);e=jf(a,b,e)}for(;f<d.length-1;f++)b.push(d[f]);f<d.length&&(e=b.push(d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function jf(a,b,c){var d=c.boundary;if(null===d)return hf(a,b,c);d.parentFlushed=!0;if(4===d.status)return a.renderState.generateStaticMarkup||(d=d.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),d&&(b.push(' data-dgst="'),d=y(d),b.push(d),b.push('"')),b.push("></template>")),hf(a,b,c),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==d.status)return 0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Vb(b,a.renderState,
d.rootSegmentID),hf(a,b,c),b.push("\x3c!--/$--\x3e");if(d.byteSize>a.progressiveChunkSize)return d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Vb(b,a.renderState,d.rootSegmentID),hf(a,b,c),b.push("\x3c!--/$--\x3e");c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach(Bc,e),c.stylesheets.forEach(Cc,e));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=d.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
jf(a,b,c[0]);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function kf(a,b,c){Wb(b,a.renderState,c.parentFormatContext,c.id);jf(a,b,c);return Xb(b,c.parentFormatContext)}
function lf(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)mf(a,b,c,d[e]);d.length=0;oc(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),uc(b,c)):(b.push('" data-sty="'),vc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Ub(b,a)&&d}
function mf(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return kf(a,b,d)}if(e===c.rootSegmentID)return kf(a,b,d);kf(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);e=e.toString(16);b.push(e);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(e);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function gf(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var l=e.htmlChunks,n=e.headChunks;f=0;if(l){for(f=0;f<l.length;f++)b.push(l[f]);if(n)for(f=0;f<n.length;f++)b.push(n[f]);else{var m=P("head");b.push(m);
b.push(">")}}else if(n)for(f=0;f<n.length;f++)b.push(n[f]);var p=e.charsetChunks;for(f=0;f<p.length;f++)b.push(p[f]);p.length=0;e.preconnects.forEach(R,b);e.preconnects.clear();var q=e.preconnectChunks;for(f=0;f<q.length;f++)b.push(q[f]);q.length=0;e.fontPreloads.forEach(R,b);e.fontPreloads.clear();e.highImagePreloads.forEach(R,b);e.highImagePreloads.clear();e.styles.forEach(rc,b);var C=e.importMapChunks;for(f=0;f<C.length;f++)b.push(C[f]);C.length=0;e.bootstrapScripts.forEach(R,b);e.scripts.forEach(R,
b);e.scripts.clear();e.bulkPreloads.forEach(R,b);e.bulkPreloads.clear();var z=e.preloadChunks;for(f=0;f<z.length;f++)b.push(z[f]);z.length=0;var A=e.hoistableChunks;for(f=0;f<A.length;f++)b.push(A[f]);A.length=0;if(l&&null===n){var t=Mb("head");b.push(t)}jf(a,b,d);a.completedRootSegment=null;Ub(b,a.renderState)}else return;var r=a.renderState;d=0;r.preconnects.forEach(R,b);r.preconnects.clear();var D=r.preconnectChunks;for(d=0;d<D.length;d++)b.push(D[d]);D.length=0;r.fontPreloads.forEach(R,b);r.fontPreloads.clear();
r.highImagePreloads.forEach(R,b);r.highImagePreloads.clear();r.styles.forEach(tc,b);r.scripts.forEach(R,b);r.scripts.clear();r.bulkPreloads.forEach(R,b);r.bulkPreloads.clear();var w=r.preloadChunks;for(d=0;d<w.length;d++)b.push(w[d]);w.length=0;var v=r.hoistableChunks;for(d=0;d<v.length;d++)b.push(v[d]);v.length=0;var E=a.clientRenderedBoundaries;for(c=0;c<E.length;c++){var Q=E[c];r=b;var F=a.resumableState,V=a.renderState,ma=Q.rootSegmentID,fa=Q.errorDigest,L=Q.errorMessage,H=Q.errorComponentStack,
Y=0===F.streamingFormat;Y?(r.push(V.startInlineScript),0===(F.instructions&4)?(F.instructions|=4,r.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):r.push('$RX("')):r.push('<template data-rxi="" data-bid="');r.push(V.boundaryPrefix);var Ra=ma.toString(16);r.push(Ra);Y&&r.push('"');if(fa||L||H)if(Y){r.push(",");var Sa=hc(fa||"");r.push(Sa)}else{r.push('" data-dgst="');
var Ta=y(fa||"");r.push(Ta)}if(L||H)if(Y){r.push(",");var na=hc(L||"");r.push(na)}else{r.push('" data-msg="');var S=y(L||"");r.push(S)}if(H)if(Y){r.push(",");var sb=hc(H);r.push(sb)}else{r.push('" data-stck="');var oa=y(H);r.push(oa)}if(Y?!r.push(")\x3c/script>"):!r.push('"></template>')){a.destination=null;c++;E.splice(0,c);return}}E.splice(0,c);var pa=a.completedBoundaries;for(c=0;c<pa.length;c++)if(!lf(a,b,pa[c])){a.destination=null;c++;pa.splice(0,c);return}pa.splice(0,c);var ca=a.partialBoundaries;
for(c=0;c<ca.length;c++){var qa=ca[c];a:{E=a;Q=b;E.renderState.boundaryResources=qa.resources;var ra=qa.completedSegments;for(F=0;F<ra.length;F++)if(!mf(E,Q,qa,ra[F])){F++;ra.splice(0,F);var Ua=!1;break a}ra.splice(0,F);Ua=oc(Q,qa.resources,E.renderState)}if(!Ua){a.destination=null;c++;ca.splice(0,c);return}}ca.splice(0,c);var sa=a.completedBoundaries;for(c=0;c<sa.length;c++)if(!lf(a,b,sa[c])){a.destination=null;c++;sa.splice(0,c);return}sa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&
0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&(ca=Mb("body"),b.push(ca)),c.hasHtml&&(c=Mb("html"),b.push(c)),b.push(null),a.destination=null)}}function nf(a){a.flushScheduled=null!==a.destination;Le(a);null===a.trackedPostpones&&ff(a,0===a.pendingRootTasks)}function yc(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?gf(a,b):a.flushScheduled=!1}}
function of(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{gf(a,b)}catch(c){W(a,c,{}),Qe(a,c)}}}function pf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return cf(e,a,d)});c.clear()}null!==a.destination&&gf(a,a.destination)}catch(e){W(a,e,{}),Qe(a,e)}}function qf(){}
function rf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=nb(b?b.identifierPrefix:void 0,void 0);a=Fe(a,b,Dc(b,c),ob(),Infinity,qf,void 0,function(){h=!0},void 0,void 0,void 0);nf(a);pf(a,d);of(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function sf(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var tf=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}sf(b,a);var c=b.prototype;c._destroy=function(d,e){pf(this.request);e(d)};c._read=function(){this.startedFlowing&&of(this.request,this)};return b}(ea.Readable);function uf(){}
function vf(a,b){var c=new tf;b=nb(b?b.identifierPrefix:void 0,void 0);var d=Fe(a,b,Dc(b,!1),ob(),Infinity,uf,function(){c.startedFlowing=!0;of(d,c)},void 0,void 0,void 0);c.request=d;nf(d);return c}exports.renderToNodeStream=function(a,b){return vf(a,b)};exports.renderToStaticMarkup=function(a,b){return rf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return vf(a,b)};exports.renderToString=function(a,b){return rf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-canary-60a927d04-20240113";

//# sourceMappingURL=react-dom-server-legacy.node.production.min.js.map
