(()=>{var e={};e.id=469,e.ids=[469],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8922:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>c});var i=s(482),r=s(9108),a=s(2563),o=s.n(a),n=s(8300),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9914)),"/Users/<USER>/Downloads/astrology/src/app/services/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,4566)),"/Users/<USER>/Downloads/astrology/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Downloads/astrology/src/app/services/page.tsx"],p="/services/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3149:(e,t,s)=>{Promise.resolve().then(s.bind(s,9597))},9597:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var i=s(5344);s(3729);var r=s(6506),a=s(7292),o=s(9758),n=s(1902);let l=()=>{let{t:e}=(0,o.$G)(["common","home"]),t=[{icon:n.UI,title:"Daily Horoscope",titleHi:"दैनिक राशिफल",description:"Get personalized daily predictions based on your zodiac sign and planetary positions.",descriptionHi:"अपनी राशि और ग्रहों की स्थिति के आधार पर व्यक्तिगत दैनिक भविष्यवाणी प्राप्त करें।",color:"text-temple-gold-500",bgColor:"bg-temple-gold-50",href:"/horoscope",features:["Daily predictions","Weekly forecasts","Monthly overview","Planetary transits"]},{icon:n.N8,title:"Kundli Analysis",titleHi:"कुंडली विश्लेषण",description:"Comprehensive birth chart analysis with detailed insights into your life path.",descriptionHi:"आपके जीवन पथ की विस्तृत अंतर्दृष्टि के साथ व्यापक जन्म कुंडली विश्लेषण।",color:"text-saffron-500",bgColor:"bg-saffron-50",href:"/kundli",features:["Birth chart creation","Planetary analysis","Dasha predictions","Remedial measures"]},{icon:n.ht,title:"Live Consultation",titleHi:"लाइव परामर्श",description:"Connect with experienced astrologers for personalized guidance and solutions.",descriptionHi:"व्यक्तिगत मार्गदर्शन और समाधान के लिए अनुभवी ज्योतिषियों से जुड़ें।",color:"text-maroon-500",bgColor:"bg-maroon-50",href:"/consultation",features:["Video consultation","Chat support","Phone consultation","Expert astrologers"]},{icon:n.X5,title:"Puja Booking",titleHi:"पूजा बुकिंग",description:"Book authentic Hindu pujas and rituals performed by experienced priests.",descriptionHi:"अनुभवी पुजारियों द्वारा किए गए प्रामाणिक हिंदू पूजा और अनुष्ठान बुक करें।",color:"text-divine-purple-500",bgColor:"bg-divine-purple-50",href:"/puja",features:["Traditional rituals","Online ceremonies","Custom pujas","Sacred materials"]},{icon:n.pO,title:"Gemstone Consultation",titleHi:"रत्न परामर्श",description:"Discover the right gemstones to enhance your spiritual energy and well-being.",descriptionHi:"अपनी आध्यात्मिक ऊर्जा और कल्याण को बढ़ाने के लिए सही रत्नों की खोज करें।",color:"text-lotus-pink-500",bgColor:"bg-lotus-pink-50",href:"/gemstones",features:["Gemstone selection","Authenticity guarantee","Wearing guidelines","Energization rituals"]},{icon:n.UW,title:"Vastu Shastra",titleHi:"वास्तु शास्त्र",description:"Harmonize your living and working spaces with ancient Vastu principles.",descriptionHi:"प्राचीन वास्तु सिद्धांतों के साथ अपने रहने और काम करने के स्थानों को सामंजस्यपूर्ण बनाएं।",color:"text-earth-brown-500",bgColor:"bg-earth-brown-50",href:"/vastu",features:["Home consultation","Office planning","Remedial solutions","Construction guidance"]}];return(0,i.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,i.jsxs)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,i.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[i.jsx("div",{className:"absolute top-10 left-10",children:i.jsx(n.ht,{size:100,className:"text-saffron-400 om-pulse"})}),i.jsx("div",{className:"absolute bottom-10 right-10",children:i.jsx(n.pO,{size:120,className:"text-lotus-pink-400"})})]}),i.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,i.jsxs)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[i.jsx("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Our Divine Services"}),i.jsx("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Explore our comprehensive range of spiritual and astrological services designed to guide you on your divine path"})]})})]}),i.jsx("section",{className:"py-20 bg-white",children:i.jsx("div",{className:"spiritual-container",children:i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map((e,t)=>{let s=e.icon;return i.jsx(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},whileHover:{y:-8,scale:1.02},transition:{duration:.5,delay:.1*t,hover:{duration:.3}},viewport:{once:!0},className:"spiritual-card-enhanced group cursor-pointer",children:(0,i.jsxs)(r.default,{href:e.href,className:"block h-full",children:[i.jsx("div",{className:`${e.bgColor} w-16 h-16 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`,children:i.jsx(s,{className:`w-8 h-8 ${e.color}`})}),i.jsx("h3",{className:"text-xl font-serif font-bold mb-3 group-hover:text-saffron-600 transition-colors",children:e.title}),i.jsx("p",{className:"text-gray-600 mb-4 leading-relaxed",children:e.description}),i.jsx("ul",{className:"space-y-2 mb-6",children:e.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center text-sm text-gray-500",children:[i.jsx("div",{className:"w-1.5 h-1.5 bg-saffron-400 rounded-full mr-2"}),e]},t))}),i.jsx("div",{className:"mt-auto text-saffron-500 font-medium text-sm opacity-0 group-hover:opacity-100 transition-opacity",children:"Learn More →"})]})},e.href)})})})}),(0,i.jsxs)("section",{className:"py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-saffron-900 text-white relative overflow-hidden",children:[i.jsx("div",{className:"absolute inset-0 opacity-10",children:i.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:i.jsx(n.UW,{size:200,className:"text-saffron-400 mandala-rotate"})})}),i.jsx("div",{className:"spiritual-container text-center relative z-10",children:(0,i.jsxs)(a.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:[i.jsx("h2",{className:"text-3xl md:text-4xl font-serif font-bold mb-6 gradient-text",children:"Ready to Begin Your Spiritual Journey?"}),i.jsx("p",{className:"text-xl text-gray-300 mb-8 max-w-2xl mx-auto",children:"Connect with our expert astrologers and discover the divine guidance that awaits you"}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[i.jsx(r.default,{href:"/consultation",className:"spiritual-button text-lg py-3 px-8 sacred-glow",children:"Book Consultation"}),i.jsx(r.default,{href:"/contact",className:"spiritual-button-secondary text-lg py-3 px-8 border-white text-white hover:bg-white hover:text-gray-900",children:"Contact Us"})]})]})})]})]})}},9914:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>r,default:()=>o});let i=(0,s(6843).createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/services/page.tsx`),{__esModule:r,$$typeof:a}=i,o=i.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[651,713],()=>s(8922));module.exports=i})();