(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[11],{4543:function(e,s,a){Promise.resolve().then(a.bind(a,4218))},4218:function(e,s,a){"use strict";a.r(s);var t=a(3827),r=a(4090),i=a(8792),l=a(8371),n=a(2635),c=a(3665),d=a(5228),o=a(463);s.default=()=>{let{t:e}=(0,n.$G)(["common"]),[s,a]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",dateOfBirth:"",gender:"",agreeToTerms:!1}),[m,x]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(!1),g=e=>{let{name:s,value:t,type:r}=e.target;a(a=>({...a,[s]:"checkbox"===r?e.target.checked:t}))},j=async e=>{if(e.preventDefault(),s.password!==s.confirmPassword){alert("Passwords do not match!");return}if(!s.agreeToTerms){alert("Please agree to the terms and conditions");return}f(!0),await new Promise(e=>setTimeout(e,2e3)),alert("Registration successful! Welcome to Kashi Vedic family."),f(!1)};return(0,t.jsxs)("div",{className:"min-h-screen pt-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30",children:[(0,t.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,t.jsx)("div",{className:"absolute top-20 left-10 opacity-10",children:(0,t.jsx)(c.ht,{size:150,className:"text-saffron-400 om-pulse"})}),(0,t.jsx)("div",{className:"absolute bottom-20 right-10 opacity-10",children:(0,t.jsx)(c.pO,{size:180,className:"text-lotus-pink-400"})})]}),(0,t.jsx)("div",{className:"spiritual-container py-20 relative z-10",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"spiritual-card-enhanced",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-saffron rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,t.jsx)(c.ht,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h1",{className:"text-2xl font-serif font-bold gradient-text mb-2",children:"Join Our Spiritual Community"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Create your account to begin your divine journey"})]}),(0,t.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,t.jsx)("input",{type:"text",name:"firstName",value:s.firstName,onChange:g,className:"spiritual-input",placeholder:"Enter first name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,t.jsx)("input",{type:"text",name:"lastName",value:s.lastName,onChange:g,className:"spiritual-input",placeholder:"Enter last name",required:!0})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",name:"email",value:s.email,onChange:g,className:"spiritual-input",placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number *"}),(0,t.jsx)("input",{type:"tel",name:"phone",value:s.phone,onChange:g,className:"spiritual-input",placeholder:"+91 XXXXX XXXXX",required:!0})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,t.jsx)("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:g,className:"spiritual-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,t.jsxs)("select",{name:"gender",value:s.gender,onChange:g,className:"spiritual-input",required:!0,children:[(0,t.jsx)("option",{value:"",children:"Select Gender"}),(0,t.jsx)("option",{value:"male",children:"Male"}),(0,t.jsx)("option",{value:"female",children:"Female"}),(0,t.jsx)("option",{value:"other",children:"Other"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:m?"text":"password",name:"password",value:s.password,onChange:g,className:"spiritual-input pr-10",placeholder:"Create password",required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!m),children:m?(0,t.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(o.Z,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:u?"text":"password",name:"confirmPassword",value:s.confirmPassword,onChange:g,className:"spiritual-input pr-10",placeholder:"Confirm password",required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!u),children:u?(0,t.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(o.Z,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",checked:s.agreeToTerms,onChange:g,className:"h-4 w-4 text-saffron-600 focus:ring-saffron-500 border-gray-300 rounded",required:!0})}),(0,t.jsx)("div",{className:"ml-3 text-sm",children:(0,t.jsxs)("label",{htmlFor:"agreeToTerms",className:"text-gray-700",children:["I agree to the"," ",(0,t.jsx)(i.default,{href:"/terms",className:"text-saffron-600 hover:text-saffron-500",children:"Terms and Conditions"})," ","and"," ",(0,t.jsx)(i.default,{href:"/privacy",className:"text-saffron-600 hover:text-saffron-500",children:"Privacy Policy"})]})})]}),(0,t.jsx)("button",{type:"submit",disabled:p,className:"w-full spiritual-button py-3 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Creating Account...":"Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(i.default,{href:"/login",className:"text-saffron-600 hover:text-saffron-500 font-medium",children:"Sign in here"})]})})]}),(0,t.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mt-8",children:(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-lg font-serif font-bold mb-4 gradient-text text-center",children:"What You'll Get as a Member"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDD2E"}),"Daily personalized horoscope"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDCCA"}),"Free kundli generation"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDC68‍\uD83C\uDFEB"}),"Access to expert astrologers"]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83C\uDFAF"}),"Personalized remedies"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDCF1"}),"Mobile app access"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"text-saffron-500 mr-2",children:"\uD83D\uDD14"}),"Important transit alerts"]})]})]})]})})]})})]})}},463:function(e,s,a){"use strict";var t=a(4090);let r=t.forwardRef(function(e,s){let{title:a,titleId:r,...i}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},i),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});s.Z=r},5228:function(e,s,a){"use strict";var t=a(4090);let r=t.forwardRef(function(e,s){let{title:a,titleId:r,...i}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},i),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});s.Z=r}},function(e){e.O(0,[371,635,792,665,971,69,744],function(){return e(e.s=4543)}),_N_E=e.O()}]);