(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[924],{5709:function(e,s,a){Promise.resolve().then(a.bind(a,6120))},6120:function(e,s,a){"use strict";a.r(s);var t=a(3827),i=a(4090),l=a(8371),n=a(2635),r=a(3665);s.default=()=>{let{t:e}=(0,n.$G)(["common","home"]),[s,a]=(0,i.useState)({name:"",gender:"",dateOfBirth:"",timeOfBirth:"",placeOfBirth:"",latitude:"",longitude:""}),[c,d]=(0,i.useState)(!1),[x,o]=(0,i.useState)("rashi"),[m,h]=(0,i.useState)(!1),p=e=>{let{name:s,value:t}=e.target;a(e=>({...e,[s]:t}))},u=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,2e3)),h(!1),d(!0)},j=[{icon:r.N8,title:"Birth Chart Analysis",titleHi:"जन्म कुंडली विश्लेषण",description:"Detailed analysis of planetary positions at the time of birth"},{icon:r.UW,title:"Dasha Predictions",titleHi:"दशा भविष्यवाणी",description:"Planetary periods and their effects on your life"},{icon:r.ht,title:"Remedial Measures",titleHi:"उपचारात्मक उपाय",description:"Personalized remedies to overcome challenges"},{icon:r.pO,title:"Life Predictions",titleHi:"जीवन भविष्यवाणी",description:"Insights into career, marriage, health, and prosperity"}];return(0,t.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,t.jsxs)("section",{className:"py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,t.jsx)("div",{className:"absolute top-10 left-10",children:(0,t.jsx)(r.ht,{size:100,className:"text-bhagwa-400 om-pulse"})}),(0,t.jsx)("div",{className:"absolute bottom-10 right-10",children:(0,t.jsx)(r.pO,{size:120,className:"text-lotus-pink-400"})}),(0,t.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",children:(0,t.jsx)(r.UW,{size:200,className:"text-divine-purple-300 mandala-rotate"})})]}),(0,t.jsx)("div",{className:"spiritual-container text-center relative z-10",children:(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,t.jsx)("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Kundli Analysis"}),(0,t.jsx)("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Generate your personalized birth chart and discover the cosmic blueprint of your life"})]})})]}),(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsxs)("div",{className:"spiritual-container",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"What You'll Discover"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Comprehensive insights from your birth chart analysis"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:j.map((e,s)=>{let a=e.icon;return(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*s},viewport:{once:!0},className:"text-center spiritual-card-enhanced",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-bhagwa rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,t.jsx)(a,{className:"w-8 h-8 text-white"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title)})})]})}),(0,t.jsx)("section",{className:"py-16 bg-gray-50",children:(0,t.jsx)("div",{className:"spiritual-container max-w-2xl mx-auto",children:(0,t.jsx)(l.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},children:(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h2",{className:"text-2xl font-serif font-bold mb-6 text-center gradient-text",children:"Generate Your Kundli"}),(0,t.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,t.jsx)("input",{type:"text",name:"name",value:s.name,onChange:p,className:"spiritual-input",placeholder:"Enter your full name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gender *"}),(0,t.jsxs)("select",{name:"gender",value:s.gender,onChange:p,className:"spiritual-input",required:!0,children:[(0,t.jsx)("option",{value:"",children:"Select Gender"}),(0,t.jsx)("option",{value:"male",children:"Male"}),(0,t.jsx)("option",{value:"female",children:"Female"}),(0,t.jsx)("option",{value:"other",children:"Other"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date of Birth *"}),(0,t.jsx)("input",{type:"date",name:"dateOfBirth",value:s.dateOfBirth,onChange:p,className:"spiritual-input",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Time of Birth *"}),(0,t.jsx)("input",{type:"time",name:"timeOfBirth",value:s.timeOfBirth,onChange:p,className:"spiritual-input",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Place of Birth *"}),(0,t.jsx)("input",{type:"text",name:"placeOfBirth",value:s.placeOfBirth,onChange:p,className:"spiritual-input",placeholder:"City, State, Country",required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Latitude (Optional)"}),(0,t.jsx)("input",{type:"text",name:"latitude",value:s.latitude,onChange:p,className:"spiritual-input",placeholder:"e.g., 25.3176"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Longitude (Optional)"}),(0,t.jsx)("input",{type:"text",name:"longitude",value:s.longitude,onChange:p,className:"spiritual-input",placeholder:"e.g., 82.9739"})]})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("button",{type:"submit",disabled:m,className:"spiritual-button text-lg py-3 px-8 sacred-glow disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Generating Kundli..."]}):"Generate Kundli"})})]})]})})})}),c&&(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsx)("div",{className:"spiritual-container",children:(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,t.jsxs)("h2",{className:"text-3xl font-serif font-bold mb-8 text-center gradient-text",children:["Your Kundli - ",s.name]}),(0,t.jsx)("div",{className:"flex justify-center mb-8",children:(0,t.jsx)("div",{className:"bg-gray-100 rounded-lg p-1 flex space-x-1",children:[{id:"rashi",label:"Rashi Chart",labelHi:"राशि चार्ट"},{id:"navamsa",label:"Navamsa Chart",labelHi:"नवांश चार्ट"},{id:"dashamsa",label:"Dashamsa Chart",labelHi:"दशांश चार्ट"}].map(e=>(0,t.jsx)("button",{onClick:()=>o(e.id),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(x===e.id?"bg-bhagwa-400 text-white":"text-gray-600 hover:text-gray-900"),children:e.label},e.id))})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsxs)("h3",{className:"text-xl font-serif font-bold mb-4",children:["rashi"===x&&"Rashi Chart (Birth Chart)","navamsa"===x&&"Navamsa Chart (D9)","dashamsa"===x&&"Dashamsa Chart (D10)"]}),(0,t.jsx)("div",{className:"bg-gray-100 h-80 rounded-lg flex items-center justify-center relative",children:(0,t.jsx)("div",{className:"grid grid-cols-4 grid-rows-4 w-72 h-72 border-2 border-bhagwa-400",children:Array.from({length:16},(e,s)=>{let a=s<4?s+1:s<8?12-(s-4):s<12?9-(s-8):4+(s-12);return(0,t.jsxs)("div",{className:"border border-gray-300 flex items-center justify-center text-xs font-medium relative bg-white",children:[(0,t.jsx)("span",{className:"absolute top-1 left-1 text-bhagwa-600 text-xs",children:a}),"rashi"===x&&(0,t.jsxs)(t.Fragment,{children:[1===a&&(0,t.jsx)("span",{className:"text-red-600 text-xs",children:"Ma"}),5===a&&(0,t.jsx)("span",{className:"text-orange-600 text-xs",children:"Su"}),8===a&&(0,t.jsx)("span",{className:"text-blue-600 text-xs",children:"Mo"}),6===a&&(0,t.jsx)("span",{className:"text-green-600 text-xs",children:"Me"}),9===a&&(0,t.jsx)("span",{className:"text-purple-600 text-xs",children:"Ju"}),4===a&&(0,t.jsx)("span",{className:"text-pink-600 text-xs",children:"Ve"}),10===a&&(0,t.jsx)("span",{className:"text-gray-600 text-xs",children:"Sa"})]}),"navamsa"===x&&(0,t.jsxs)(t.Fragment,{children:[2===a&&(0,t.jsx)("span",{className:"text-red-600 text-xs",children:"Ma"}),7===a&&(0,t.jsx)("span",{className:"text-orange-600 text-xs",children:"Su"}),11===a&&(0,t.jsx)("span",{className:"text-blue-600 text-xs",children:"Mo"}),3===a&&(0,t.jsx)("span",{className:"text-green-600 text-xs",children:"Me"}),1===a&&(0,t.jsx)("span",{className:"text-purple-600 text-xs",children:"Ju"}),9===a&&(0,t.jsx)("span",{className:"text-pink-600 text-xs",children:"Ve"}),12===a&&(0,t.jsx)("span",{className:"text-gray-600 text-xs",children:"Sa"})]}),"dashamsa"===x&&(0,t.jsxs)(t.Fragment,{children:[3===a&&(0,t.jsx)("span",{className:"text-red-600 text-xs",children:"Ma"}),8===a&&(0,t.jsx)("span",{className:"text-orange-600 text-xs",children:"Su"}),2===a&&(0,t.jsx)("span",{className:"text-blue-600 text-xs",children:"Mo"}),7===a&&(0,t.jsx)("span",{className:"text-green-600 text-xs",children:"Me"}),11===a&&(0,t.jsx)("span",{className:"text-purple-600 text-xs",children:"Ju"}),5===a&&(0,t.jsx)("span",{className:"text-pink-600 text-xs",children:"Ve"}),1===a&&(0,t.jsx)("span",{className:"text-gray-600 text-xs",children:"Sa"})]})]},s)})})}),(0,t.jsx)("div",{className:"mt-4 text-sm text-gray-600",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Legend:"})," Su-Sun, Mo-Moon, Ma-Mars, Me-Mercury, Ju-Jupiter, Ve-Venus, Sa-Saturn"]})})]}),(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-4",children:"Birth Details"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Name:"}),(0,t.jsx)("span",{children:s.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Date of Birth:"}),(0,t.jsx)("span",{children:s.dateOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Time of Birth:"}),(0,t.jsx)("span",{children:s.timeOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Place of Birth:"}),(0,t.jsx)("span",{children:s.placeOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Sun Sign:"}),(0,t.jsx)("span",{children:"Leo ♌"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Moon Sign:"}),(0,t.jsx)("span",{children:"Scorpio ♏"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:"Ascendant:"}),(0,t.jsx)("span",{children:"Gemini ♊"})]})]})]}),(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-4",children:"Planetary Positions"}),(0,t.jsx)("div",{className:"space-y-2",children:[{planet:"Sun (सूर्य)",sign:"Leo",degree:"15\xb023'"},{planet:"Moon (चंद्र)",sign:"Scorpio",degree:"8\xb045'"},{planet:"Mars (मंगल)",sign:"Aries",degree:"22\xb012'"},{planet:"Mercury (बुध)",sign:"Virgo",degree:"3\xb056'"},{planet:"Jupiter (बृहस्पति)",sign:"Sagittarius",degree:"18\xb034'"},{planet:"Venus (शुक्र)",sign:"Cancer",degree:"12\xb028'"},{planet:"Saturn (शनि)",sign:"Capricorn",degree:"25\xb017'"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"font-medium",children:[e.planet,":"]}),(0,t.jsxs)("span",{children:[e.sign," ",e.degree]})]},s))})]}),(0,t.jsxs)("div",{className:"spiritual-card-enhanced",children:[(0,t.jsx)("h3",{className:"text-xl font-serif font-bold mb-4",children:"Key Insights"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Personality Traits"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Strong leadership qualities, creative nature, and natural charisma. You have a magnetic personality that draws people to you."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Career Prospects"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Excellent potential in creative fields, management, or entrepreneurship. Your innovative ideas will bring success."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-bhagwa-600 mb-2",children:"Relationships"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Deep emotional connections are important to you. You seek meaningful relationships and are a loyal partner."})]})]})]})]}),(0,t.jsxs)("div",{className:"text-center mt-8",children:[(0,t.jsx)("button",{className:"spiritual-button text-lg py-3 px-8 mr-4",children:"Download Full Report"}),(0,t.jsx)("button",{className:"spiritual-button-secondary text-lg py-3 px-8",children:"Book Consultation"})]})]})})})]})}}},function(e){e.O(0,[371,635,665,971,69,744],function(){return e(e.s=5709)}),_N_E=e.O()}]);