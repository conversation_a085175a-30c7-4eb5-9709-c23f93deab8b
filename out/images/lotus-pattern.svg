<svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="lotus" x="0" y="0" width="150" height="150" patternUnits="userSpaceOnUse">
      <g transform="translate(75,75)">
        <!-- <PERSON> petals -->
        <g opacity="0.6">
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(0)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(30)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(60)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(90)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(120)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(150)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(180)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(210)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(240)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(270)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(300)"/>
          <ellipse cx="0" cy="-30" rx="6" ry="15" fill="currentColor" transform="rotate(330)"/>
        </g>
        <!-- Center -->
        <circle cx="0" cy="0" r="8" fill="currentColor" opacity="0.8"/>
        <!-- Stem -->
        <rect x="-2" y="0" width="4" height="30" fill="currentColor" opacity="0.5"/>
      </g>
    </pattern>
  </defs>
  <rect width="150" height="150" fill="url(#lotus)"/>
</svg>
