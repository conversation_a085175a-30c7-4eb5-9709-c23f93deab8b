/*
 React
 react-dom-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var ba=require("util"),ca=require("crypto"),ka=require("async_hooks"),la=require("next/dist/compiled/react"),ma=require("react-dom");function pa(a){"function"===typeof a.flush&&a.flush()}var k=null,n=0,qa=!0;
function v(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<n&&(wa(a,k.subarray(0,n)),k=new Uint8Array(2048),n=0),wa(a,xa.encode(b));else{var c=k;0<n&&(c=k.subarray(n));c=xa.encodeInto(b,c);var d=c.read;n+=c.written;d<b.length&&(wa(a,k.subarray(0,n)),k=new Uint8Array(2048),n=xa.encodeInto(b.slice(d),k).written);2048===n&&(wa(a,k),k=new Uint8Array(2048),n=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<n&&(wa(a,k.subarray(0,n)),k=new Uint8Array(2048),n=0),wa(a,b)):(c=k.length-n,c<
b.byteLength&&(0===c?wa(a,k):(k.set(b.subarray(0,c),n),n+=c,wa(a,k),b=b.subarray(c)),k=new Uint8Array(2048),n=0),k.set(b,n),n+=b.byteLength,2048===n&&(wa(a,k),k=new Uint8Array(2048),n=0)))}function wa(a,b){a=a.write(b);qa=qa&&a}function y(a,b){v(a,b);return qa}function ya(a){k&&0<n&&a.write(k.subarray(0,n));k=null;n=0;qa=!0}var xa=new ba.TextEncoder;function z(a){return xa.encode(a)}
var B=Object.assign,C=Object.prototype.hasOwnProperty,Ea=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Fa={},Ga={};
function Ha(a){if(C.call(Ga,a))return!0;if(C.call(Fa,a))return!1;if(Ea.test(a))return Ga[a]=!0;Fa[a]=!0;return!1}
var Ia=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),Ja=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ka=/["'&<>]/;
function D(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=Ka.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var La=/([A-Z])/g,Ma=/^ms-/,Ta=Array.isArray,Ua=la.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Va={pending:!1,data:null,method:null,action:null},Wa=ma.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,qb={prefetchDNS:Xa,preconnect:Ya,preload:Za,preloadModule:$a,preinitStyle:ab,preinitScript:bb,preinitModuleScript:cb},E=[],rb=z('"></template>'),sb=z("<script>"),tb=z("\x3c/script>"),ub=z('<script src="'),vb=z('<script type="module" src="'),wb=z('" nonce="'),xb=z('" integrity="'),
yb=z('" crossorigin="'),zb=z('" async="">\x3c/script>'),Ab=/(<\/|<)(s)(cript)/gi;function Bb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var Cb=z('<script type="importmap">'),Lb=z("\x3c/script>");function I(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Mb(a,b,c){switch(b){case "noscript":return I(2,null,a.tagScope|1);case "select":return I(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return I(3,null,a.tagScope);case "picture":return I(2,null,a.tagScope|2);case "math":return I(4,null,a.tagScope);case "foreignObject":return I(2,null,a.tagScope);case "table":return I(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return I(6,null,a.tagScope);case "colgroup":return I(8,null,a.tagScope);case "tr":return I(7,null,a.tagScope)}return 5<=
a.insertionMode?I(2,null,a.tagScope):0===a.insertionMode?"html"===b?I(1,null,a.tagScope):I(2,null,a.tagScope):1===a.insertionMode?I(2,null,a.tagScope):a}var Nb=z("\x3c!-- --\x3e");function Ob(a,b,c,d){if(""===b)return d;d&&a.push(Nb);a.push(D(b));return!0}var Pb=new Map,Qb=z(' style="'),Rb=z(":"),Sb=z(";");
function Tb(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(C.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=D(d);e=D((""+e).trim())}else f=Pb.get(d),void 0===f&&(f=z(D(d.replace(La,"-$1").toLowerCase().replace(Ma,"-ms-"))),Pb.set(d,f)),e="number"===typeof e?0===e||Ia.has(d)?""+e:e+"px":
D((""+e).trim());c?(c=!1,a.push(Qb,f,Rb,e)):a.push(Sb,f,Rb,e)}}c||a.push(L)}var O=z(" "),P=z('="'),L=z('"'),Ub=z('=""');function Vb(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(O,b,Ub)}function Q(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(O,b,P,D(c),L)}function Wb(a){var b=a.nextFormID++;return a.idPrefix+b}var Xb=z(D("javascript:throw new Error('A React form was unexpectedly submitted.')")),Yb=z('<input type="hidden"');
function Zb(a,b){this.push(Yb);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");Q(this,"name",b);Q(this,"value",a);this.push($b)}
function ac(a,b,c,d,e,f,g,h){var l=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Wb(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,l=b.data):(a.push(O,"formAction",P,Xb,L),g=f=e=d=h=null,bc(b,c)));null!=h&&R(a,"name",h);null!=d&&R(a,"formAction",d);null!=e&&R(a,"formEncType",e);null!=f&&R(a,"formMethod",f);null!=g&&R(a,"formTarget",g);return l}
function R(a,b,c){switch(b){case "className":Q(a,"class",c);break;case "tabIndex":Q(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":Q(a,b,c);break;case "style":Tb(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(O,b,P,D(""+c),L);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":break;
case "autoFocus":case "multiple":case "muted":Vb(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(O,"xlink:href",P,D(""+c),L);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(O,b,P,D(c),L);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(O,b,Ub);break;case "capture":case "download":!0===c?a.push(O,b,Ub):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(O,b,P,D(c),L);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(O,b,P,D(c),L);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(O,b,P,D(c),L);break;case "xlinkActuate":Q(a,"xlink:actuate",c);break;case "xlinkArcrole":Q(a,
"xlink:arcrole",c);break;case "xlinkRole":Q(a,"xlink:role",c);break;case "xlinkShow":Q(a,"xlink:show",c);break;case "xlinkTitle":Q(a,"xlink:title",c);break;case "xlinkType":Q(a,"xlink:type",c);break;case "xmlBase":Q(a,"xml:base",c);break;case "xmlLang":Q(a,"xml:lang",c);break;case "xmlSpace":Q(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=Ja.get(b)||b,Ha(b)){switch(typeof c){case "function":case "symbol":return;case "boolean":var d=b.toLowerCase().slice(0,
5);if("data-"!==d&&"aria-"!==d)return}a.push(O,b,P,D(c),L)}}}var S=z(">"),$b=z("/>");function cc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function dc(a){var b="";la.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var ec=z(' selected=""'),fc=z('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'A React form was unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.getRootNode(),(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,\nd,b))}});');
function bc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,fc,tb))}var gc=z("\x3c!--F!--\x3e"),hc=z("\x3c!--F--\x3e");
function ic(a,b,c,d,e,f,g){var h=b.rel,l=b.href,m=b.precedence;if(3===f||g||null!=b.itemProp||"string"!==typeof h||"string"!==typeof l||""===l)return T(a,b),null;if("stylesheet"===b.rel){if("string"!==typeof m||null!=b.disabled||b.onLoad||b.onError)return T(a,b);f=d.styles.get(m);g=c.styleResources.hasOwnProperty(l)?c.styleResources[l]:void 0;null!==g?(c.styleResources[l]=null,f||(f={precedence:D(m),rules:[],hrefs:[],sheets:new Map},d.styles.set(m,f)),b={state:0,props:B({},b,{"data-precedence":b.precedence,
precedence:null})},g&&(2===g.length&&jc(b.props,g),(c=d.preloads.stylesheets.get(l))&&0<c.length?c.length=0:b.state=1),f.sheets.set(l,b),d.boundaryResources&&d.boundaryResources.stylesheets.add(b)):f&&(l=f.sheets.get(l))&&d.boundaryResources&&d.boundaryResources.stylesheets.add(l);e&&a.push(Nb);return null}if(b.onLoad||b.onError)return T(a,b);e&&a.push(Nb);switch(b.rel){case "preconnect":case "dns-prefetch":return T(d.preconnectChunks,b);case "preload":return T(d.preloadChunks,b);default:return T(d.hoistableChunks,
b)}}function T(a,b){a.push(W("link"));for(var c in b)if(C.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:R(a,c,d)}}a.push($b);return null}
function kc(a,b,c){a.push(W(c));for(var d in b)if(C.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:R(a,d,e)}}a.push($b);return null}
function lc(a,b){a.push(W("title"));var c=null,d=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:R(a,e,f)}}a.push(S);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(D(""+b));cc(a,d,c);a.push(mc("title"));return null}
function nc(a,b){a.push(W("script"));var c=null,d=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:R(a,e,f)}}a.push(S);cc(a,d,c);"string"===typeof c&&a.push(D(c));a.push(mc("script"));return null}
function oc(a,b,c){a.push(W(c));var d=c=null,e;for(e in b)if(C.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:R(a,e,f)}}a.push(S);cc(a,d,c);return"string"===typeof c?(a.push(D(c)),null):c}var yc=z("\n"),zc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Ac=new Map;function W(a){var b=Ac.get(a);if(void 0===b){if(!zc.test(a))throw Error("Invalid tag: "+a);b=z("<"+a);Ac.set(a,b)}return b}var Bc=z("<!DOCTYPE html>");
function Cc(a,b,c,d,e,f,g){switch(b){case "div":case "span":case "svg":case "path":case "a":case "g":case "p":case "li":break;case "select":a.push(W("select"));var h=null,l=null,m;for(m in c)if(C.call(c,m)){var q=c[m];if(null!=q)switch(m){case "children":h=q;break;case "dangerouslySetInnerHTML":l=q;break;case "defaultValue":case "value":break;default:R(a,m,q)}}a.push(S);cc(a,l,h);return h;case "option":var p=f.selectedValue;a.push(W("option"));var r=null,t=null,A=null,F=null,u;for(u in c)if(C.call(c,
u)){var x=c[u];if(null!=x)switch(u){case "children":r=x;break;case "selected":A=x;break;case "dangerouslySetInnerHTML":F=x;break;case "value":t=x;default:R(a,u,x)}}if(null!=p){var da=null!==t?""+t:dc(r);if(Ta(p))for(var U=0;U<p.length;U++){if(""+p[U]===da){a.push(ec);break}}else""+p===da&&a.push(ec)}else A&&a.push(ec);a.push(S);cc(a,F,r);return r;case "textarea":a.push(W("textarea"));var w=null,G=null,H=null,ea;for(ea in c)if(C.call(c,ea)){var na=c[ea];if(null!=na)switch(ea){case "children":H=na;
break;case "value":w=na;break;case "defaultValue":G=na;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:R(a,ea,na)}}null===w&&null!==G&&(w=G);a.push(S);if(null!=H){if(null!=w)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Ta(H)){if(1<H.length)throw Error("<textarea> can only have at most one child.");w=""+H[0]}w=""+H}"string"===typeof w&&"\n"===w[0]&&a.push(yc);null!==w&&a.push(D(""+w));
return null;case "input":a.push(W("input"));var V=null,fa=null,ha=null,J=null,M=null,ra=null,sa=null,ta=null,Na=null,ia;for(ia in c)if(C.call(c,ia)){var aa=c[ia];if(null!=aa)switch(ia){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":V=aa;break;case "formAction":fa=aa;break;case "formEncType":ha=aa;break;case "formMethod":J=aa;break;case "formTarget":M=aa;break;case "defaultChecked":Na=
aa;break;case "defaultValue":sa=aa;break;case "checked":ta=aa;break;case "value":ra=aa;break;default:R(a,ia,aa)}}var td=ac(a,d,e,fa,ha,J,M,V);null!==ta?Vb(a,"checked",ta):null!==Na&&Vb(a,"checked",Na);null!==ra?R(a,"value",ra):null!==sa&&R(a,"value",sa);a.push($b);null!==td&&td.forEach(Zb,a);return null;case "button":a.push(W("button"));var db=null,ud=null,vd=null,wd=null,xd=null,yd=null,zd=null,eb;for(eb in c)if(C.call(c,eb)){var oa=c[eb];if(null!=oa)switch(eb){case "children":db=oa;break;case "dangerouslySetInnerHTML":ud=
oa;break;case "name":vd=oa;break;case "formAction":wd=oa;break;case "formEncType":xd=oa;break;case "formMethod":yd=oa;break;case "formTarget":zd=oa;break;default:R(a,eb,oa)}}var Ad=ac(a,d,e,wd,xd,yd,zd,vd);a.push(S);null!==Ad&&Ad.forEach(Zb,a);cc(a,ud,db);if("string"===typeof db){a.push(D(db));var Bd=null}else Bd=db;return Bd;case "form":a.push(W("form"));var fb=null,Cd=null,ua=null,gb=null,hb=null,ib=null,jb;for(jb in c)if(C.call(c,jb)){var va=c[jb];if(null!=va)switch(jb){case "children":fb=va;break;
case "dangerouslySetInnerHTML":Cd=va;break;case "action":ua=va;break;case "encType":gb=va;break;case "method":hb=va;break;case "target":ib=va;break;default:R(a,jb,va)}}var pc=null,qc=null;if("function"===typeof ua)if("function"===typeof ua.$$FORM_ACTION){var xf=Wb(d),Oa=ua.$$FORM_ACTION(xf);ua=Oa.action||"";gb=Oa.encType;hb=Oa.method;ib=Oa.target;pc=Oa.data;qc=Oa.name}else a.push(O,"action",P,Xb,L),ib=hb=gb=ua=null,bc(d,e);null!=ua&&R(a,"action",ua);null!=gb&&R(a,"encType",gb);null!=hb&&R(a,"method",
hb);null!=ib&&R(a,"target",ib);a.push(S);null!==qc&&(a.push(Yb),Q(a,"name",qc),a.push($b),null!==pc&&pc.forEach(Zb,a));cc(a,Cd,fb);if("string"===typeof fb){a.push(D(fb));var Dd=null}else Dd=fb;return Dd;case "menuitem":a.push(W("menuitem"));for(var Db in c)if(C.call(c,Db)){var Ed=c[Db];if(null!=Ed)switch(Db){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:R(a,Db,Ed)}}a.push(S);return null;case "title":if(3===f.insertionMode||
f.tagScope&1||null!=c.itemProp)var Fd=lc(a,c);else lc(e.hoistableChunks,c),Fd=null;return Fd;case "link":return ic(a,c,d,e,g,f.insertionMode,!!(f.tagScope&1));case "script":var rc=c.async;if("string"!==typeof c.src||!c.src||!rc||"function"===typeof rc||"symbol"===typeof rc||c.onLoad||c.onError||3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Gd=nc(a,c);else{var Eb=c.src;if("module"===c.type){var Fb=d.moduleScriptResources;var Hd=e.preloads.moduleScripts}else Fb=d.scriptResources,Hd=e.preloads.scripts;
var Gb=Fb.hasOwnProperty(Eb)?Fb[Eb]:void 0;if(null!==Gb){Fb[Eb]=null;var sc=c;if(Gb){2===Gb.length&&(sc=B({},c),jc(sc,Gb));var Id=Hd.get(Eb);Id&&(Id.length=0)}var Jd=[];e.scripts.add(Jd);nc(Jd,sc)}g&&a.push(Nb);Gd=null}return Gd;case "style":var Hb=c.precedence,za=c.href;if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp||"string"!==typeof Hb||"string"!==typeof za||""===za){a.push(W("style"));var Pa=null,Kd=null,kb;for(kb in c)if(C.call(c,kb)){var Ib=c[kb];if(null!=Ib)switch(kb){case "children":Pa=
Ib;break;case "dangerouslySetInnerHTML":Kd=Ib;break;default:R(a,kb,Ib)}}a.push(S);var lb=Array.isArray(Pa)?2>Pa.length?Pa[0]:null:Pa;"function"!==typeof lb&&"symbol"!==typeof lb&&null!==lb&&void 0!==lb&&a.push(D(""+lb));cc(a,Kd,Pa);a.push(mc("style"));var Ld=null}else{var Aa=e.styles.get(Hb);if(null!==(d.styleResources.hasOwnProperty(za)?d.styleResources[za]:void 0)){d.styleResources[za]=null;Aa?Aa.hrefs.push(D(za)):(Aa={precedence:D(Hb),rules:[],hrefs:[D(za)],sheets:new Map},e.styles.set(Hb,Aa));
var Md=Aa.rules,Qa=null,Nd=null,Jb;for(Jb in c)if(C.call(c,Jb)){var tc=c[Jb];if(null!=tc)switch(Jb){case "children":Qa=tc;break;case "dangerouslySetInnerHTML":Nd=tc}}var mb=Array.isArray(Qa)?2>Qa.length?Qa[0]:null:Qa;"function"!==typeof mb&&"symbol"!==typeof mb&&null!==mb&&void 0!==mb&&Md.push(D(""+mb));cc(Md,Nd,Qa)}Aa&&e.boundaryResources&&e.boundaryResources.styles.add(Aa);g&&a.push(Nb);Ld=void 0}return Ld;case "meta":if(3===f.insertionMode||f.tagScope&1||null!=c.itemProp)var Od=kc(a,c,"meta");
else g&&a.push(Nb),Od="string"===typeof c.charSet?kc(e.charsetChunks,c,"meta"):"viewport"===c.name?kc(e.preconnectChunks,c,"meta"):kc(e.hoistableChunks,c,"meta");return Od;case "listing":case "pre":a.push(W(b));var nb=null,ob=null,pb;for(pb in c)if(C.call(c,pb)){var Kb=c[pb];if(null!=Kb)switch(pb){case "children":nb=Kb;break;case "dangerouslySetInnerHTML":ob=Kb;break;default:R(a,pb,Kb)}}a.push(S);if(null!=ob){if(null!=nb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");
if("object"!==typeof ob||!("__html"in ob))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");var Ba=ob.__html;null!==Ba&&void 0!==Ba&&("string"===typeof Ba&&0<Ba.length&&"\n"===Ba[0]?a.push(yc,Ba):a.push(""+Ba))}"string"===typeof nb&&"\n"===nb[0]&&a.push(yc);return nb;case "img":var N=c.src,K=c.srcSet;if(!("lazy"===c.loading||!N&&!K||"string"!==typeof N&&null!=N||"string"!==typeof K&&
null!=K)&&"low"!==c.fetchPriority&&!1===!!(f.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof K||":"!==K[4]||"d"!==K[0]&&"D"!==K[0]||"a"!==K[1]&&"A"!==K[1]||"t"!==K[2]&&"T"!==K[2]||"a"!==K[3]&&"A"!==K[3])){var Pd="string"===typeof c.sizes?c.sizes:void 0,Ra=K?K+"\n"+(Pd||""):N,uc=e.preloads.images,Ca=uc.get(Ra);if(Ca){if("high"===c.fetchPriority||10>e.highImagePreloads.size)uc.delete(Ra),
e.highImagePreloads.add(Ca)}else if(!d.imageResources.hasOwnProperty(Ra)){d.imageResources[Ra]=E;var vc=c.crossOrigin;var Qd="string"===typeof vc?"use-credentials"===vc?vc:"":void 0;var ja=e.headers,wc;ja&&0<ja.remainingCapacity&&("high"===c.fetchPriority||500>ja.highImagePreloads.length)&&(wc=Dc(N,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Qd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(ja.remainingCapacity-=
wc.length))?(e.resets.image[Ra]=E,ja.highImagePreloads&&(ja.highImagePreloads+=", "),ja.highImagePreloads+=wc):(Ca=[],T(Ca,{rel:"preload",as:"image",href:K?void 0:N,imageSrcSet:K,imageSizes:Pd,crossOrigin:Qd,integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ca):(e.bulkPreloads.add(Ca),uc.set(Ra,Ca)))}}return kc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return kc(a,
c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;case "head":if(2>f.insertionMode&&null===e.headChunks){e.headChunks=[];var Rd=oc(e.headChunks,c,"head")}else Rd=oc(a,c,"head");return Rd;case "html":if(0===f.insertionMode&&null===e.htmlChunks){e.htmlChunks=[Bc];var Sd=oc(e.htmlChunks,c,"html")}else Sd=oc(a,c,"html");return Sd;default:if(-1!==b.indexOf("-")){a.push(W(b));
var xc=null,Td=null,Sa;for(Sa in c)if(C.call(c,Sa)){var Da=c[Sa];if(null!=Da){var yf=Sa;switch(Sa){case "children":xc=Da;break;case "dangerouslySetInnerHTML":Td=Da;break;case "style":Tb(a,Da);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":break;default:Ha(Sa)&&"function"!==typeof Da&&"symbol"!==typeof Da&&a.push(O,yf,P,D(Da),L)}}}a.push(S);cc(a,Td,xc);return xc}}return oc(a,c,b)}var Ec=new Map;
function mc(a){var b=Ec.get(a);void 0===b&&(b=z("</"+a+">"),Ec.set(a,b));return b}function Fc(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)v(a,b[c]);return c<b.length?(c=b[c],b.length=0,y(a,c)):!0}var Gc=z('<template id="'),Hc=z('"></template>'),Ic=z("\x3c!--$--\x3e"),Jc=z('\x3c!--$?--\x3e<template id="'),Kc=z('"></template>'),Lc=z("\x3c!--$!--\x3e"),Mc=z("\x3c!--/$--\x3e"),Nc=z("<template"),Oc=z('"'),Pc=z(' data-dgst="');z(' data-msg="');z(' data-stck="');var Qc=z("></template>");
function Rc(a,b,c){v(a,Jc);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");v(a,b.boundaryPrefix);v(a,c.toString(16));return y(a,Kc)}
var Sc=z('<div hidden id="'),Tc=z('">'),Uc=z("</div>"),Vc=z('<svg aria-hidden="true" style="display:none" id="'),Wc=z('">'),Xc=z("</svg>"),Yc=z('<math aria-hidden="true" style="display:none" id="'),Zc=z('">'),$c=z("</math>"),ad=z('<table hidden id="'),bd=z('">'),cd=z("</table>"),dd=z('<table hidden><tbody id="'),ed=z('">'),fd=z("</tbody></table>"),gd=z('<table hidden><tr id="'),hd=z('">'),id=z("</tr></table>"),jd=z('<table hidden><colgroup id="'),kd=z('">'),ld=z("</colgroup></table>");
function md(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return v(a,Sc),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,Tc);case 3:return v(a,Vc),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,Wc);case 4:return v(a,Yc),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,Zc);case 5:return v(a,ad),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,bd);case 6:return v(a,dd),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,ed);case 7:return v(a,gd),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,hd);case 8:return v(a,
jd),v(a,b.segmentPrefix),v(a,d.toString(16)),y(a,kd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function nd(a,b){switch(b.insertionMode){case 0:case 1:case 2:return y(a,Uc);case 3:return y(a,Xc);case 4:return y(a,$c);case 5:return y(a,cd);case 6:return y(a,fd);case 7:return y(a,id);case 8:return y(a,ld);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var od=z('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),pd=z('$RS("'),qd=z('","'),rd=z('")\x3c/script>'),sd=z('<template data-rsi="" data-sid="'),Ud=z('" data-pid="'),Vd=z('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
Wd=z('$RC("'),Xd=z('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Yd=z('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Zd=z('$RR("'),$d=z('","'),ae=z('",'),be=z('"'),ce=z(")\x3c/script>"),de=z('<template data-rci="" data-bid="'),ee=z('<template data-rri="" data-bid="'),fe=z('" data-sid="'),ge=z('" data-sty="'),he=z('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),ie=z('$RX("'),je=z('"'),ke=z(","),le=z(")\x3c/script>"),me=z('<template data-rxi="" data-bid="'),ne=z('" data-dgst="'),
oe=z('" data-msg="'),pe=z('" data-stck="'),qe=/[<\u2028\u2029]/g;function re(a){return JSON.stringify(a).replace(qe,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var se=/[&><\u2028\u2029]/g;
function te(a){return JSON.stringify(a).replace(se,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var ue=z('<style media="not all" data-precedence="'),ve=z('" data-href="'),we=z('">'),xe=z("</style>"),ye=!1,ze=!0;function Ae(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){v(this,ue);v(this,a.precedence);for(v(this,ve);d<c.length-1;d++)v(this,c[d]),v(this,Be);v(this,c[d]);v(this,we);for(d=0;d<b.length;d++)v(this,b[d]);ze=y(this,xe);ye=!0;b.length=0;c.length=0}}function Ce(a){return 2!==a.state?ye=!0:!1}
function De(a,b,c){ye=!1;ze=!0;b.styles.forEach(Ae,a);b.stylesheets.forEach(Ce);ye&&(c.stylesToHoist=!0);return ze}function Ee(a){for(var b=0;b<a.length;b++)v(this,a[b]);a.length=0}var Fe=[];function Ge(a){T(Fe,a.props);for(var b=0;b<Fe.length;b++)v(this,Fe[b]);Fe.length=0;a.state=2}var He=z('<style data-precedence="'),Ie=z('" data-href="'),Be=z(" "),Je=z('">'),Ke=z("</style>");
function Le(a){var b=0<a.sheets.size;a.sheets.forEach(Ge,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){v(this,He);v(this,a.precedence);a=0;if(d.length){for(v(this,Ie);a<d.length-1;a++)v(this,d[a]),v(this,Be);v(this,d[a])}v(this,Je);for(a=0;a<c.length;a++)v(this,c[a]);v(this,Ke);c.length=0;d.length=0}}
function Me(a){if(0===a.state){a.state=1;var b=a.props;T(Fe,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Fe.length;a++)v(this,Fe[a]);Fe.length=0}}function Ne(a){a.sheets.forEach(Me,this);a.sheets.clear()}var Oe=z("["),Pe=z(",["),Qe=z(","),Re=z("]");
function Se(a,b){v(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,te(""+d.props.href)),v(a,Re),c=Pe;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,te(""+d.props.href));e=""+e;v(a,Qe);v(a,te(e));for(var g in f)if(C.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ha(g))break a;h=""+h}v(e,Qe);v(e,te(l));v(e,Qe);v(e,te(h))}}}v(a,
Re);c=Pe;d.state=3}});v(a,Re)}
function Te(a,b){v(a,Oe);var c=Oe;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)v(a,c),v(a,D(JSON.stringify(""+d.props.href))),v(a,Re),c=Pe;else{v(a,c);var e=d.props["data-precedence"],f=d.props;v(a,D(JSON.stringify(""+d.props.href)));e=""+e;v(a,Qe);v(a,D(JSON.stringify(e)));for(var g in f)if(C.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!Ha(g))break a;h=""+h}v(e,Qe);v(e,D(JSON.stringify(l)));v(e,Qe);v(e,D(JSON.stringify(h)))}}}v(a,
Re);c=Pe;d.state=3}});v(a,Re)}function Xa(a){var b=Ue();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Ve,We)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],T(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Xe(b)}}}
function Ya(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Ve,We)+">; rel=preconnect";if("string"===typeof b){var l=(""+b).replace(Ye,Ze);h+='; crossorigin="'+l+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],T(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Xe(c)}}}
function Za(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}var m=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(m))return;e.imageResources[m]=E;e=f.headers;var q;e&&0<e.remainingCapacity&&"high"===l&&(q=Dc(a,b,c),2<=(e.remainingCapacity-=q.length))?(f.resets.image[m]=E,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=q):(e=[],T(e,B({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(m,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];T(g,B({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?E:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
T(g,B({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?E:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=E;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(m=Dc(a,b,c),2<=(e.remainingCapacity-=m.length)))f.resets.font[a]=E,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=m;else switch(e=[],
a=B({rel:"preload",href:a,as:b},c),T(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Xe(d)}}}
function $a(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?E:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=E}T(f,B({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Xe(c)}}}
function ab(a,b,c){var d=Ue();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:D(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:B({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&jc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Xe(d))}}}
function bb(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=B({src:a,async:!0},b),f&&(2===f.length&&jc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),nc(a,b),Xe(c))}}}
function cb(a,b){var c=Ue();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=B({src:a,type:"module",async:!0},b),f&&(2===f.length&&jc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),nc(a,b),Xe(c))}}}function jc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Dc(a,b,c){a=(""+a).replace(Ve,We);b=(""+b).replace(Ye,Ze);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)C.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Ye,Ze)+'"'));return b}var Ve=/[<>\r\n]/g;
function We(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Ye=/["';,\r\n]/g;
function Ze(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function $e(a){this.styles.add(a)}function af(a){this.stylesheets.add(a)}
var bf=new ka.AsyncLocalStorage,cf=Symbol.for("react.element"),df=Symbol.for("react.portal"),ef=Symbol.for("react.fragment"),ff=Symbol.for("react.strict_mode"),gf=Symbol.for("react.profiler"),hf=Symbol.for("react.provider"),jf=Symbol.for("react.context"),kf=Symbol.for("react.server_context"),lf=Symbol.for("react.forward_ref"),mf=Symbol.for("react.suspense"),nf=Symbol.for("react.suspense_list"),of=Symbol.for("react.memo"),pf=Symbol.for("react.lazy"),qf=Symbol.for("react.scope"),rf=Symbol.for("react.debug_trace_mode"),
sf=Symbol.for("react.offscreen"),tf=Symbol.for("react.legacy_hidden"),uf=Symbol.for("react.cache"),vf=Symbol.for("react.default_value"),wf=Symbol.iterator,zf=Symbol.for("react.client.reference");
function Af(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===zf?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ef:return"Fragment";case df:return"Portal";case gf:return"Profiler";case ff:return"StrictMode";case mf:return"Suspense";case nf:return"SuspenseList";case uf:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case jf:return(a.displayName||"Context")+".Consumer";case hf:return(a._context.displayName||"Context")+".Provider";case lf:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case of:return b=a.displayName||null,null!==b?b:Af(a.type)||"Memo";case pf:b=a._payload;a=a._init;try{return Af(a(b))}catch(c){}}return null}var Bf;function Cf(a){if(void 0===Bf)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Bf=b&&b[1]||""}return"\n"+Bf+a}var Df=!1;
function Ef(a,b){if(!a||Df)return"";Df=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var p=function(){throw Error();};Object.defineProperty(p.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(p,[])}catch(t){var r=t}Reflect.construct(a,[],p)}else{try{p.call()}catch(t){r=t}a.call(p.prototype)}}else{try{throw Error();}catch(t){r=t}(p=a())&&"function"===typeof p.catch&&
p.catch(function(){})}}catch(t){if(t&&r&&"string"===typeof t.stack)return[t.stack,r.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var l=g.split("\n"),m=h.split("\n");for(e=d=0;d<l.length&&!l[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<m.length&&!m[e].includes("DetermineComponentFrameRoot");)e++;if(d===l.length||e===m.length)for(d=l.length-1,e=m.length-1;1<=d&&0<=e&&l[d]!==m[e];)e--;for(;1<=d&&0<=e;d--,e--)if(l[d]!==m[e]){if(1!==d||1!==e){do if(d--,e--,0>e||l[d]!==m[e]){var q="\n"+l[d].replace(" at new "," at ");a.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",a.displayName));return q}while(1<=d&&0<=e)}break}}}finally{Df=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?Cf(c):""}
var Ff={};function Gf(a,b){a=a.contextTypes;if(!a)return Ff;var c={},d;for(d in a)c[d]=b[d];return c}var Hf=null;function If(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");If(a,c)}b.context._currentValue=b.value}}
function Jf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Jf(a)}function Kf(a){var b=a.parent;null!==b&&Kf(b);a.context._currentValue=a.value}function Lf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?If(a,b):Lf(a,b)}
function Mf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?If(a,c):Mf(a,c);b.context._currentValue=b.value}function Nf(a){var b=Hf;b!==a&&(null===b?Kf(a):null===a?Jf(b):b.depth===a.depth?If(b,a):b.depth>a.depth?Lf(b,a):Mf(b,a),Hf=a)}
var Of={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Pf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Of;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:B({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Of.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=B({},f,h)):B(f,h))}a.state=f}else f.queue=null}
var Qf={id:1,overflow:""};function Rf(a,b,c){var d=a.id;a=a.overflow;var e=32-Sf(d)-1;d&=~(1<<e);c+=1;var f=32-Sf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Sf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Sf=Math.clz32?Math.clz32:Tf,Uf=Math.log,Vf=Math.LN2;function Tf(a){a>>>=0;return 0===a?32:31-(Uf(a)/Vf|0)|0}var Wf=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Xf(){}function Yf(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Xf,Xf),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Zf=b;throw Wf;}}var Zf=null;
function $f(){if(null===Zf)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Zf;Zf=null;return a}function ag(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var bg="function"===typeof Object.is?Object.is:ag,cg=null,dg=null,eg=null,fg=null,gg=null,X=null,hg=!1,ig=!1,jg=0,kg=0,lg=-1,mg=0,ng=null,og=null,pg=0;
function qg(){if(null===cg)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return cg}
function rg(){if(0<pg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function sg(){null===X?null===gg?(hg=!1,gg=X=rg()):(hg=!0,X=gg):null===X.next?(hg=!1,X=X.next=rg()):(hg=!0,X=X.next);return X}function tg(a,b,c,d){for(;ig;)ig=!1,kg=jg=0,lg=-1,mg=0,pg+=1,X=null,c=a(b,d);ug();return c}function vg(){var a=ng;ng=null;return a}function ug(){fg=eg=dg=cg=null;ig=!1;gg=null;pg=0;X=og=null}
function wg(a,b){return"function"===typeof b?b(a):b}function xg(a,b,c){cg=qg();X=sg();if(hg){var d=X.queue;b=d.dispatch;if(null!==og&&(c=og.get(d),void 0!==c)){og.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===wg?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=yg.bind(null,cg,a);return[X.memoizedState,a]}
function zg(a,b){cg=qg();X=sg();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!bg(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}
function yg(a,b,c){if(25<=pg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===cg)if(ig=!0,a={action:c,next:null},null===og&&(og=new Map),c=og.get(b),void 0===c)og.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Ag(){throw Error("startTransition cannot be called during server rendering.");}function Bg(){throw Error("Cannot update optimistic state while rendering.");}
function Cg(a,b,c){if(void 0!==a)return"p"+a;a=JSON.stringify([b,null,c]);b=ca.createHash("md5");b.update(a);return"k"+b.digest("hex")}function Dg(a){var b=mg;mg+=1;null===ng&&(ng=[]);return Yf(ng,a,b)}function Eg(){throw Error("Cache cannot be refreshed during server rendering.");}function Fg(){}
var Hg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Dg(a);if(a.$$typeof===jf||a.$$typeof===kf)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){qg();return a._currentValue},useMemo:zg,useReducer:xg,useRef:function(a){cg=qg();X=sg();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return xg(wg,a)},
useInsertionEffect:Fg,useLayoutEffect:Fg,useCallback:function(a,b){return zg(function(){return a},b)},useImperativeHandle:Fg,useEffect:Fg,useDebugValue:Fg,useDeferredValue:function(a){qg();return a},useTransition:function(){qg();return[!1,Ag]},useId:function(){var a=dg.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Sf(a)-1)).toString(32)+b;var c=Gg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=jg++;a=":"+c.idPrefix+"R"+a;0<b&&
(a+="H"+b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Eg},useHostTransitionStatus:function(){qg();return Va},useOptimistic:function(a){qg();return[a,Bg]},useFormState:function(a,b,c){qg();var d=kg++,e=eg;if("function"===typeof a.$$FORM_ACTION){var f=null,g=fg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;
if(null!==e&&"function"===typeof h){var l=e[1];h.call(a,e[2],e[3])&&(f=Cg(c,g,d),l===f&&(lg=d,b=e[0]))}var m=a.bind(null,b);a=function(p){m(p)};"function"===typeof m.$$FORM_ACTION&&(a.$$FORM_ACTION=function(p){p=m.$$FORM_ACTION(p);void 0!==c&&(c+="",p.action=c);var r=p.data;r&&(null===f&&(f=Cg(c,g,d)),r.append("$ACTION_KEY",f));return p});return[b,a]}var q=a.bind(null,b);return[b,function(p){q(p)}]}},Gg=null,Ig={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");
}},Jg=Ua.ReactCurrentDispatcher,Kg=Ua.ReactCurrentCache;function Lg(a){console.error(a);return null}function Mg(){}var Ng=null;function Ue(){if(Ng)return Ng;var a=bf.getStore();return a?a:null}function Og(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return Pg(a)}))}
function Qg(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,resources:{styles:new Set,stylesheets:new Set},trackedContentKeyPath:null,trackedFallbackNode:null}}
function Rg(a,b,c,d,e,f,g,h,l,m,q,p,r){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var t={replay:null,node:c,childIndex:d,ping:function(){return Og(a,t)},blockedBoundary:e,blockedSegment:f,abortSet:g,keyPath:h,formatContext:l,legacyContext:m,context:q,treeContext:p,componentStack:r,thenableState:b};g.add(t);return t}
function Sg(a,b,c,d,e,f,g,h,l,m,q,p,r){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var t={replay:c,node:d,childIndex:e,ping:function(){return Og(a,t)},blockedBoundary:f,blockedSegment:null,abortSet:g,keyPath:h,formatContext:l,legacyContext:m,context:q,treeContext:p,componentStack:r,thenableState:b};g.add(t);return t}
function Tg(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function Ug(a,b){return{tag:0,parent:a.componentStack,type:b}}
function Vg(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=Cf(b.type,null,null);break;case 1:a+=Ef(b.type,!1);break;case 2:a+=Ef(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function Y(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function Wg(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Xg(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((Af(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=B({},c,d)}b.legacyContext=e;Z(a,b,null,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,null,f,-1),b.keyPath=e}
function Yg(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var m=0;m<f;m++)m===g?l.push(gc):l.push(hc)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Rf(c,1,0),Zg(a,b,d,-1),b.treeContext=c):h?Zg(a,b,d,-1):Z(a,b,null,d,-1);b.keyPath=f}function $g(a,b){if(a&&a.defaultProps){b=B({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function ah(a,b,c,d,e,f,g){if("function"===typeof e)if(e.prototype&&e.prototype.isReactComponent){d=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:e};g=Gf(e,b.legacyContext);var h=e.contextType;h=new e(f,"object"===typeof h&&null!==h?h._currentValue:g);Pf(h,e,f,g);Xg(a,b,c,h,e);b.componentStack=d}else{g=Gf(e,b.legacyContext);h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e};cg={};dg=b;eg=a;fg=c;kg=jg=0;lg=-1;mg=0;ng=d;d=e(f,g);d=tg(e,f,d,g);var l=0!==
jg,m=kg,q=lg;"object"===typeof d&&null!==d&&"function"===typeof d.render&&void 0===d.$$typeof?(Pf(d,e,f,g),Xg(a,b,c,d,e)):Yg(a,b,c,d,l,m,q);b.componentStack=h}else if("string"===typeof e){d=b.componentStack;b.componentStack=Ug(b,e);g=b.blockedSegment;if(null===g)g=f.children,h=b.formatContext,l=b.keyPath,b.formatContext=Mb(h,e,f),b.keyPath=c,Zg(a,b,g,-1),b.formatContext=h,b.keyPath=l;else{l=Cc(g.chunks,e,f,a.resumableState,a.renderState,b.formatContext,g.lastPushedText);g.lastPushedText=!1;h=b.formatContext;
m=b.keyPath;b.formatContext=Mb(h,e,f);b.keyPath=c;Zg(a,b,l,-1);b.formatContext=h;b.keyPath=m;a:{c=g.chunks;a=a.resumableState;switch(e){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(mc(e))}g.lastPushedText=
!1}b.componentStack=d}else{switch(e){case tf:case rf:case ff:case gf:case ef:e=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=e;return;case sf:"hidden"!==f.mode&&(e=b.keyPath,b.keyPath=c,Z(a,b,null,f.children,-1),b.keyPath=e);return;case nf:e=b.componentStack;b.componentStack=Ug(b,"SuspenseList");d=b.keyPath;b.keyPath=c;Z(a,b,null,f.children,-1);b.keyPath=d;b.componentStack=e;return;case qf:throw Error("ReactDOMServer does not yet support scope components.");case mf:a:if(null!==b.replay){e=
b.keyPath;b.keyPath=c;c=f.children;try{Zg(a,b,c,-1)}finally{b.keyPath=e}}else{var p=b.componentStack;e=b.componentStack=Ug(b,"Suspense");var r=b.keyPath;d=b.blockedBoundary;var t=b.blockedSegment;g=f.fallback;var A=f.children;f=new Set;m=Qg(a,f);null!==a.trackedPostpones&&(m.trackedContentKeyPath=c);q=Tg(a,t.chunks.length,m,b.formatContext,!1,!1);t.children.push(q);t.lastPushedText=!1;var F=Tg(a,0,null,b.formatContext,!1,!1);F.parentFlushed=!0;b.blockedBoundary=m;b.blockedSegment=F;a.renderState.boundaryResources=
m.resources;b.keyPath=c;try{if(Zg(a,b,A,-1),F.lastPushedText&&F.textEmbedded&&F.chunks.push(Nb),F.status=1,bh(m,F),0===m.pendingTasks&&0===m.status){m.status=1;b.componentStack=p;break a}}catch(u){F.status=4,m.status=4,h=Vg(a,b.componentStack),l=Y(a,u,h),m.errorDigest=l,ch(a,m)}finally{a.renderState.boundaryResources=d?d.resources:null,b.blockedBoundary=d,b.blockedSegment=t,b.keyPath=r,b.componentStack=p}h=[c[0],"Suspense Fallback",c[2]];l=a.trackedPostpones;null!==l&&(p=[h[1],h[2],[],null],l.workingMap.set(h,
p),5===m.status?l.workingMap.get(c)[4]=p:m.trackedFallbackNode=p);b=Rg(a,null,g,-1,d,q,f,h,b.formatContext,b.legacyContext,b.context,b.treeContext,e);a.pingedTasks.push(b)}return}if("object"===typeof e&&null!==e)switch(e.$$typeof){case lf:h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:e.render};e=e.render;cg={};dg=b;eg=a;fg=c;kg=jg=0;lg=-1;mg=0;ng=d;d=e(f,g);f=tg(e,f,d,g);Yg(a,b,c,f,0!==jg,kg,lg);b.componentStack=h;return;case of:e=e.type;f=$g(e,f);ah(a,b,c,d,e,f,g);return;
case hf:g=f.children;d=b.keyPath;e=e._context;f=f.value;h=e._currentValue;e._currentValue=f;l=Hf;Hf=f={parent:l,depth:null===l?0:l.depth+1,context:e,parentValue:h,value:f};b.context=f;b.keyPath=c;Z(a,b,null,g,-1);a=Hf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");c=a.parentValue;a.context._currentValue=c===vf?a.context._defaultValue:c;a=Hf=a.parent;b.context=a;b.keyPath=d;return;case jf:f=f.children;f=f(e._currentValue);e=b.keyPath;b.keyPath=c;Z(a,
b,null,f,-1);b.keyPath=e;return;case pf:g=b.componentStack;b.componentStack=Ug(b,"Lazy");h=e._init;e=h(e._payload);f=$g(e,f);ah(a,b,c,d,e,f,void 0);b.componentStack=g;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==e?e:typeof e)+"."));}}
function dh(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Tg(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,Zg(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(bh(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d,e){if(null!==b.replay&&"number"===typeof b.replay.slots)dh(a,b,b.replay.slots,d,e);else{b.node=d;b.childIndex=e;if("object"===typeof d&&null!==d){switch(d.$$typeof){case cf:var f=d.type,g=d.key,h=d.props,l=d.ref,m=Af(f),q=null==g?-1===e?0:e:g;g=[b.keyPath,m,q];if(null!==b.replay)a:{var p=b.replay;e=p.nodes;for(d=0;d<e.length;d++){var r=e[d];if(q===r[1]){if(4===r.length){if(null!==m&&m!==r[0])throw Error("Expected the resume to render <"+r[0]+"> in this slot but instead it rendered <"+
m+">. The tree doesn't match so React will fallback to client rendering.");var t=r[2];m=r[3];r=b.node;b.replay={nodes:t,slots:m,pendingTasks:1};try{ah(a,b,g,c,f,h,l);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(G){if("object"===typeof G&&null!==G&&(G===Wf||"function"===typeof G.then))throw b.node===r&&(b.replay=p),
G;b.replay.pendingTasks--;h=Vg(a,b.componentStack);g=a;a=b.blockedBoundary;c=G;h=Y(g,c,h);eh(g,a,t,m,c,h)}b.replay=p}else{if(f!==mf)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(Af(f)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{p=void 0;c=r[5];f=r[2];l=r[3];m=null===r[4]?[]:r[4][2];r=null===r[4]?null:r[4][3];q=b.componentStack;var A=b.componentStack=Ug(b,"Suspense"),F=b.keyPath,u=b.replay,x=b.blockedBoundary,
da=h.children;h=h.fallback;var U=new Set,w=Qg(a,U);w.parentFlushed=!0;w.rootSegmentID=c;b.blockedBoundary=w;b.replay={nodes:f,slots:l,pendingTasks:1};a.renderState.boundaryResources=w.resources;try{Zg(a,b,da,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===w.pendingTasks&&0===w.status){w.status=1;a.completedBoundaries.push(w);
break b}}catch(G){w.status=4,t=Vg(a,b.componentStack),p=Y(a,G,t),w.errorDigest=p,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(w)}finally{a.renderState.boundaryResources=x?x.resources:null,b.blockedBoundary=x,b.replay=u,b.keyPath=F,b.componentStack=q}b=Sg(a,null,{nodes:m,slots:r,pendingTasks:0},h,-1,x,U,[g[0],"Suspense Fallback",g[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,A);a.pingedTasks.push(b)}}e.splice(d,1);break a}}}else ah(a,b,g,c,f,h,l);return;case df:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case pf:h=b.componentStack;b.componentStack=Ug(b,"Lazy");g=d._init;d=g(d._payload);b.componentStack=h;Z(a,b,null,d,e);return}if(Ta(d)){fh(a,b,d,e);return}null===d||"object"!==typeof d?h=null:(h=wf&&d[wf]||d["@@iterator"],h="function"===typeof h?h:null);if(h&&(h=h.call(d))){d=h.next();if(!d.done){g=[];do g.push(d.value),d=h.next();while(!d.done);fh(a,b,g,e)}return}if("function"===typeof d.then)return Z(a,b,null,Dg(d),e);if(d.$$typeof===jf||d.$$typeof===kf)return Z(a,b,null,d._currentValue,e);e=Object.prototype.toString.call(d);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===e?"object with keys {"+Object.keys(d).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof d?(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,d,a.renderState,e.lastPushedText))):"number"===typeof d&&(e=b.blockedSegment,null!==e&&(e.lastPushedText=Ob(e.chunks,""+d,a.renderState,e.lastPushedText)))}}
function fh(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{fh(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(p){if("object"===typeof p&&
null!==p&&(p===Wf||"function"===typeof p.then))throw p;b.replay.pendingTasks--;c=Vg(a,b.componentStack);var m=b.blockedBoundary,q=p;c=Y(a,q,c);eh(a,m,d,l,q,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)l=c[d],b.treeContext=Rf(f,g,d),m=h[d],"number"===typeof m?(dh(a,b,m,l,d),delete h[d]):Zg(a,b,l,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Rf(f,g,h),
Zg(a,b,d,h);b.treeContext=f;b.keyPath=e}function ch(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function Zg(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,m=b.componentStack,q=b.blockedSegment;if(null===q)try{return Z(a,b,null,c,d)}catch(t){if(ug(),c=t===Wf?$f():t,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=vg();a=Sg(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=
f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=m;Nf(g);return}}else{var p=q.children.length,r=q.chunks.length;try{return Z(a,b,null,c,d)}catch(t){if(ug(),q.children.length=p,q.chunks.length=r,c=t===Wf?$f():t,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=vg();q=b.blockedSegment;p=Tg(a,q.chunks.length,null,b.formatContext,q.lastPushedText,!0);q.children.push(p);q.lastPushedText=!1;a=Rg(a,d,b.node,b.childIndex,b.blockedBoundary,p,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,
b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=m;Nf(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;Nf(g);throw c;}function gh(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,hh(this,b,a))}
function eh(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)eh(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,m=f,q=Qg(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=m;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var p in d)delete d[p]}}
function ih(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){Y(b,c,d);Wg(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Y(b,c,d),eh(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&jh(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=Vg(b,a.componentStack),a=Y(b,c,a),d.errorDigest=a,ch(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return ih(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&kh(b)}
function lh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var l=h.value.sheets.values(),m=l.next();0<e.remainingCapacity&&!m.done;m=l.next()){var q=m.value,p=q.props,r=p.href,t=q.props,A=Dc(t.href,"style",{crossOrigin:t.crossOrigin,integrity:t.integrity,
nonce:t.nonce,type:t.type,fetchPriority:t.fetchPriority,referrerPolicy:t.referrerPolicy,media:t.media});if(2<=(e.remainingCapacity-=A.length))c.resets.style[r]=E,f&&(f+=", "),f+=A,c.resets.style[r]="string"===typeof p.crossOrigin||"string"===typeof p.integrity?[p.crossOrigin,p.integrity]:E;else break b}}f?d({Link:f}):d({})}}}catch(F){Y(a,F,{})}}function jh(a){null===a.trackedPostpones&&lh(a,!0);a.onShellError=Mg;a=a.onShellReady;a()}
function kh(a){lh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function bh(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&bh(a,c)}else a.completedSegments.push(b)}
function hh(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&jh(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&bh(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(gh,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(bh(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&kh(a)}
function Pg(a){if(2!==a.status){var b=Hf,c=Jg.current;Jg.current=Hg;var d=Kg.current;Kg.current=Ig;var e=Ng;Ng=a;var f=Gg;Gg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],m=a,q=l.blockedBoundary;m.renderState.boundaryResources=q?q.resources:null;var p=l.blockedSegment;if(null===p){var r=m;if(0!==l.replay.pendingTasks){Nf(l.context);try{var t=l.thenableState;l.thenableState=null;Z(r,l,t,l.node,l.childIndex);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);hh(r,l.blockedBoundary,null)}catch(M){ug();var A=M===Wf?$f():M;if("object"===typeof A&&null!==A&&"function"===typeof A.then){var F=l.ping;A.then(F,F);l.thenableState=vg()}else{l.replay.pendingTasks--;l.abortSet.delete(l);var u=Vg(r,l.componentStack);m=void 0;var x=r,da=l.blockedBoundary,U=A,w=l.replay.nodes,G=l.replay.slots;m=Y(x,U,u);eh(x,da,w,G,U,m);r.pendingRootTasks--;0===r.pendingRootTasks&&jh(r);r.allPendingTasks--;0===r.allPendingTasks&&kh(r)}}finally{r.renderState.boundaryResources=
null}}}else if(r=void 0,x=p,0===x.status){Nf(l.context);var H=x.children.length,ea=x.chunks.length;try{var na=l.thenableState;l.thenableState=null;Z(m,l,na,l.node,l.childIndex);x.lastPushedText&&x.textEmbedded&&x.chunks.push(Nb);l.abortSet.delete(l);x.status=1;hh(m,l.blockedBoundary,x)}catch(M){ug();x.children.length=H;x.chunks.length=ea;var V=M===Wf?$f():M;if("object"===typeof V&&null!==V&&"function"===typeof V.then){var fa=l.ping;V.then(fa,fa);l.thenableState=vg()}else{var ha=Vg(m,l.componentStack);
l.abortSet.delete(l);x.status=4;var J=l.blockedBoundary;r=Y(m,V,ha);null===J?Wg(m,V):(J.pendingTasks--,4!==J.status&&(J.status=4,J.errorDigest=r,ch(m,J),J.parentFlushed&&m.clientRenderedBoundaries.push(J)));m.allPendingTasks--;0===m.allPendingTasks&&kh(m)}}finally{m.renderState.boundaryResources=null}}}g.splice(0,h);null!==a.destination&&mh(a,a.destination)}catch(M){Y(a,M,{}),Wg(a,M)}finally{Gg=f,Jg.current=c,Kg.current=d,c===Hg&&Nf(b),Ng=e}}}
function nh(a,b,c){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:var d=c.id;c.lastPushedText=!1;c.textEmbedded=!1;a=a.renderState;v(b,Gc);v(b,a.placeholderPrefix);a=d.toString(16);v(b,a);return y(b,Hc);case 1:c.status=2;var e=!0;d=c.chunks;var f=0;c=c.children;for(var g=0;g<c.length;g++){for(e=c[g];f<e.index;f++)v(b,d[f]);e=oh(a,b,e)}for(;f<d.length-1;f++)v(b,d[f]);f<d.length&&(e=y(b,d[f]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function oh(a,b,c){var d=c.boundary;if(null===d)return nh(a,b,c);d.parentFlushed=!0;if(4===d.status)d=d.errorDigest,y(b,Lc),v(b,Nc),d&&(v(b,Pc),v(b,D(d)),v(b,Oc)),y(b,Qc),nh(a,b,c);else if(1!==d.status)0===d.status&&(d.rootSegmentID=a.nextSegmentId++),0<d.completedSegments.length&&a.partialBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),nh(a,b,c);else if(d.byteSize>a.progressiveChunkSize)d.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(d),Rc(b,a.renderState,d.rootSegmentID),nh(a,b,
c);else{c=d.resources;var e=a.renderState.boundaryResources;e&&(c.styles.forEach($e,e),c.stylesheets.forEach(af,e));y(b,Ic);d=d.completedSegments;if(1!==d.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");oh(a,b,d[0])}return y(b,Mc)}function ph(a,b,c){md(b,a.renderState,c.parentFormatContext,c.id);oh(a,b,c);return nd(b,c.parentFormatContext)}
function qh(a,b,c){a.renderState.boundaryResources=c.resources;for(var d=c.completedSegments,e=0;e<d.length;e++)rh(a,b,c,d[e]);d.length=0;De(b,c.resources,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.resources;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(v(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,v(b,2048<Xd.length?Xd.slice():Xd)):0===(d.instructions&8)?(d.instructions|=8,v(b,Yd)):v(b,Zd):0===(d.instructions&2)?(d.instructions|=
2,v(b,Vd)):v(b,Wd)):f?v(b,ee):v(b,de);d=e.toString(16);v(b,a.boundaryPrefix);v(b,d);g?v(b,$d):v(b,fe);v(b,a.segmentPrefix);v(b,d);f?g?(v(b,ae),Se(b,c)):(v(b,ge),Te(b,c)):g&&v(b,be);d=g?y(b,ce):y(b,rb);return Fc(b,a)&&d}
function rh(a,b,c,d){if(2===d.status)return!0;var e=d.id;if(-1===e){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return ph(a,b,d)}if(e===c.rootSegmentID)return ph(a,b,d);ph(a,b,d);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(v(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,v(b,od)):v(b,pd)):v(b,sd);v(b,a.segmentPrefix);e=e.toString(16);v(b,e);d?v(b,qd):v(b,Ud);v(b,a.placeholderPrefix);v(b,
e);b=d?y(b,rd):y(b,rb);return b}
function mh(a,b){k=new Uint8Array(2048);n=0;qa=!0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var m=e.htmlChunks,q=e.headChunks;f=0;if(m){for(f=0;f<m.length;f++)v(b,m[f]);if(q)for(f=0;f<q.length;f++)v(b,q[f]);
else v(b,W("head")),v(b,S)}else if(q)for(f=0;f<q.length;f++)v(b,q[f]);var p=e.charsetChunks;for(f=0;f<p.length;f++)v(b,p[f]);p.length=0;e.preconnects.forEach(Ee,b);e.preconnects.clear();var r=e.preconnectChunks;for(f=0;f<r.length;f++)v(b,r[f]);r.length=0;e.fontPreloads.forEach(Ee,b);e.fontPreloads.clear();e.highImagePreloads.forEach(Ee,b);e.highImagePreloads.clear();e.styles.forEach(Le,b);var t=e.importMapChunks;for(f=0;f<t.length;f++)v(b,t[f]);t.length=0;e.bootstrapScripts.forEach(Ee,b);e.scripts.forEach(Ee,
b);e.scripts.clear();e.bulkPreloads.forEach(Ee,b);e.bulkPreloads.clear();var A=e.preloadChunks;for(f=0;f<A.length;f++)v(b,A[f]);A.length=0;var F=e.hoistableChunks;for(f=0;f<F.length;f++)v(b,F[f]);F.length=0;m&&null===q&&v(b,mc("head"));oh(a,b,d);a.completedRootSegment=null;Fc(b,a.renderState)}else return;var u=a.renderState;d=0;u.preconnects.forEach(Ee,b);u.preconnects.clear();var x=u.preconnectChunks;for(d=0;d<x.length;d++)v(b,x[d]);x.length=0;u.fontPreloads.forEach(Ee,b);u.fontPreloads.clear();
u.highImagePreloads.forEach(Ee,b);u.highImagePreloads.clear();u.styles.forEach(Ne,b);u.scripts.forEach(Ee,b);u.scripts.clear();u.bulkPreloads.forEach(Ee,b);u.bulkPreloads.clear();var da=u.preloadChunks;for(d=0;d<da.length;d++)v(b,da[d]);da.length=0;var U=u.hoistableChunks;for(d=0;d<U.length;d++)v(b,U[d]);U.length=0;var w=a.clientRenderedBoundaries;for(c=0;c<w.length;c++){var G=w[c];u=b;var H=a.resumableState,ea=a.renderState,na=G.rootSegmentID,V=G.errorDigest,fa=G.errorMessage,ha=G.errorComponentStack,
J=0===H.streamingFormat;J?(v(u,ea.startInlineScript),0===(H.instructions&4)?(H.instructions|=4,v(u,he)):v(u,ie)):v(u,me);v(u,ea.boundaryPrefix);v(u,na.toString(16));J&&v(u,je);if(V||fa||ha)J?(v(u,ke),v(u,re(V||""))):(v(u,ne),v(u,D(V||"")));if(fa||ha)J?(v(u,ke),v(u,re(fa||""))):(v(u,oe),v(u,D(fa||"")));ha&&(J?(v(u,ke),v(u,re(ha))):(v(u,pe),v(u,D(ha))));if(J?!y(u,le):!y(u,rb)){a.destination=null;c++;w.splice(0,c);return}}w.splice(0,c);var M=a.completedBoundaries;for(c=0;c<M.length;c++)if(!qh(a,b,M[c])){a.destination=
null;c++;M.splice(0,c);return}M.splice(0,c);ya(b);k=new Uint8Array(2048);n=0;qa=!0;var ra=a.partialBoundaries;for(c=0;c<ra.length;c++){var sa=ra[c];a:{w=a;G=b;w.renderState.boundaryResources=sa.resources;var ta=sa.completedSegments;for(H=0;H<ta.length;H++)if(!rh(w,G,sa,ta[H])){H++;ta.splice(0,H);var Na=!1;break a}ta.splice(0,H);Na=De(G,sa.resources,w.renderState)}if(!Na){a.destination=null;c++;ra.splice(0,c);return}}ra.splice(0,c);var ia=a.completedBoundaries;for(c=0;c<ia.length;c++)if(!qh(a,b,ia[c])){a.destination=
null;c++;ia.splice(0,c);return}ia.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&v(b,mc("body")),c.hasHtml&&v(b,mc("html")),ya(b),pa(b),b.end(),a.destination=null):(ya(b),pa(b))}}function sh(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return bf.run(a,Pg,a)});null===a.trackedPostpones&&setImmediate(function(){return bf.run(a,th,a)})}
function th(a){lh(a,0===a.pendingRootTasks)}function Xe(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setImmediate(function(){var b=a.destination;b?mh(a,b):a.flushScheduled=!1}))}function uh(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{mh(a,b)}catch(c){Y(a,c,{}),Wg(a,c)}}}
function vh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return ih(e,a,d)});c.clear()}null!==a.destination&&mh(a,a.destination)}catch(e){Y(a,e,{}),Wg(a,e)}}function wh(a,b){return function(){return uh(b,a)}}function xh(a,b){return function(){a.destination=null;vh(a,Error(b))}}
function yh(a,b){var c=b?b.identifierPrefix:void 0;var d=0;void 0!==(b?b.unstable_externalRuntimeSrc:void 0)&&(d=1);c={idPrefix:void 0===c?"":c,nextFormID:0,streamingFormat:d,bootstrapScriptContent:b?b.bootstrapScriptContent:void 0,bootstrapScripts:b?b.bootstrapScripts:void 0,bootstrapModules:b?b.bootstrapModules:void 0,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},
moduleUnknownResources:{},moduleScriptResources:{}};var e=b?b.nonce:void 0,f=b?b.unstable_externalRuntimeSrc:void 0,g=b?b.importMap:void 0;d=b?b.onHeaders:void 0;var h=b?b.maxHeadersLength:void 0,l=void 0===e?sb:z('<script nonce="'+D(e)+'">'),m=c.idPrefix,q=[],p=null,r=c.bootstrapScriptContent,t=c.bootstrapScripts,A=c.bootstrapModules;void 0!==r&&q.push(l,(""+r).replace(Ab,Bb),tb);void 0!==f&&("string"===typeof f?(p={src:f,chunks:[]},nc(p.chunks,{src:f,async:!0,integrity:void 0,nonce:e})):(p={src:f.src,
chunks:[]},nc(p.chunks,{src:f.src,async:!0,integrity:f.integrity,nonce:e})));f=[];void 0!==g&&(f.push(Cb),f.push((""+JSON.stringify(g)).replace(Ab,Bb)),f.push(Lb));g=d?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof h?h:2E3}:null;d={placeholderPrefix:z(m+"P:"),segmentPrefix:z(m+"S:"),boundaryPrefix:z(m+"B:"),startInlineScript:l,htmlChunks:null,headChunks:null,externalRuntimeScript:p,bootstrapChunks:q,onHeaders:d,headers:g,resets:{font:{},dns:{},connect:{default:{},
anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],preconnectChunks:[],importMapChunks:f,preloadChunks:[],hoistableChunks:[],preconnects:new Set,fontPreloads:new Set,highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:e,boundaryResources:null,stylesToHoist:!1};if(void 0!==t)for(l=0;l<t.length;l++)f=t[l],g=p=void 0,h={rel:"preload",as:"script",fetchPriority:"low",
nonce:e},"string"===typeof f?h.href=m=f:(h.href=m=f.src,h.integrity=g="string"===typeof f.integrity?f.integrity:void 0,h.crossOrigin=p="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=c,r=m,f.scriptResources[r]=null,f.moduleScriptResources[r]=null,f=[],T(f,h),d.bootstrapScripts.add(f),q.push(ub,D(m)),e&&q.push(wb,D(e)),"string"===typeof g&&q.push(xb,D(g)),"string"===typeof p&&q.push(yb,D(p)),q.push(zb);if(void 0!==A)for(t=0;t<A.length;t++)h=
A[t],p=m=void 0,g={rel:"modulepreload",fetchPriority:"low",nonce:e},"string"===typeof h?g.href=l=h:(g.href=l=h.src,g.integrity=p="string"===typeof h.integrity?h.integrity:void 0,g.crossOrigin=m="string"===typeof h||null==h.crossOrigin?void 0:"use-credentials"===h.crossOrigin?"use-credentials":""),h=c,f=l,h.scriptResources[f]=null,h.moduleScriptResources[f]=null,h=[],T(h,g),d.bootstrapScripts.add(h),q.push(vb,D(l)),e&&q.push(wb,D(e)),"string"===typeof p&&q.push(xb,D(p)),"string"===typeof m&&q.push(yb,
D(m)),q.push(zb);e=b?b.namespaceURI:void 0;e=I("http://www.w3.org/2000/svg"===e?3:"http://www.w3.org/1998/Math/MathML"===e?4:0,null,0);A=b?b.progressiveChunkSize:void 0;t=b?b.onError:void 0;l=b?b.onAllReady:void 0;m=b?b.onShellReady:void 0;p=b?b.onShellError:void 0;g=b?b.onPostpone:void 0;h=b?b.formState:void 0;Wa.current=qb;b=[];q=new Set;c={destination:null,flushScheduled:!1,resumableState:c,renderState:d,rootFormatContext:e,progressiveChunkSize:void 0===A?12800:A,status:0,fatalError:null,nextSegmentId:0,
allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:q,pingedTasks:b,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===t?Lg:t,onPostpone:void 0===g?Mg:g,onAllReady:void 0===l?Mg:l,onShellReady:void 0===m?Mg:m,onShellError:void 0===p?Mg:p,onFatalError:Mg,formState:void 0===h?null:h};d=Tg(c,0,null,e,!1,!1);d.parentFlushed=!0;a=Rg(c,null,a,-1,null,d,q,null,e,Ff,null,Qf,null);b.push(a);return c}
exports.renderToPipeableStream=function(a,b){var c=yh(a,b),d=!1;sh(c);return{pipe:function(e){if(d)throw Error("React currently only supports piping to one writable stream.");d=!0;lh(c,null===c.trackedPostpones?0===c.pendingRootTasks:null===c.completedRootSegment?0===c.pendingRootTasks:5!==c.completedRootSegment.status);uh(c,e);e.on("drain",wh(e,c));e.on("error",xh(c,"The destination stream errored while writing data."));e.on("close",xh(c,"The destination stream closed early."));return e},abort:function(e){vh(c,
e)}}};exports.version="18.3.0-canary-60a927d04-20240113";

//# sourceMappingURL=react-dom-server.node.production.min.js.map
