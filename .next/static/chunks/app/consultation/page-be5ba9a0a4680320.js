(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[771],{8344:function(e,t,s){Promise.resolve().then(s.bind(s,5528))},5528:function(e,t,s){"use strict";s.r(t);var i=s(3827);s(4090);var n=s(8371);t.default=()=>(0,i.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,i.jsx)("section",{className:"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden",children:(0,i.jsx)("div",{className:"spiritual-container text-center relative z-10",children:(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text",children:"Live Consultation"}),(0,i.jsx)("p",{className:"text-xl text-gray-700 mb-8 max-w-3xl mx-auto",children:"Connect with our expert astrologers for personalized guidance and spiritual insights"})]})})}),(0,i.jsx)("section",{className:"py-16 bg-white",children:(0,i.jsx)("div",{className:"spiritual-container",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4 gradient-text",children:"Book Your Consultation"}),(0,i.jsx)("p",{className:"text-gray-600 mb-8",children:"Choose from our experienced astrologers"}),(0,i.jsx)("button",{className:"spiritual-button text-lg py-3 px-8 sacred-glow",children:"Book Now"})]})})})]})}},function(e){e.O(0,[371,971,69,744],function(){return e(e.s=8344)}),_N_E=e.O()}]);