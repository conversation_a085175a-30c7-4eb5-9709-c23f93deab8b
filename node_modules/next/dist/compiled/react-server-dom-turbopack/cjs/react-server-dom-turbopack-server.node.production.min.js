/*
 React
 react-server-dom-turbopack-server.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var ba=require("util");require("crypto");var ca=require("async_hooks"),da=require("react"),ea=require("react-dom"),l=null,m=0,p=!0;function q(a,b){a=a.write(b);p=p&&a}
function r(a,b){if("string"===typeof b){if(0!==b.length)if(2048<3*b.length)0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,fa.encode(b));else{var d=l;0<m&&(d=l.subarray(m));d=fa.encodeInto(b,d);var c=d.read;m+=d.written;c<b.length&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=fa.encodeInto(b.slice(c),l).written);2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)}}else 0!==b.byteLength&&(2048<b.byteLength?(0<m&&(q(a,l.subarray(0,m)),l=new Uint8Array(2048),m=0),q(a,b)):(d=l.length-m,d<b.byteLength&&
(0===d?q(a,l):(l.set(b.subarray(0,d),m),m+=d,q(a,l),b=b.subarray(d)),l=new Uint8Array(2048),m=0),l.set(b,m),m+=b.byteLength,2048===m&&(q(a,l),l=new Uint8Array(2048),m=0)));return p}var fa=new ba.TextEncoder,t=Symbol.for("react.client.reference"),v=Symbol.for("react.server.reference");function w(a,b,d){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:d}})}var ha=Function.prototype.bind,ia=Array.prototype.slice;
function ja(){var a=ha.apply(this,arguments);if(this.$$typeof===v){var b=ia.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ja}})}return a}
var ka=Promise.prototype,la={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ma={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];
case "__esModule":var d=a.$$id;a.default=w(function(){throw Error("Attempted to call the default export of "+d+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var c=w({},a.$$id,!0),e=new Proxy(c,ma);a.status="fulfilled";a.value=e;return a.then=w(function(f){return Promise.resolve(f(e))},
a.$$id+"#then",!1)}c=a[b];c||(c=w(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");},a.$$id+"#"+b,a.$$async),Object.defineProperty(c,"name",{value:b}),c=a[b]=new Proxy(c,la));return c},getPrototypeOf:function(){return ka},set:function(){throw Error("Cannot assign to a client module from a server module.");
}},ua={prefetchDNS:na,preconnect:oa,preload:pa,preloadModule:qa,preinitStyle:ra,preinitScript:sa,preinitModuleScript:ta};function na(a){if("string"===typeof a&&a){var b=x();if(b){var d=b.hints,c="D|"+a;d.has(c)||(d.add(c),y(b,"D",a))}}}function oa(a,b){if("string"===typeof a){var d=x();if(d){var c=d.hints,e="C|"+(null==b?"null":b)+"|"+a;c.has(e)||(c.add(e),"string"===typeof b?y(d,"C",[a,b]):y(d,"C",a))}}}
function pa(a,b,d){if("string"===typeof a){var c=x();if(c){var e=c.hints,f="L";if("image"===b&&d){var g=d.imageSrcSet,k=d.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(d=A(d))?y(c,"L",[a,b,d]):y(c,"L",[a,b]))}}}function qa(a,b){if("string"===typeof a){var d=x();if(d){var c=d.hints,e="m|"+a;if(!c.has(e))return c.add(e),(b=A(b))?y(d,"m",[a,b]):y(d,"m",a)}}}
function ra(a,b,d){if("string"===typeof a){var c=x();if(c){var e=c.hints,f="S|"+a;if(!e.has(f))return e.add(f),(d=A(d))?y(c,"S",[a,"string"===typeof b?b:0,d]):"string"===typeof b?y(c,"S",[a,b]):y(c,"S",a)}}}function sa(a,b){if("string"===typeof a){var d=x();if(d){var c=d.hints,e="X|"+a;if(!c.has(e))return c.add(e),(b=A(b))?y(d,"X",[a,b]):y(d,"X",a)}}}function ta(a,b){if("string"===typeof a){var d=x();if(d){var c=d.hints,e="M|"+a;if(!c.has(e))return c.add(e),(b=A(b))?y(d,"M",[a,b]):y(d,"M",a)}}}
function A(a){if(null==a)return null;var b=!1,d={},c;for(c in a)null!=a[c]&&(b=!0,d[c]=a[c]);return b?d:null}var va=ea.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,wa=new ca.AsyncLocalStorage,B=Symbol.for("react.element"),xa=Symbol.for("react.fragment"),ya=Symbol.for("react.server_context"),za=Symbol.for("react.forward_ref"),Aa=Symbol.for("react.suspense"),Ba=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),Da=Symbol.for("react.memo_cache_sentinel");
Symbol.for("react.postpone");var Ea=Symbol.iterator,D=null;function E(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var d=b.parent;if(null===a){if(null!==d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===d)throw Error("The stacks must reach the root at the same time. This is a bug in React.");E(a,d);b.context._currentValue=b.value}}}function Fa(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Fa(a)}
function Ga(a){var b=a.parent;null!==b&&Ga(b);a.context._currentValue=a.value}function Ha(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?E(a,b):Ha(a,b)}
function Ia(a,b){var d=b.parent;if(null===d)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===d.depth?E(a,d):Ia(a,d);b.context._currentValue=b.value}var Ja=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Ka(){}function La(a,b,d){d=a[d];void 0===d?a.push(b):d!==b&&(b.then(Ka,Ka),b=d);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(c){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=c}},function(c){if("pending"===b.status){var e=b;e.status="rejected";e.reason=c}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}F=b;throw Ja;}}var F=null;
function Ma(){if(null===F)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=F;F=null;return a}var G=null,H=0,I=null;function Na(){var a=I;I=null;return a}function Oa(a){return a._currentValue}
var Sa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:J,useTransition:J,readContext:Oa,useContext:Oa,useReducer:J,useRef:J,useState:J,useInsertionEffect:J,useLayoutEffect:J,useImperativeHandle:J,useEffect:J,useId:Pa,useSyncExternalStore:J,useCacheRefresh:function(){return Qa},useMemoCache:function(a){for(var b=Array(a),d=0;d<a;d++)b[d]=Da;return b},use:Ra};
function J(){throw Error("This Hook is not supported in Server Components.");}function Qa(){throw Error("Refreshing the cache is not supported in Server Components.");}function Pa(){if(null===G)throw Error("useId can only be used while React is rendering");var a=G.identifierCount++;return":"+G.identifierPrefix+"S"+a.toString(32)+":"}
function Ra(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=H;H+=1;null===I&&(I=[]);return La(I,a,b)}if(a.$$typeof===ya)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));}function Ta(){return(new AbortController).signal}function Ua(){var a=x();return a?a.cache:new Map}
var Va={getCacheSignal:function(){var a=Ua(),b=a.get(Ta);void 0===b&&(b=Ta(),a.set(Ta,b));return b},getCacheForType:function(a){var b=Ua(),d=b.get(a);void 0===d&&(d=a(),b.set(a,d));return d}},Wa=Array.isArray,Xa=Object.getPrototypeOf;function Ya(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,d){return d})}
function Za(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Wa(a))return"[...]";a=Ya(a);return"Object"===a?"{...}":a;case "function":return"function";default:return String(a)}}
function K(a){if("string"===typeof a)return a;switch(a){case Aa:return"Suspense";case Ba:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case za:return K(a.render);case Ca:return K(a.type);case C:var b=a._payload;a=a._init;try{return K(a(b))}catch(d){}}return""}
function L(a,b){var d=Ya(a);if("Object"!==d&&"Array"!==d)return d;d=-1;var c=0;if(Wa(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?L(g):Za(g);""+f===b?(d=e.length,c=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+K(a.type)+"/>";else{e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h="object"===typeof h&&null!==h?L(h):
Za(h);k===b?(d=e.length,c=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<d&&0<c?(a=" ".repeat(d)+"^".repeat(c),"\n  "+e+"\n  "+a):"\n  "+e}var $a=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ab=da.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;if(!ab)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');
var bb=Object.prototype,M=JSON.stringify,cb=ab.ReactCurrentCache,db=$a.ReactCurrentDispatcher;function eb(a){console.error(a)}function fb(){}
function gb(a,b,d,c,e,f){if(null!==cb.current&&cb.current!==Va)throw Error("Currently React only supports one RSC renderer at a time.");va.current=ua;cb.current=Va;var g=new Set;c=[];var k=new Set,h={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:g,pingedTasks:c,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenProviders:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===d?eb:d,onPostpone:void 0===f?fb:f,toJSON:function(n,z){return hb(h,this,n,z)}};h.pendingChunks++;a=N(h,a,null,g);c.push(a);return h}var O=null;function x(){if(O)return O;var a=wa.getStore();return a?a:null}
function ib(a,b){a.pendingChunks++;var d=N(a,null,D,a.abortableTasks);switch(b.status){case "fulfilled":return d.model=b.value,jb(a,d),d.id;case "rejected":var c=P(a,b.reason);Q(a,d.id,c);return d.id;default:"string"!==typeof b.status&&(b.status="pending",b.then(function(e){"pending"===b.status&&(b.status="fulfilled",b.value=e)},function(e){"pending"===b.status&&(b.status="rejected",b.reason=e)}))}b.then(function(e){d.model=e;jb(a,d)},function(e){d.status=4;e=P(a,e);Q(a,d.id,e);a.abortableTasks.delete(d);
null!==a.destination&&R(a,a.destination)});return d.id}function y(a,b,d){d=M(d);var c=a.nextChunkId++;b="H"+b;b=c.toString(16)+":"+b;a.completedHintChunks.push(b+d+"\n");kb(a)}function lb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function mb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:C,_payload:a,_init:lb}}
function S(a,b,d,c,e,f){if(null!==c&&void 0!==c)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof b){if(b.$$typeof===t)return[B,b,d,e];H=0;I=f;e=b(e);return"object"===typeof e&&null!==e&&"function"===typeof e.then?"fulfilled"===e.status?e.value:mb(e):e}if("string"===typeof b)return[B,b,d,e];if("symbol"===typeof b)return b===xa?e.children:[B,b,d,e];if(null!=b&&"object"===typeof b){if(b.$$typeof===t)return[B,b,d,e];switch(b.$$typeof){case C:var g=
b._init;b=g(b._payload);return S(a,b,d,c,e,f);case za:return a=b.render,H=0,I=f,a(e,void 0);case Ca:return S(a,b.type,d,c,e,f)}}throw Error("Unsupported Server Component type: "+Za(b));}function jb(a,b){var d=a.pingedTasks;d.push(b);1===d.length&&(a.flushScheduled=null!==a.destination,setImmediate(function(){return nb(a)}))}function N(a,b,d,c){var e={id:a.nextChunkId++,status:0,model:b,context:d,ping:function(){return jb(a,e)},thenableState:null};c.add(e);return e}
function T(a){return"$"+a.toString(16)}function ob(a,b,d){a=M(d);return b.toString(16)+":"+a+"\n"}
function pb(a,b,d,c){var e=c.$$async?c.$$id+"#async":c.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===d?"$L"+g.toString(16):T(g);try{var k=a.bundlerConfig,h=c.$$id;g="";var n=k[h];if(n)g=n.name;else{var z=h.lastIndexOf("#");-1!==z&&(g=h.slice(z+1),n=k[h.slice(0,z)]);if(!n)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===c.$$async?[n.id,n.chunks,g,1]:[n.id,n.chunks,
g];a.pendingChunks++;var aa=a.nextChunkId++,Jb=M(u),Kb=aa.toString(16)+":I"+Jb+"\n";a.completedImportChunks.push(Kb);f.set(e,aa);return b[0]===B&&"1"===d?"$L"+aa.toString(16):T(aa)}catch(Lb){return a.pendingChunks++,b=a.nextChunkId++,d=P(a,Lb),Q(a,b,d),T(b)}}function U(a,b){a.pendingChunks++;b=N(a,b,D,a.abortableTasks);qb(a,b);return b.id}var V=!1;
function hb(a,b,d,c){switch(c){case B:return"$"}for(;"object"===typeof c&&null!==c&&(c.$$typeof===B||c.$$typeof===C);)try{switch(c.$$typeof){case B:var e=a.writtenObjects,f=e.get(c);if(void 0!==f){if(-1===f){var g=U(a,c);return T(g)}if(V===c)V=null;else return T(f)}else e.set(c,-1);var k=c;c=S(a,k.type,k.key,k.ref,k.props,null);break;case C:var h=c._init;c=h(c._payload)}}catch(n){b=n===Ja?Ma():n;if("object"===typeof b&&null!==b&&"function"===typeof b.then)return a.pendingChunks++,a=N(a,c,D,a.abortableTasks),
c=a.ping,b.then(c,c),a.thenableState=Na(),"$L"+a.id.toString(16);a.pendingChunks++;c=a.nextChunkId++;b=P(a,b);Q(a,c,b);return"$L"+c.toString(16)}if(null===c)return null;if("object"===typeof c){if(c.$$typeof===t)return pb(a,b,d,c);b=a.writtenObjects;d=b.get(c);if("function"===typeof c.then){if(void 0!==d)if(V===c)V=null;else return"$@"+d.toString(16);a=ib(a,c);b.set(c,a);return"$@"+a.toString(16)}if(void 0!==d){if(-1===d)return a=U(a,c),T(a);if(V===c)V=null;else return T(d)}else b.set(c,-1);if(Wa(c))return c;
if(c instanceof Map){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b][0],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$Q"+U(a,c).toString(16)}if(c instanceof Set){c=Array.from(c);for(b=0;b<c.length;b++)d=c[b],"object"===typeof d&&null!==d&&(e=a.writtenObjects,void 0===e.get(d)&&e.set(d,-1));return"$W"+U(a,c).toString(16)}null===c||"object"!==typeof c?a=null:(a=Ea&&c[Ea]||c["@@iterator"],a="function"===typeof a?a:null);if(a)return Array.from(c);a=Xa(c);if(a!==
bb&&(null===a||null!==Xa(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return c}if("string"===typeof c){if("Z"===c[c.length-1]&&b[d]instanceof Date)return"$D"+c;if(1024<=c.length)return a.pendingChunks+=2,b=a.nextChunkId++,d="string"===typeof c?Buffer.byteLength(c,"utf8"):c.byteLength,d=b.toString(16)+":T"+d.toString(16)+",",a.completedRegularChunks.push(d,c),T(b);a="$"===c[0]?"$"+
c:c;return a}if("boolean"===typeof c)return c;if("number"===typeof c)return a=c,Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN";if("undefined"===typeof c)return"$undefined";if("function"===typeof c){if(c.$$typeof===t)return pb(a,b,d,c);if(c.$$typeof===v)return b=a.writtenServerReferences,d=b.get(c),void 0!==d?a="$F"+d.toString(16):(d=c.$$bound,d={id:c.$$id,bound:d?Promise.resolve(d):null},a=U(a,d),b.set(c,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+
L(b,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server".'+L(b,d));}if("symbol"===typeof c){e=a.writtenSymbols;f=e.get(c);if(void 0!==f)return T(f);f=c.description;if(Symbol.for(f)!==c)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(c.description+") cannot be found among global symbols.")+
L(b,d));a.pendingChunks++;b=a.nextChunkId++;d=ob(a,b,"$S"+f);a.completedImportChunks.push(d);e.set(c,b);return T(b)}if("bigint"===typeof c)return"$n"+c.toString(10);throw Error("Type "+typeof c+" is not supported in Client Component props."+L(b,d));}
function P(a,b){a=a.onError;b=a(b);if(null!=b&&"string"!==typeof b)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof b+'" instead');return b||""}function rb(a,b){null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}
function Q(a,b,d){d={digest:d};b=b.toString(16)+":E"+M(d)+"\n";a.completedErrorChunks.push(b)}
function qb(a,b){if(0===b.status){var d=D,c=b.context;d!==c&&(null===d?Ga(c):null===c?Fa(d):d.depth===c.depth?E(d,c):d.depth>c.depth?Ha(d,c):Ia(d,c),D=c);try{var e=b.model;if("object"===typeof e&&null!==e&&e.$$typeof===B){a.writtenObjects.set(e,b.id);d=e;var f=b.thenableState;b.model=e;e=S(a,d.type,d.key,d.ref,d.props,f);for(b.thenableState=null;"object"===typeof e&&null!==e&&e.$$typeof===B;)a.writtenObjects.set(e,b.id),f=e,b.model=e,e=S(a,f.type,f.key,f.ref,f.props,null)}"object"===typeof e&&null!==
e&&a.writtenObjects.set(e,b.id);var g=b.id;V=e;var k=M(e,a.toJSON),h=g.toString(16)+":"+k+"\n";a.completedRegularChunks.push(h);a.abortableTasks.delete(b);b.status=1}catch(n){g=n===Ja?Ma():n,"object"===typeof g&&null!==g&&"function"===typeof g.then?(a=b.ping,g.then(a,a),b.thenableState=Na()):(a.abortableTasks.delete(b),b.status=4,g=P(a,g),Q(a,b.id,g))}}}
function nb(a){var b=db.current;db.current=Sa;var d=O;G=O=a;try{var c=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<c.length;e++)qb(a,c[e]);null!==a.destination&&R(a,a.destination)}catch(f){P(a,f),rb(a,f)}finally{db.current=b,G=null,O=d}}
function R(a,b){l=new Uint8Array(2048);m=0;p=!0;try{for(var d=a.completedImportChunks,c=0;c<d.length;c++)if(a.pendingChunks--,!r(b,d[c])){a.destination=null;c++;break}d.splice(0,c);var e=a.completedHintChunks;for(c=0;c<e.length;c++)if(!r(b,e[c])){a.destination=null;c++;break}e.splice(0,c);var f=a.completedRegularChunks;for(c=0;c<f.length;c++)if(a.pendingChunks--,!r(b,f[c])){a.destination=null;c++;break}f.splice(0,c);var g=a.completedErrorChunks;for(c=0;c<g.length;c++)if(a.pendingChunks--,!r(b,g[c])){a.destination=
null;c++;break}g.splice(0,c)}finally{a.flushScheduled=!1,l&&0<m&&b.write(l.subarray(0,m)),l=null,m=0,p=!0}"function"===typeof b.flush&&b.flush();0===a.pendingChunks&&b.end()}function sb(a){a.flushScheduled=null!==a.destination;setImmediate(function(){return wa.run(a,nb,a)})}function kb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setImmediate(function(){return R(a,b)})}}
function tb(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{R(a,b)}catch(d){P(a,d),rb(a,d)}}}
function ub(a,b){try{var d=a.abortableTasks;if(0<d.size){a.pendingChunks++;var c=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=P(a,e);Q(a,c,f,e);d.forEach(function(g){g.status=3;var k=T(c);g=ob(a,g.id,k);a.completedErrorChunks.push(g)});d.clear()}null!==a.destination&&R(a,a.destination)}catch(g){P(a,g),rb(a,g)}}
function vb(a,b){var d="",c=a[b];if(c)d=c.name;else{var e=b.lastIndexOf("#");-1!==e&&(d=b.slice(e+1),c=a[b.slice(0,e)]);if(!c)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[c.id,c.chunks,d]}var wb=new Map;
function xb(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(d){b.status="fulfilled";b.value=d},function(d){b.status="rejected";b.reason=d});return b}function yb(){}
function zb(a){for(var b=a[1],d=[],c=0;c<b.length;c++){var e=b[c],f=wb.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);d.push(f);var g=wb.set.bind(wb,e,null);f.then(g,yb);wb.set(e,f)}else null!==f&&d.push(f)}return 4===a.length?0===d.length?xb(a[0]):Promise.all(d).then(function(){return xb(a[0])}):0<d.length?Promise.all(d):null}
function W(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function Ab(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}Ab.prototype=Object.create(Promise.prototype);
Ab.prototype.then=function(a,b){switch(this.status){case "resolved_model":Bb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Cb(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}
function Db(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&Cb(d,b)}}function Eb(a,b,d,c,e,f){var g=vb(a._bundlerConfig,b);a=zb(g);if(d)d=Promise.all([d,a]).then(function(k){k=k[0];var h=W(g);return h.bind.apply(h,[null].concat(k))});else if(a)d=Promise.resolve(a).then(function(){return W(g)});else return W(g);d.then(Fb(c,e,f),Gb(c));return null}var X=null,Y=null;
function Bb(a){var b=X,d=Y;X=a;Y=null;try{var c=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=c,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=c)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=d}}function Hb(a,b){a._chunks.forEach(function(d){"pending"===d.status&&Db(d,b)})}
function Z(a,b){var d=a._chunks,c=d.get(b);c||(c=a._formData.get(a._prefix+b),c=null!=c?new Ab("resolved_model",c,null,a):new Ab("pending",null,null,a),d.set(b,c));return c}function Fb(a,b,d){if(Y){var c=Y;c.deps++}else c=Y={deps:1,value:null};return function(e){b[d]=e;c.deps--;0===c.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=c.value,null!==e&&Cb(e,c.value))}}function Gb(a){return function(b){return Db(a,b)}}
function Ib(a,b){a=Z(a,b);"resolved_model"===a.status&&Bb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Mb(a,b,d,c){if("$"===c[0])switch(c[1]){case "$":return c.slice(1);case "@":return b=parseInt(c.slice(2),16),Z(a,b);case "S":return Symbol.for(c.slice(2));case "F":return c=parseInt(c.slice(2),16),c=Ib(a,c),Eb(a,c.id,c.bound,X,b,d);case "Q":return b=parseInt(c.slice(2),16),a=Ib(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=Ib(a,b),new Set(a);case "K":b=c.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=Z(a,c);switch(a.status){case "resolved_model":Bb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return c=X,a.then(Fb(c,b,d),Gb(c)),null;default:throw a.reason;}}return c}
function Nb(a,b){var d=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,c=new Map,e={_bundlerConfig:a,_prefix:b,_formData:d,_chunks:c,_fromJSON:function(f,g){return"string"===typeof g?Mb(e,this,f,g):g}};return e}
function Ob(a,b,d){a._formData.append(b,d);var c=a._prefix;if(b.startsWith(c)&&(a=a._chunks,b=+b.slice(c.length),(b=a.get(b))&&"pending"===b.status&&(c=b.value,a=b.reason,b.status="resolved_model",b.value=d,null!==c)))switch(Bb(b),b.status){case "fulfilled":Cb(c,b.value);break;case "pending":case "blocked":b.value=c;b.reason=a;break;case "rejected":a&&Cb(a,b.reason)}}function Pb(a){Hb(a,Error("Connection closed."))}
function Qb(a,b,d){var c=vb(a,b);a=zb(c);return d?Promise.all([d,a]).then(function(e){e=e[0];var f=W(c);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return W(c)}):Promise.resolve(W(c))}function Rb(a,b,d){a=Nb(b,d,a);Pb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}function Sb(a,b){return function(){return tb(b,a)}}exports.createClientModuleProxy=function(a){a=w({},a,!1);return new Proxy(a,ma)};
exports.decodeAction=function(a,b){var d=new FormData,c=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Rb(a,b,e),c=Qb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),c=Qb(b,e,null)):d.append(f,e)});return null===c?null:c.then(function(e){return e.bind(null,d)})};exports.decodeReply=function(a,b){if("string"===typeof a){var d=new FormData;d.append("0",a);a=d}a=Nb(b,"",a);b=Z(a,0);Pb(a);return b};
exports.decodeReplyFromBusboy=function(a,b){var d=Nb(b,""),c=0,e=[];a.on("field",function(f,g){0<c?e.push(f,g):Ob(d,f,g)});a.on("file",function(f,g,k){var h=k.filename,n=k.mimeType;if("base64"===k.encoding.toLowerCase())throw Error("React doesn't accept base64 encoded file uploads because we don't expect form data passed from a browser to ever encode data that way. If that's the wrong assumption, we can easily fix it.");c++;var z=[];g.on("data",function(u){z.push(u)});g.on("end",function(){var u=
new Blob(z,{type:n});d._formData.append(f,u,h);c--;if(0===c){for(u=0;u<e.length;u+=2)Ob(d,e[u],e[u+1]);e.length=0}})});a.on("finish",function(){Pb(d)});a.on("error",function(f){Hb(d,f)});return Z(d,0)};exports.registerClientReference=function(a,b,d){return w(a,b+"#"+d,!1)};exports.registerServerReference=function(a,b,d){return Object.defineProperties(a,{$$typeof:{value:v},$$id:{value:null===d?b:b+"#"+d},$$bound:{value:null},bind:{value:ja}})};
exports.renderToPipeableStream=function(a,b,d){var c=gb(a,b,d?d.onError:void 0,d?d.context:void 0,d?d.identifierPrefix:void 0,d?d.onPostpone:void 0),e=!1;sb(c);return{pipe:function(f){if(e)throw Error("React currently only supports piping to one writable stream.");e=!0;tb(c,f);f.on("drain",Sb(f,c));return f},abort:function(f){ub(c,f)}}};

//# sourceMappingURL=react-server-dom-turbopack-server.node.production.min.js.map
